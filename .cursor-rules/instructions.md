# Cursor Rules for Productivity (Nuxt 3 + Vue 3 + TypeScript + shadcn-nuxt + Tailwind + Drizzle + Zod + VueUse + Pinia)

## What are Cursor Rules?
Cursor rules are project-specific instructions that guide AI code generation, refactoring, and completions in the Cursor editor. They help enforce your team's conventions, best practices, and stack-specific patterns, so the AI writes code the way you want—every time.

## How to Use
- Place your `.cursorrules` or `.cursorrules.json` file in the project root.
- Optionally, add a `.cursor-rules/` folder for documentation, examples, and advanced rules.
- Cursor will automatically use these rules to guide code suggestions, completions, and refactors.

## Key Rules for This Project
- Use `<script setup lang="ts">` and the Composition API for all Vue components. Avoid Options API.
- Use TypeScript everywhere. Prefer `types` over `interfaces` unless you need extension.
- Avoid enums; use const objects or maps.
- Use PascalCase for component files, camelCase for composables, lowercase-with-dashes for directories.
- Use composables (`useXxx`) for shared logic. Place in `app/composables/`.
- Favor functional programming and early returns. Avoid classes.
- Always use Tailwind CSS for styling. No inline styles or CSS files except for Tailwind entry.
- Use shadcn-nuxt UI components for all UI primitives. Prefer composition over inheritance.
- Use Zod for all schema validation, both client and server. Share schemas where possible.
- Use Pinia for state management if global state is needed.
- Use VueUse for utility composables (e.g., useVModel, useFetch, etc).
- Use Drizzle ORM for all DB access. Prefer type-safe queries and migrations.
- Use `$fetch` or `useFetch` for API calls. Always type responses.
- Use `useHead` and `useSeoMeta` for SEO/meta tags.
- Use Nuxt's file-based routing and directory conventions (`pages/`, `components/`, `composables/`, `plugins/`, `server/api/`).
- Use `defineProps`, `defineEmits`, and `defineExpose` for component props/events/expose.
- Use `definePageMeta` for per-page meta config.
- Use Nuxt's `runtimeConfig` for environment variables.
- Use Nuxt's auto-imports for composables and components. Don't import manually if not needed.
- Use `<ClientOnly>` for client-only components (e.g., Toaster).
- Use `<NuxtImage>` or `<NuxtPicture>` for images. Optimize with WebP, lazy loading, and size data.
- Use class-variance-authority (CVA) for managing Tailwind variants in UI components.
- Use descriptive variable names with auxiliary verbs (`isLoading`, `hasError`, etc).
- Write modular, DRY, and readable code. Avoid duplication.
- Add JSDoc comments for all exported functions, composables, and complex logic.
- Write unit tests for composables and critical logic. Use Jest or Vitest.
- Always handle errors and edge cases. Use early returns and guard clauses.
- Use `.env` for secrets and runtime config. Never hardcode secrets.
- Use Prettier and ESLint for formatting and linting. Follow project config.

## Advanced
- You can add more markdown files in `.cursor-rules/` for team-specific conventions, code examples, or onboarding notes.
- See the `.cursorrules` file in the project root for the full machine-readable rules.

---

**For more, see: [Cursor Rules Guide](https://www.cursorrules.org/article) and [Awesome CursorRules](https://github.com/PatrickJS/awesome-cursorrules)** 