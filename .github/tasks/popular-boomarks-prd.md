# Popular Bookmarks Feature - PRD

## Overview
A social bookmarking site needs a page to present its best content. The Popular page displays the most valuable bookmarks across different time periods: today, this week, this month, this year, and all time.

## Implementation Status: ✅ COMPLETED

## Feature Components

### 1. Popularity Scoring Algorithm
We've implemented a weighted formula that combines the following metrics:

- **Save Count (35%)**: Total number of bookmarks for a URL (logarithmically scaled to prevent dominance of high-count items)
- **Save Velocity (25%)**: Rate of new bookmarks in the last 7 days
- **Freshness (20%)**: How recently the URL was first bookmarked (using exponential decay)
- **User Diversity (10%)**: Ratio of unique users to total saves
- **Engagement (10%)**: Based on visit counts and reading completion

The formula gives more weight to recent saves and bookmarks with a higher number of unique users, while preventing any single metric from dominating the score.

### 2. Data Storage and Processing
- Data is calculated and stored in a PostgreSQL materialized view (`popular_urls`)
- The view is refreshed hourly via a scheduled task
- Implemented using Drizzle ORM with PostgreSQL best practices
- View definition is stored in `server/db/views/popular/popular-urls.ts`
- Includes fallback queries in case the materialized view is unavailable

### 3. Time Period Specific Scoring
Different scoring weights are applied for different time periods:
- **Daily**: Higher emphasis on recent activity (50% save count, 50% recent saves)
- **Weekly**: Balanced between recency and popularity (40% save count, 60% recent saves)
- **Monthly**: More weight on overall popularity (60% save count, 40% recent saves)
- **Yearly**: Strong emphasis on overall popularity (80% save count, 20% recent saves)
- **All Time**: Uses the full weighted formula described above

### 4. API Endpoints
- `/api/popular` - Get popular URLs for all time
- `/api/popular/[period]` - Get popular URLs for a specific period (today, week, month, year)
- `/api/tags/[tagName]/popular` - Get popular URLs for a specific tag
- `/api/admin/refresh-views` - Admin endpoint to manually refresh the materialized view

### 5. User Interface
- Clean, responsive design showing popular bookmarks
- Time period selector (All Time, Today, This Week, This Month, This Year)
- Each bookmark displays:
  - Title and URL
  - Save count and unique user count
  - Associated tags
  - First saved date
  - Favicon for visual recognition
- Pagination with "Load More" functionality
- Proper handling of loading states and empty results

### 6. Content Filtering
- NSFW content is filtered by default (can be toggled via API parameter)
- Private bookmarks are excluded from popularity calculations
- URLs must have at least one save to appear in results

## Technical Implementation
- Materialized view for efficient querying of popularity data
- Indexes on all score columns for optimal performance
- Hourly refresh schedule to keep data current
- Fallback query mechanism if the materialized view is unavailable
- Proper error handling throughout the stack

## Future Enhancements

### Phase 1: Content-Aware Algorithm Improvements (Next Sprint)
**Priority: HIGH - Implement before wider release**

#### 1. Content-Type Aware Time Decay
Current implementation uses uniform decay rates. Need different rates for content types:
```sql
-- Implementation: Add to materialized view
CASE 
  WHEN tags && ARRAY['tools', 'reference', 'documentation', 'library'] 
    THEN EXP(-0.02 * EXTRACT(DAY FROM NOW() - bc.first_saved_at))  -- Slower decay
  WHEN tags && ARRAY['news', 'trending', 'announcement'] 
    THEN EXP(-0.08 * EXTRACT(DAY FROM NOW() - bc.first_saved_at))  -- Faster decay
  ELSE EXP(-0.05 * EXTRACT(DAY FROM NOW() - bc.first_saved_at))     -- Default decay
END AS content_aware_freshness
```

**Rationale**: Reference materials should maintain long-term visibility, while trending content should decay faster to promote fresh discoveries.

#### 2. Gaming Protection - Account Maturity Weighting
Add user account age and behavior scoring to prevent sockpuppet manipulation:
```sql
-- Add to engagement calculation
(0.15 * (
  SELECT AVG(GREATEST(EXTRACT(DAY FROM NOW() - u.created_at), 1)) 
  FROM bookmark b2 
  JOIN user u ON b2.user_id = u.id 
  WHERE b2.link_id = bc.link_id
)) AS account_maturity_score
```

**Rationale**: New accounts should have reduced influence until they establish legitimate usage patterns.

### Phase 2: Advanced Trending Detection (Month 2)
**Priority: MEDIUM**

#### 1. Velocity-Based Trending
Implement rate-of-change detection for emerging popular content:
```sql
-- Trending multiplier based on recent velocity vs baseline
CASE WHEN bc.save_count > 5 
  THEN (rs.recent_save_count::float / 7.0) / (bc.save_count::float / GREATEST(EXTRACT(DAY FROM NOW() - bc.first_saved_at), 1))
  ELSE 1.0
END AS velocity_multiplier
```

#### 2. Cold Start Boosting
New content with high-quality early signals gets temporary boost:
```sql
-- Boost new content with good early indicators
CASE WHEN EXTRACT(HOUR FROM NOW() - bc.first_saved_at) < 24 
     AND bc.unique_users >= 3 
     AND (bc.unique_users::float / bc.save_count) > 0.7
  THEN 1.5
  ELSE 1.0
END AS cold_start_boost
```

### Phase 3: Machine Learning Integration (Month 3+)
**Priority: LOW - Future Enhancement**

#### 1. Personalized Popularity Scoring
- Use current signals as ML features rather than hardcoded weights
- Learn user preference patterns for novelty vs popularity balance
- Implement cohort-based parameter optimization

#### 2. Advanced Gaming Detection
- ML-based behavioral analysis for bot detection
- Network analysis for coordinated voting patterns
- Anomaly detection for unusual engagement patterns

### Technical Debt & Monitoring
**Priority: HIGH - Implement alongside Phase 1**

#### 1. Algorithm Performance Monitoring
- Track engagement metrics by algorithm version
- A/B testing infrastructure for safe algorithm iterations
- Automated alerts for ranking quality degradation

#### 2. Content Distribution Analysis
- Monitor for filter bubbles and echo chambers
- Track content diversity across time periods
- Measure creator satisfaction and fair exposure

#### 3. Scalability Improvements
- Incremental materialized view updates based on activity thresholds
- Redis caching for hot rankings
- Separate real-time trending from batch quality scoring

### Implementation Notes
- **Start Simple**: Implement Phase 1 improvements first, validate with real usage data
- **Measure Everything**: Each change should be A/B tested with clear success metrics
- **Iterative Approach**: Don't implement all improvements simultaneously
- **User Feedback**: Monitor community response to ranking changes

### Research-Based Enhancements (Long-term)
- User-specific popular content based on followed tags or users
- Cross-platform signal integration (external mentions, shares)
- Wilson Score confidence intervals for sparse data handling
- Network effect integration with follower influence weighting

oren's research:
<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

## Summary of Social Media Ranking Algorithms

The referenced article exposes and explains the core algorithms behind how several major social bookmarking and news aggregation sites-Reddit, StumbleUpon, Del.icio.us, and Hacker News-rank and promote user-submitted content. Below is a concise breakdown of each platform's approach[^2][^1]:

### Hacker News

**Formula:**

$$
\frac{p - 1}{(t + 2)^{1.5}}
$$

- **p**: Number of user votes (points), subtracting 1 to remove the submitter's own vote.
- **t**: Time since submission in hours.
- **Explanation:**
The score is the number of votes, discounted by an "age factor" that grows over time. This causes stories to drop in rank as they age, unless they continue to receive votes. The exponent (1.5) ensures a rapid decay in visibility for older posts, promoting freshness and engagement[^2][^1].


### Reddit

**Key Elements:**

- Uses a timestamp constant (site launch time) to compute the relative age of a submission.
- Incorporates a decay mechanism: the further from submission time, the less impact new votes have.
- Early votes are weighted more heavily than later ones: the first 10 votes have as much influence as the next 90.
- The algorithm uses logarithmic scaling to balance vote counts and age, ensuring that both popularity and recency are considered[^2][^1].


### StumbleUpon

**Formula (Simplified):**

$$
\text{Initial Stumbler Power} + \sum \text{Subsequent Stumbler Powers} + N
$$

- **Initial Stumbler Power:** Audience size of the first stumbler divided by their interactions with the domain.
- **Subsequent Stumbler Power:** Factors in each stumbler's audience percentage, toolbar usage bonuses, and penalties for network connections.
- **N:** A random "safety variable" to introduce flexibility and unpredictability.
- **Explanation:**
The algorithm combines the influence of the first user to "stumble" a site and subsequent users, adjusting for their reach and engagement. Bonuses and penalties are applied based on toolbar usage and whether users are connected, aiming to balance organic discovery with anti-gaming mechanisms[^2][^1].


### Del.icio.us

**Formula:**

$$
\text{Points} = \text{Number of bookmarks in the last 3600 seconds}
$$

- **Explanation:**
The ranking is determined by how many times a story has been bookmarked in the past hour. Every bookmark counts as one point, and stories are compared based on their current hourly bookmarking rate, favoring rapidly spreading content[^2][^1].


### Digg

**Approach:**

- Digg's algorithm is deliberately opaque to prevent gaming.
- It is believed to consider:
    - Submission time and category
    - Submitter's authority and activity
    - Social network factors (friends/fans)
    - Geographic location and HTTP referer of voters
- The complexity is higher than the other sites, and the precise formula is not public[^2][^1].

---

## Comparison Table

| Platform | Core Metric | Age Decay? | Social Influence? | Transparency |
| :-- | :-- | :-- | :-- | :-- |
| Hacker News | Votes/age factor | Yes | No | Transparent |
| Reddit | Weighted votes/log(time) | Yes | No | Partially known |
| StumbleUpon | Stumbler power + bonuses | Indirect | Yes | Semi-transparent |
| Del.icio.us | Recent bookmarks/hour | No | No | Transparent |
| Digg | Multiple social/activity | Yes | Yes | Opaque |


---

## Key Takeaways

- **Freshness and engagement** are critical: All algorithms penalize older content to keep feeds dynamic.
- **Early activity matters:** Early votes or bookmarks have disproportionate influence.
- **Social reach and anti-gaming:** Some platforms (StumbleUpon, Digg) factor in user influence and connections, while others (Hacker News, Reddit) focus more on raw votes and time.
- **Transparency varies:** Open formulas (Hacker News, Del.icio.us) allow users to understand ranking, while secretive algorithms (Digg) aim to prevent manipulation[^2][^1].

These mechanisms collectively shape what content surfaces to the top, balancing popularity, recency, and attempts to prevent system abuse.

<div style="text-align: center">⁂</div>

[^1]: https://moz.com/blog/reddit-stumbleupon-delicious-and-hacker-news-algorithms-exposed

[^2]: https://moz.com/blog/reddit-stumbleupon-delicious-and-hacker-news-algorithms-exposed

[^3]: https://news.ycombinator.com/item?id=39778590

[^4]: https://www.reddit.com/r/androidapps/comments/uhbtdx/what_happened_to_stumbleupon_and_is_there_an/

[^5]: https://www.reddit.com/r/SideProject/comments/1dqsi86/do_people_miss_stumbleupon/

[^6]: https://en.wikipedia.org/wiki/Reddit

[^7]: https://blog.udemy.com/social-bookmarking-sites-list/

[^8]: https://github.com/sengkchu/dataquest-guided-projects-solutions/blob/master/Guided Project_%20Transforming%20data%20with%20Python/hn_stories.csv

[^9]: https://dl.acm.org/doi/10.1145/1518701.1518907

[^10]: https://www.reddit.com/r/ifyoulikeblank/comments/b7mr57/wewil_if_i_enjoyed_using_stumbleupon/

[^11]: https://www.slideshare.net/slideshow/social-media-marketing-for-traffic-seo/7241793

[^12]: https://www.reddit.com/r/golang/comments/z4bg24/using_rust_at_a_startup_a_cautionary_tale/

[^13]: https://www.academia.edu/39557206/Mitchell_S_S_D_and_Lim_M_2018_Too_Crowded_for_Crowdsourced_Journalism_Reddit_and_Citizen_Participation_in_the_Syrian_Crisis_Canadian_Journal_of_Communication_43_3_399_419

[^14]: https://www.socialmediaexaminer.com/how-to-track-social-media-traffic-with-google-analytics/

[^15]: https://www.datadreamer.com/cgi-bin/research2/engine.py

[^16]: https://www.webfx.com/blog/web-design/karma-design-pattern/



