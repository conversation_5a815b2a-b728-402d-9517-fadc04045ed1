# bookmarks local first

- this is cutting edge software so websearch, docs fetching is important for success. invest time going over the docs!:
  https://pglite.dev/docs, https://electric-sql.com/docs/,

# use mcp tools

- this is long running task, use sequential thinking to write a concise, comapct, yet simple solution that works and performs well.

- relevant MCP tools: to write better code use DOCS mcp tools : pglite Docs, electric Docs, vueuse Docs, pglite-vue Docs, nuxt Docs, vue Docs
- if you can't find docs also use - context7 for updated code snippets per package

the goal is to create a pg instance on the client with indexdb favorites-local, that will host our bookmark, link... populate it from the server and have the bookmarks update incrementally if changes are made. you need to try to re-use existing code for schema and such. like always, the less code the better! dry, kiss, yagni patterns

remember: i like simple code, readable, clean, short, performant.
this is important feature.
in order to improve performance, save on api calls we'll implement a local first bookmarks
with:

- pglite as local database with indexdb persistene, and extension: live, vector,
- vue reactive updates from the electricsql vue library
- 2 ways to syn data: electric-sync, vue live query. for simplicity we will do live query and not sync 

- we don't want to expose any data that does not relate to presenting the bookmarks to the user - presentational data from bookmarks, links. for example, the user should not know about visit count and other internal stuff. we should also bring only the embedding from bookmark embedding.
- if possible, i'd like not to expose db id. in electeric sync has a way of doing it. great.

the libraries: @electric-sql/pglite, @electric-sql/pglite-vue,

important: you may create a view table if it will help make the shape syncing easier between server on local!

task success condition:
the app works, the search in bookmarks page searched locally.

<!-- denied. to not do - electricsql sync engine hosted on the cloud and that i've already connected to our neondb postgres. electric does the sync from postgres to pglite in thee browser -->
