# Favorites Companion Feature
ignore commented out text!

article.vue page is a test page for features i am testing... 
<!-- when pressing the bookmark icon: 
if the page(by url) is not save already - open save dialog
if the page is saved open the companion tool
- read progress: circular pie chart that gets filled by scroll position
- mark as complete - done reading. 
- mark as unread - ignore the current progresss
- read aloud ()  - play button when clicked: 
it turns to a pause button
it figures out where the article text...
it uses the speech api to get audio and json timing
it highlights the sentence currently read and word (syncing is important)
the cursor changes behaviour as it highlight paragrasps of text - when clicked when read aloud on.. it switched to the new paragraph....  -->

1. [] i want to quickly estimate reading time of an article (large blob of text that is not menus, footer or irrelevant text)
- detect on page the container (selector) that hold the text
- use known and trusted estimation formula for reading

2. Companion Design
- toolbar shaped

section
- speech (icon: headphones, voice)
-- player: gender, back, slow, play|pause, faster, forward, time ? 

- readability (Text/Char)
-- stripdown styles, leave formatting
-- open a readability reader ??
-- change font family and size

- reading progress
-- time to read
-- progress bar

- settings
-- light/dark
-- transparency, icon ghost
-- lock position

- bookmark
-- open down panel: 
page name
page url
tldr
tags + suggested tags
save

