- AI: ignore commented out section when referencing for context
# feature: reading progress for wxt extension

- when the user marks a bookmark for reading later, we create a reading entry in the database
- when the user visits the bookmark, we update the reading entry with the current read percentage
- when the user completes the bookmark, we update the reading entry with the completed status
- we can then display the reading progress in the UI
<!-- - we can also display the reading progress in the notifications -->
<!-- - we can also display the reading progress in the analytics -->

we will focus on the ui of the extension in this prd:
- it will show a progress bar with the current read percentage
- it will show a button to mark the bookmark as completed
- it will show a button to mark the bookmark as unread

problems: sites have comments, footers, ads on the bottom that may need be related to the article. if we create a progress bar along the scrollbar with drag handles we can adjust the progress bar to the content of the article.

we can show the progress bar progress with an indicator on that progress that updates as user scrolls the article

style: a light blue progress bar with a dark blue indicator