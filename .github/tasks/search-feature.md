every good app requires good search.
the search should search our content by free text, tags, scope:
for example: cnn news #channel - everthing that is not # or @ is free text. # mean tag, @ means a user or if none all the site
i really wanted to enhance the search with query dsl (and, not) but i want it to be simple
in this project, i have already begun to write search and the api but i am not happy with the result. this may require research on you part... including sequential thinking to trully come up with a great searching experience that is robust yet easy to use

i want to also add emphasis on ux on regards of the search input. i want to convey to the user what is search,tag, scope... find out a good way to do it
