# Task: Improve Bookmark Item Design

## 1. Discovery & Analysis

- **Objective**: Enhance the visual presentation and user interaction of individual bookmark items.
- **Current State**: Review the existing `BookmarkItem.vue` component (if available) or define a baseline structure.
- **Key Information to Display**:
    * Favicon of the bookmarked site.
    * Page Title.
    * Domain name (e.g., `github.com`).
    * User-added description (if any).
    * Tags associated with the bookmark.
    * Timestamp (e.g., "Saved 2 hours ago").
    * Action buttons/icons (e.g., Edit, Delete, Mark as Read Later, Copy Link).
    * Popularity score/indicator (optional).
    * NSFW flag (if applicable).

## 2. Planning & Design (PRD)

### Goals & Success Metrics:
* **Goal**: Create a visually appealing, informative, and easy-to-use bookmark item component.
* **Success Metrics**:
    * Improved user engagement with individual bookmarks.
    * Reduced visual clutter.
    * Positive user feedback on the new design.

### Feature Breakdown:

* **Layout**:
    * Use a card-based design.
    * Consider a two-column layout within the card:
        * Left: Favicon.
        * Right: Title, Domain, Description, Tags, Timestamp.
    * Actions could be on the right edge or appear on hover.
* **Visual Hierarchy**:
    * Emphasize the title.
    * Use subtle colors for secondary information like domain and timestamp.
    * Tags should be distinct but not overpowering.
* **Interactivity**:
    * Clear hover states for the entire card and individual action items.
    * Clicking the main content area (title/URL) should navigate to the bookmark.
* **Responsiveness**:
    * Ensure the design adapts well to various screen sizes (mobile, tablet, desktop).
    * Tags might wrap or be truncated on smaller screens.
* **Accessibility**:
    * Ensure proper `aria-labels` for interactive elements.
    * Keyboard navigability for all actions.
    * Sufficient color contrast.

### Additional Schema/UI Considerations:
* **Private/Public Indicator**: Show if the bookmark is private.
* **Read Later Indicator**: Show if marked as "read later".
* **Archived State**: Optionally display if the bookmark is archived.
* **Screenshot Preview**: Optionally display a screenshot if available.
* **User Info**: Show who saved the bookmark (if relevant in context).
* **TL;DR**: Display summary if present.

### Technical Design:

* **Component**: `app/components/bookmark/BookmarkItem.vue` (or similar).
* **Props**: The component will accept a `bookmark` object prop containing all necessary data.
* **Styling**: Exclusively use Tailwind CSS classes.
* **Icons**: Utilize an icon library (e.g., `lucide-vue-next` if available, or SVG icons).
* **State Management**: Local component state for hover effects. Emitted events for actions (edit, delete, etc.).

### Mockup/Wireframe (Conceptual):

```
+-------------------------------------------------------------------+
| [Favicon]  **Page Title That Can Be Quite Long** |  example.com   [Actions] [Private/Public Indicator] [Saved: 2h ago] |
|            [notes, blockquote style]                                            |
|            [tldr - if available].        |
|            [Tag1] [Tag2] [Tag3]   | suggested tags [tag4] [tag5]                  |
+-------------------------------------------------------------------+
```

### Milestones & Deliverables:

* **M1: Basic Structure & Styling**:
    * Create/update `BookmarkItem.vue`.
    * Implement the basic layout with Tailwind CSS.
    * Display core information: favicon, title, URL.
* **M2: Add Details & Tags**:
    * Incorporate description, domain, and timestamp.
    * Style and display tags.
* **M3: Implement Actions**:
    * Add action buttons/icons (Edit, Delete, etc.).
    * Implement hover effects for actions.
* **M4: Refine UX & Accessibility**:
    * Improve hover states for the card.
    * Ensure keyboard navigation and ARIA attributes.
    * Test color contrast.
* **M5: Responsiveness**:
    * Test and adjust styling for different screen sizes.
* **M6: Review & Iterate**:
    * Gather feedback and make necessary adjustments.

## 3. Implementation

### ✅ **Completed Milestones:**

**M1: Basic Structure & Styling** ✅
- Created `BookmarkItem.vue` with card-based layout
- Implemented favicon, title, and domain display
- Applied Tailwind CSS styling with hover effects

**M2: Add Details & Tags** ✅
- Added notes display with styled background
- Implemented relative timestamp formatting (e.g., "2h ago", "just now")
- Added clickable, accessible tags with hover/focus states
- Tags emit 'tag-click' events for parent handling

**M3: Implement Actions** ✅
- Added 4 action buttons: Edit, Delete, Read Later, Copy Link
- Implemented proper event emission with TypeScript support
- Added keyboard navigation (Enter/Space) for all actions
- Visual feedback with hover/focus states
- Read Later button shows active state when bookmark.isReadLater is true

### 🚧 **Currently in Progress:**

**Phase 2: Missing PRD Features** ✅
- Added Private/Public indicators with Lock/Eye icons
- Added TL;DR display with distinct blue styling
- Fixed critical emit declaration issues

### 📋 **Remaining Tasks:**

**M4: Refine UX & Accessibility**
- Enhanced hover states for the entire card
- ARIA attributes validation
- Color contrast testing

**M5: Responsiveness**
- Mobile/tablet layout adjustments
- Tag wrapping behavior on smaller screens

**M6: Review & Iterate**
- Component testing across browsers
- Performance optimization
- Final design polish

## 4. Validation

* (To be filled as development progresses)
    * Test on multiple browsers and devices.
    * Validate accessibility using tools like Axe DevTools.

## 5. Review & Feedback

* (To be filled after implementation and validation)

## Next Steps:

1. Confirm the location and name of the `BookmarkItem.vue` component.
2. Begin implementation of **M1: Basic Structure & Styling**.
3. Decide on an icon set if not already established.
