# shadcn-vue Component Library Guidelines

## Component Usage Requirements
1. Follow a component-first development approach by leveraging existing shadcn-vue components
2. Prioritize composition of existing components before creating new ones
3. Reference the official documentation at https://www.shadcn-vue.com for implementation details

## Component Categories
- **Layout Components**: Aspect Ratio, Card, Collapsible, Resizable, etc.
- **Input Components**: Button, Checkbox, Input, Select, Slider, etc. 
- **Navigation**: Breadcrumb, Dropdown Menu, Navigation Menu, etc.
- **Feedback**: Alert, Toast, Progress, Skeleton, etc.
- **Data Display**: Table, Calendar, Charts (Line, Bar, Area, Donut)
- **Overlay**: Dialog, Drawer, Popover, Sheet, etc.

## Technical Integration
- Components are built with Radix Vue and Tailwind CSS
- Dark mode support available for major frameworks (Nuxt, Vite, Astro)
- CLI tools provided for adding components
- Configuration via components.json
- Form handling through VeeValidate and Zod integration

## Documentation & Resources
- Component API documentation available for each component
- Figma design resources provided
- Example implementations and use cases included
- Theming customization via CSS variables

Reference all implementation details in the official documentation at https://www.shadcn-vue.com