Using Drizzle ORM and <PERSON><PERSON>zle-z<PERSON>, create an example that demonstrates:

1. A complete database schema definition including:
   - Table structure with proper column types and constraints
   - Relations between tables (if applicable)
   - Zod validation schema integration

2. Basic database operations showing:
   - SELECT queries with filtering and sorting
   - INSERT operations with validation
   - UPDATE and DELETE operations with proper error handling
   - Type-safe query building using <PERSON><PERSON><PERSON>'s query builders

3. Implementation requirements:
   - Use TypeScript with strict type checking
   - Include proper error handling and logging
   - Follow Drizzle ORM best practices for schema definition
   - Implement zod validators for data validation
   - Add JSDoc comments for function documentation

Please provide working example code that can be tested in a Node.js environment with Drizzle ORM ^0.29.0 and Drizzle-zod installed.

Reference Documentation:
- Drizzle ORM: https://orm.drizzle.team/docs/overview
- Drizzle-zod: https://orm.drizzle.team/docs/zod

# code example

```typescript
// Example of a database query with Drizzle ORM
import { eq, desc } from "drizzle-orm";
import { db, tables } from "@/server/db";

export const findBookmarksByUserId = async (userId: string) => {
  try {
    return await db
      .select()
      .from(tables.bookmarks)
      .where(eq(tables.bookmarks.userId, userId))
      .orderBy(desc(tables.bookmarks.createdAt));
  } catch (error) {
    console.error("Error fetching bookmarks:", error);
    throw new Error("Failed to fetch bookmarks");
  }
};
```