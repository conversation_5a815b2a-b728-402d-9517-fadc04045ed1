# Vue 3.5+ Props with Type-Safe Default Values

Define component props in Vue 3.5+ using TypeScript interfaces and direct destructuring for optimal type safety and developer experience:

## Recommended Pattern

### TypeScript Interface
```typescript
interface Props {
  msg?: string
  labels?: string[]
  count?: number
  isActive?: boolean
  config?: Record<string, unknown>
}
```

### Props Definition with Defaults
```typescript
const {
  msg = 'Default message',
  labels = ['default'],
  count = 0,
  isActive = false,
  config = {}
} = defineProps<Props>()
```

### Component Usage
```vue
<template>
  <div :class="{ active: isActive }">
    <h1>{{ msg }}</h1>
    <span>Count: {{ count }}</span>
    <ul>
      <li v-for="label in labels" :key="label">{{ label }}</li>
    </ul>
  </div>
</template>
```

## Important Notes:
- Use TypeScript interfaces for strong type checking
- Leverage direct destructuring for cleaner syntax
- Provide meaningful default values
- Avoid the deprecated `withDefaults` helper
- Define optional props with the `?` operator
- Include runtime validation when needed using `PropType`

For complex default values, declare them outside the destructuring:
```typescript
const defaultConfig = { theme: 'light', locale: 'en' }
const { config = defaultConfig } = defineProps<Props>()
```

Documentation:
- [Vue 3.5 Props RFC](https://github.com/vuejs/rfcs/discussions/503)
- [Vue 3 TypeScript Guide](https://vuejs.org/guide/typescript/overview.html)