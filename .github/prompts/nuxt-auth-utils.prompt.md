# Nuxt Auth Utils Documentation

A comprehensive authentication module for Nuxt applications providing secure session management with cookie-based authentication.

## Core Features

- Hybrid rendering support (SSR/CSR/SWR/Prerendering)
- OAuth integration with 40+ providers
- Password hashing utilities
- WebAuthn (passkey) support 
- Vue composables for user session management
- Tree-shakable server utilities
- Automatic session handling with AuthState component
- Extensible via hooks
- WebSocket authentication support

## Setup Instructions

1. Install in your Nuxt project:
```bash
npx nuxi@latest module add auth-utils
```

2. Add session password to `.env`:
```bash
NUXT_SESSION_PASSWORD=<32+ character password> 
```

3. Begin using authentication features

## Usage Examples

### Managing User Sessions

```ts
// Server-side
await setUserSession(event, {
  user: { id: 1, name: "User" },
  secure: { apiToken: "abc123" }
})

const session = await getUserSession(event)
await clearUserSession(event)
```

### OAuth Integration 

```ts
// server/routes/auth/github.get.ts
export default defineOAuthGitHubEventHandler({
  config: {
    emailRequired: true
  },
  async onSuccess(event, { user }) {
    await setUserSession(event, {
      user: { githubId: user.id }
    })
    return sendRedirect(event, '/')
  }
})
```

### Client-Side Auth State

```vue
<template>
  <AuthState v-slot="{ loggedIn, user }">
    <div v-if="loggedIn">
      Welcome {{ user.name }}
    </div>
    <NuxtLink v-else to="/login">
      Sign In
    </NuxtLink>
  </AuthState>
</template>
```

## Documentation Links

- [Release Notes](https://github.com/nuxt/auth-utils/blob/main/CHANGELOG.md)
- [OAuth Demo](https://github.com/atinux/atidone)
- [Passkeys Demo](https://github.com/atinux/todo-passkeys)

For complete documentation and examples, visit [nuxt-auth-utils on GitHub](https://github.com/nuxt/auth-utils).