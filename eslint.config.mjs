import withNuxt from './.nuxt/eslint.config.mjs'

export default withNuxt({
  rules: {
    "semi": ['error', 'never'],
    'vue/no-multiple-template-root': 'off',
    'vue/max-attributes-per-line': ['error', { singleline: 3 }],
  }
}, {
  // Add TypeScript support for Vue files
  files: ['**/*.vue'],
  languageOptions: {
    parserOptions: {
      parser: '@typescript-eslint/parser',
      extraFileExtensions: ['.vue']
    }
  }
})
