{"name": "Nuxt 3 + Vue 3 + TypeScript + shadcn-nuxt + Tailwind + Drizzle + Zod + VueUse + Pinia", "description": "Cursor rules for a modern Nuxt 3 project using Vue 3, TypeScript, shadcn-nuxt, Tailwind CSS, Drizzle ORM, Zod, VueUse, and Pinia. Enforces best practices, code style, and productivity for this stack.", "rules": [{"match": "*", "instructions": ["Use <script setup lang=\"ts\"> and the Composition API for all Vue components. Avoid Options API.", "Use TypeScript everywhere. Prefer types over interfaces unless you need extension.", "Avoid enums; use const objects or maps.", "Use PascalCase for component files, camelCase for composables, lowercase-with-dashes for directories.", "Use composables (useXxx) for shared logic. Place in app/composables/.", "Favor functional programming and early returns. Avoid classes.", "Always use Tailwind CSS for styling. No inline styles or CSS files except for Tailwind entry.", "Use shadcn-nuxt UI components for all UI primitives. Prefer composition over inheritance.", "Use Zod for all schema validation, both client and server. Share schemas where possible.", "Use Pinia for state management if global state is needed.", "Use VueUse for utility composables (e.g., useVModel, useFetch, etc).", "Use Drizzle ORM for all DB access. Prefer type-safe queries and migrations.", "Use $fetch or useFetch for API calls. Always type responses.", "Use useHead and useSeoMeta for SEO/meta tags.", "Use Nuxt's file-based routing and directory conventions (pages/, components/, composables/, plugins/, server/api/).", "Use defineProps, defineEmits, and defineExpose for component props/events/expose.", "Use definePageMeta for per-page meta config.", "Use Nuxt's runtimeConfig for environment variables.", "Use Nuxt's auto-imports for composables and components. Don't import manually if not needed.", "Use <ClientOnly> for client-only components (e.g., Toaster).", "Use <NuxtImage> or <NuxtPicture> for images. Optimize with WebP, lazy loading, and size data.", "Use class-variance-authority (CVA) for managing Tailwind variants in UI components.", "Use descriptive variable names with auxiliary verbs (isLoading, hasError, etc).", "Write modular, DRY, and readable code. Avoid duplication.", "Add JSDoc comments for all exported functions, composables, and complex logic.", "Write unit tests for composables and critical logic. Use Jest or Vitest.", "Always handle errors and edge cases. Use early returns and guard clauses.", "Use .env for secrets and runtime config. Never hardcode secrets.", "Use Prettier and ESLint for formatting and linting. Follow project config."]}, {"match": "app/components/**/*.vue", "instructions": ["Use <script setup lang=\"ts\">.", "Use PascalCase for component names and files.", "Use defineProps and defineEmits for props/events.", "Use shadcn-nuxt UI components for all UI primitives.", "Use Tailwind CSS for all styling. No scoped or global CSS.", "Use composables for shared logic.", "Add aria-* and role attributes for accessibility.", "Add loading and error states for async UI.", "Use slots for extensibility.", "Use <ClientOnly> for client-only features (e.g., Toaster)."]}, {"match": "app/composables/**/*.ts", "instructions": ["Use camelCase for composable names (e.g., useAuthState).", "Export composables as named exports.", "Type all arguments and return values.", "Add JSDoc for all composables.", "Use VueUse utilities where possible.", "Write pure functions; avoid side effects unless necessary."]}, {"match": "server/api/**/*.ts", "instructions": ["Use Drizzle ORM for DB access. Prefer type-safe queries.", "Validate all input with Zod schemas.", "Type all API responses.", "Handle errors with early returns and user-friendly messages.", "Never leak sensitive info in errors.", "Use runtimeConfig for secrets and DB URLs."]}, {"match": "app/assets/css/main.css", "instructions": ["Only import Tailwind CSS and shadcn-nuxt base styles.", "No custom CSS except for Tailwind config extensions."]}, {"match": "tests/**/*.ts", "instructions": ["Use Vitest or Jest for all tests.", "Use Arrange-Act-Assert pattern.", "Mock external dependencies.", "Test composables, API endpoints, and critical logic."]}, {"match": "*", "instructions": ["Use Nuxt 3 directory structure: app/pages/, app/components/, app/composables/, app/plugins/, server/api/, server/db/, server/services/.", "Use lowercase-with-dashes for directories.", "Use PascalCase for Vue component files.", "Use .ts for all TypeScript files."]}]}