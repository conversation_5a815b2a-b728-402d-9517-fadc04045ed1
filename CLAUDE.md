# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

Use pnpm as the package manager for all operations:

```bash
# Development
pnpm dev                 # Start development server on localhost:3000
pnpm build              # Build for production
pnpm preview            # Preview production build
pnpm generate           # Generate static files

# Code Quality
pnpm lint               # Run ESLint
pnpm typecheck          # Run TypeScript type checking

# Database Operations
pnpm db:generate        # Generate Drizzle migrations
pnpm db:push            # Push schema changes to database
pnpm db:migrate         # Run migrations
pnpm db:pull            # Pull schema from database
pnpm db:drop            # Drop database tables
pnpm db:studio          # Open Drizzle Studio
```

## Architecture Overview

This is a **social bookmarking platform** built with Nuxt 3 (compatibility mode 4) using:

- **Frontend**: Vue 3 Composition API with `<script setup>`, shadcn-vue components, Tailwind CSS
- **Backend**: Nuxt 3 server routes with Drizzle ORM and PostgreSQL
- **Authentication**: betterAuth with email/password and username support
- **UI Library**: shadcn-vue (located in `app/components/ui/`)
- **Validation**: Zod for client-side, drizzle-zod for server-side API validation

### Directory Structure

- `app/` - Client-side code (pages, components, layouts, composables)
- `server/` - Server-side code (API routes, database operations, services)
- `server/db/schema/` - Drizzle database schema definitions
- `server/db/operations/` - Database operation functions
- `migrations/` - Drizzle migration files

### Path Aliases

- Use `@/` for client-side imports (maps to `app/`)
- Use `$/` for server-side imports (maps to `server/`)

### Core Domain Models

- **User**: Authentication, profiles, followers/following relationships
- **Bookmark**: Core bookmarking functionality with embeddings for similarity search
- **Link**: Shared URLs with metadata and usage tracking
- **Tag**: Categorization system for organizing bookmarks
- **BookmarkMeta**: Reading progress, completion status, visit tracking

## API Architecture

RESTful API pattern with consistent naming:
- Entities use singular form in schema/operations (e.g., `bookmark.ts`)
- API routes use plural form (e.g., `/api/bookmarks/`)
- Related resources are properly nested (e.g., `/api/bookmarks/[id]/bookmark-meta`)

Key API endpoints:
- `/api/bookmarks` - Bookmark CRUD operations
- `/api/bookmarks/[id]/bookmark-meta` - Reading progress operations
- `/api/tags/` - Tag management
- `/api/followers/` - Social following functionality
- `/api/popular/[period]` - Popular content by timeframe

## Code Conventions

- **TypeScript**: Use `types` over `interfaces` unless extendability is needed
- **Variables**: camelCase in code, convert to snake_case for database operations
- **IDs**: Use uuidV7 for all entity IDs
- **Booleans**: Prefix with `is` (e.g., `isPrivate`, `isReadLater`)
- **Event Handlers**: Prefix with `handle` (e.g., `handleClick`, `handleSubmit`)
- **Styling**: Use Tailwind CSS only, no custom CSS classes
- **Components**: Use Composition API with `<script setup>` syntax

## Performance and Development Preferences

**ALWAYS prioritize in this order:**

1. **VueUse Composables**: Use @vueuse/core composables whenever possible instead of building custom implementations
   - `useDebounceFn`, `useThrottleFn` for input handling
   - `useVirtualList`, `useInfiniteScroll` for list performance
   - `useLocalStorage`, `useSessionStorage` for persistence
   - `useAsyncState`, `useFetch` for data fetching
   - `useElementSize`, `useIntersectionObserver` for responsive features

2. **Nuxt Built-in Methods**: Prefer Nuxt's provided utilities and composables
   - `$fetch` for API calls instead of fetch/axios
   - `useHead`, `useSeoMeta` for meta management
   - `navigateTo`, `useRouter` for navigation
   - `useRuntimeConfig` for configuration
   - `useState`, `useCookie` for state management

3. **Vue 3 Native Features**: Use Vue's built-in optimizations
   - `v-memo` for expensive list items
   - `shallowRef`, `shallowReactive` for performance
   - `defineAsyncComponent` for code splitting
   - `Suspense` for async components

**Performance Best Practices:**
- Debounce user input with 200ms delay (optimal UX timing)
- Use virtual scrolling for lists over 100 items
- Implement proper loading states and skeleton UI
- Prefer computed properties over watchers where possible
- Use `v-memo` for list items that render expensive content

## Authentication

The app uses betterAuth for session management:

```typescript
// Client-side auth composable
const { signIn, signOut, signUp, useSession } = useAuth()

// Server-side session requirement
import { requireUserSession } from '$/utils/requireUserSession'
const session = await requireUserSession(event)
```

## Database Operations

- Use Drizzle ORM for all database operations
- Operations are organized in `server/db/operations/` by entity
- Always use transactions for multi-table operations
- Use drizzle-zod for API validation, plain Zod for client forms

## Tasks and Planning

The `/tasks/` folder contains AI-generated planning documents and PRDs for features in development. Reference these for context on ongoing work and architectural decisions.

## Development Notes

- Always run `pnpm lint` and `pnpm typecheck` before commits
- Development server runs on `http://localhost:3000`
- The app supports dark/light theme switching via `@nuxtjs/color-mode`
- Uses betterAuth admin plugin for user management
- Implements bookmark embeddings for AI-powered recommendations
- Requires `DATABASE_URL` environment variable for database connection

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.