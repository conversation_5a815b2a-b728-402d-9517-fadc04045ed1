{"id": "ffd59896-6b1c-490e-bfac-fc34de3531fd", "prevId": "000caf46-e00b-4766-aeed-73d95ac08a32", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bookmark_embedding": {"name": "bookmark_embedding", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "bookmark_id": {"name": "bookmark_id", "type": "text", "primaryKey": false, "notNull": true}, "model_name": {"name": "model_name", "type": "text", "primaryKey": false, "notNull": true}, "model_version": {"name": "model_version", "type": "text", "primaryKey": false, "notNull": true}, "chunk": {"name": "chunk", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(384)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"embeddingIndex": {"name": "embeddingIndex", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bookmark_meta": {"name": "bookmark_meta", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "bookmark_id": {"name": "bookmark_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "read_status": {"name": "read_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'unread'"}, "read_percentage": {"name": "read_percentage", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_read_at": {"name": "last_read_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "visit_count": {"name": "visit_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "time_spent_seconds": {"name": "time_spent_seconds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"bookmark_meta_bookmark_id_bookmark_id_fk": {"name": "bookmark_meta_bookmark_id_bookmark_id_fk", "tableFrom": "bookmark_meta", "tableTo": "bookmark", "columnsFrom": ["bookmark_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "bookmark_meta_user_id_user_id_fk": {"name": "bookmark_meta_user_id_user_id_fk", "tableFrom": "bookmark_meta", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"bookmark_meta_bookmark_user_unique_idx": {"name": "bookmark_meta_bookmark_user_unique_idx", "nullsNotDistinct": false, "columns": ["bookmark_id", "user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bookmark": {"name": "bookmark", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "link_id": {"name": "link_id", "type": "text", "primaryKey": false, "notNull": true}, "user_title": {"name": "user_title", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "tldr": {"name": "tldr", "type": "text", "primaryKey": false, "notNull": false}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_read_later": {"name": "is_read_later", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "tag_names": {"name": "tag_names", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "visit_count": {"name": "visit_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"bookmark_tags_idx": {"name": "bookmark_tags_idx", "columns": [{"expression": "tag_names", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bookmark_user_id_user_id_fk": {"name": "bookmark_user_id_user_id_fk", "tableFrom": "bookmark", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "bookmark_link_id_link_id_fk": {"name": "bookmark_link_id_link_id_fk", "tableFrom": "bookmark", "tableTo": "link", "columnsFrom": ["link_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_user_url_idx": {"name": "unique_user_url_idx", "nullsNotDistinct": false, "columns": ["user_id", "link_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.follower": {"name": "follower", "schema": "", "columns": {"follower_id": {"name": "follower_id", "type": "text", "primaryKey": false, "notNull": true}, "followee_id": {"name": "followee_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"follower_follower_id_user_id_fk": {"name": "follower_follower_id_user_id_fk", "tableFrom": "follower", "tableTo": "user", "columnsFrom": ["follower_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "follower_followee_id_user_id_fk": {"name": "follower_followee_id_user_id_fk", "tableFrom": "follower", "tableTo": "user", "columnsFrom": ["followee_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"follower_follower_id_followee_id_pk": {"name": "follower_follower_id_followee_id_pk", "columns": ["follower_id", "followee_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.link_meta": {"name": "link_meta", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "link_id": {"name": "link_id", "type": "text", "primaryKey": false, "notNull": true}, "interaction_type": {"name": "interaction_type", "type": "text", "primaryKey": false, "notNull": true}, "source": {"name": "source", "type": "text", "primaryKey": false, "notNull": false}, "device_info": {"name": "device_info", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"link_meta_user_id_user_id_fk": {"name": "link_meta_user_id_user_id_fk", "tableFrom": "link_meta", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "link_meta_link_id_link_id_fk": {"name": "link_meta_link_id_link_id_fk", "tableFrom": "link_meta", "tableTo": "link", "columnsFrom": ["link_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.link": {"name": "link", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "favicon": {"name": "favicon", "type": "text", "primaryKey": false, "notNull": false}, "is_nsfw": {"name": "is_nsfw", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "click_count": {"name": "click_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "bookmark_count": {"name": "bookmark_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "tag_names": {"name": "tag_names", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"link_tag_idx": {"name": "link_tag_idx", "columns": [{"expression": "tag_names", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"link_url_unique": {"name": "link_url_unique", "nullsNotDistinct": false, "columns": ["url"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "impersonated_by": {"name": "impersonated_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tag": {"name": "tag", "schema": "", "columns": {"name": {"name": "name", "type": "text", "primaryKey": true, "notNull": true}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"tag_usage_count_idx": {"name": "tag_usage_count_idx", "columns": [{"expression": "usage_count", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "display_username": {"name": "display_username", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "banned": {"name": "banned", "type": "boolean", "primaryKey": false, "notNull": false}, "ban_reason": {"name": "ban_reason", "type": "text", "primaryKey": false, "notNull": false}, "ban_expires": {"name": "ban_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "handle": {"name": "handle", "type": "text", "primaryKey": false, "notNull": true}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "onboarded": {"name": "onboarded", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_active": {"name": "last_active", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "user_username_unique": {"name": "user_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "user_handle_unique": {"name": "user_handle_unique", "nullsNotDistinct": false, "columns": ["handle"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.role": {"name": "role", "schema": "public", "values": ["USER", "MEMBER", "ADMIN"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}