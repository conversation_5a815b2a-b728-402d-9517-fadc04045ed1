import { pgTable, index, text, vector, timestamp, foreignKey, unique, boolean, integer, jsonb, primaryKey, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const role = pgEnum("role", ['USER', 'MEMBER', 'ADMIN'])


export const bookmarkEmbedding = pgTable("bookmark_embedding", {
	id: text().primaryKey().notNull(),
	bookmarkId: text("bookmark_id").notNull(),
	modelName: text("model_name").notNull(),
	modelVersion: text("model_version").notNull(),
	chunk: text().notNull(),
	embedding: vector({ dimensions: 384 }),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("embeddingIndex").using("hnsw", table.embedding.asc().nullsLast().op("vector_cosine_ops")),
]);

export const account = pgTable("account", {
	id: text().primaryKey().notNull(),
	accountId: text("account_id").notNull(),
	providerId: text("provider_id").notNull(),
	userId: text("user_id").notNull(),
	accessToken: text("access_token"),
	refreshToken: text("refresh_token"),
	idToken: text("id_token"),
	accessTokenExpiresAt: timestamp("access_token_expires_at", { mode: 'string' }),
	refreshTokenExpiresAt: timestamp("refresh_token_expires_at", { mode: 'string' }),
	scope: text(),
	password: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "account_user_id_user_id_fk"
		}).onDelete("cascade"),
]);

export const link = pgTable("link", {
	id: text().primaryKey().notNull(),
	url: text().notNull(),
	title: text(),
	description: text(),
	favicon: text(),
	isNsfw: boolean("is_nsfw").default(false).notNull(),
	clickCount: integer("click_count").default(0).notNull(),
	bookmarkCount: integer("bookmark_count").default(0).notNull(),
	tagNames: text("tag_names").array().default([""]),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("link_tag_idx").using("btree", table.tagNames.asc().nullsLast().op("array_ops")),
	unique("link_url_unique").on(table.url),
]);

export const linkMeta = pgTable("link_meta", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	linkId: text("link_id").notNull(),
	interactionType: text("interaction_type").notNull(),
	source: text(),
	deviceInfo: jsonb("device_info"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "link_meta_user_id_user_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.linkId],
			foreignColumns: [link.id],
			name: "link_meta_link_id_link_id_fk"
		}).onDelete("cascade"),
]);

export const session = pgTable("session", {
	id: text().primaryKey().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	token: text().notNull(),
	ipAddress: text("ip_address"),
	userAgent: text("user_agent"),
	userId: text("user_id").notNull(),
	impersonatedBy: text("impersonated_by"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "session_user_id_user_id_fk"
		}).onDelete("cascade"),
	unique("session_token_unique").on(table.token),
]);

export const tag = pgTable("tag", {
	name: text().primaryKey().notNull(),
	usageCount: integer("usage_count").default(0).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("tag_usage_count_idx").using("btree", table.usageCount.asc().nullsLast().op("int4_ops")),
]);

export const bookmark = pgTable("bookmark", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	linkId: text("link_id").notNull(),
	userTitle: text("user_title"),
	notes: text(),
	tldr: text(),
	isPrivate: boolean("is_private").default(false).notNull(),
	isReadLater: boolean("is_read_later").default(false).notNull(),
	tagNames: text("tag_names").array().default([""]),
	visitCount: integer("visit_count").default(0).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("bookmark_tags_idx").using("btree", table.tagNames.asc().nullsLast().op("array_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "bookmark_user_id_user_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.linkId],
			foreignColumns: [link.id],
			name: "bookmark_link_id_link_id_fk"
		}).onDelete("cascade"),
	unique("unique_user_url_idx").on(table.userId, table.linkId),
]);

export const verification = pgTable("verification", {
	id: text().primaryKey().notNull(),
	identifier: text().notNull(),
	value: text().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const bookmarkMeta = pgTable("bookmark_meta", {
	id: text().primaryKey().notNull(),
	bookmarkId: text("bookmark_id").notNull(),
	userId: text("user_id").notNull(),
	readStatus: text("read_status").default('unread').notNull(),
	readPercentage: integer("read_percentage").default(0).notNull(),
	lastReadAt: timestamp("last_read_at", { withTimezone: true, mode: 'string' }),
	visitCount: integer("visit_count").default(0).notNull(),
	timeSpentSeconds: integer("time_spent_seconds").default(0).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.bookmarkId],
			foreignColumns: [bookmark.id],
			name: "bookmark_meta_bookmark_id_bookmark_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "bookmark_meta_user_id_user_id_fk"
		}).onDelete("cascade"),
	unique("bookmark_meta_bookmark_user_unique_idx").on(table.bookmarkId, table.userId),
]);

export const user = pgTable("user", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	bio: text(),
	email: text().notNull(),
	emailVerified: boolean("email_verified").default(false).notNull(),
	username: text(),
	displayUsername: text("display_username"),
	image: text(),
	role: text().notNull(),
	banned: boolean(),
	banReason: text("ban_reason"),
	banExpires: timestamp("ban_expires", { mode: 'string' }),
	avatarUrl: text("avatar_url"),
	onboarded: boolean().default(false).notNull(),
	lastActive: timestamp("last_active", { withTimezone: true, mode: 'string' }),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	unique("user_email_unique").on(table.email),
	unique("user_username_unique").on(table.username),
]);

export const follower = pgTable("follower", {
	followerId: text("follower_id").notNull(),
	followeeId: text("followee_id").notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.followerId],
			foreignColumns: [user.id],
			name: "follower_follower_id_user_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.followeeId],
			foreignColumns: [user.id],
			name: "follower_followee_id_user_id_fk"
		}).onDelete("cascade"),
	primaryKey({ columns: [table.followerId, table.followeeId], name: "follower_follower_id_followee_id_pk"}),
]);
