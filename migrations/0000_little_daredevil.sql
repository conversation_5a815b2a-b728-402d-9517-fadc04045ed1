CREATE TYPE "public"."role" AS ENUM('USER', 'MEMBER', 'ADMIN');--> statement-breakpoint
CREATE TABLE "bookmark_embedding" (
	"id" text PRIMARY KEY NOT NULL,
	"bookmark_id" text NOT NULL,
	"model_name" text NOT NULL,
	"model_version" text NOT NULL,
	"chunk" text NOT NULL,
	"embedding" vector(384),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "bookmark_meta" (
	"id" text PRIMARY KEY NOT NULL,
	"bookmark_id" text NOT NULL,
	"user_id" text NOT NULL,
	"read_status" text DEFAULT 'unread' NOT NULL,
	"read_percentage" integer DEFAULT 0 NOT NULL,
	"last_read_at" timestamp with time zone,
	"visit_count" integer DEFAULT 0 NOT NULL,
	"time_spent_seconds" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone,
	CONSTRAINT "bookmark_meta_bookmark_user_unique_idx" UNIQUE("bookmark_id","user_id")
);
--> statement-breakpoint
CREATE TABLE "bookmark" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"link_id" text NOT NULL,
	"user_title" text,
	"notes" text,
	"tldr" text,
	"is_private" boolean DEFAULT false NOT NULL,
	"is_read_later" boolean DEFAULT false NOT NULL,
	"tag_names" text[] DEFAULT '{}',
	"visit_count" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone,
	CONSTRAINT "unique_user_url_idx" UNIQUE("user_id","link_id")
);
--> statement-breakpoint
CREATE TABLE "follower" (
	"follower_id" text NOT NULL,
	"followee_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "follower_follower_id_followee_id_pk" PRIMARY KEY("follower_id","followee_id")
);
--> statement-breakpoint
CREATE TABLE "link_meta" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"link_id" text NOT NULL,
	"interaction_type" text NOT NULL,
	"source" text,
	"device_info" jsonb,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "link" (
	"id" text PRIMARY KEY NOT NULL,
	"url" text NOT NULL,
	"title" text,
	"description" text,
	"favicon" text,
	"is_nsfw" boolean DEFAULT false NOT NULL,
	"click_count" integer DEFAULT 0 NOT NULL,
	"bookmark_count" integer DEFAULT 0 NOT NULL,
	"tag_names" text[] DEFAULT '{}',
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone,
	CONSTRAINT "link_url_unique" UNIQUE("url")
);
--> statement-breakpoint
CREATE TABLE "tag" (
	"name" text PRIMARY KEY NOT NULL,
	"usage_count" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "user" (
	"id" text PRIMARY KEY NOT NULL,
	"email" text NOT NULL,
	"email_verified" boolean DEFAULT false NOT NULL,
	"role" "role" DEFAULT 'USER' NOT NULL,
	"name" text NOT NULL,
	"handle" text NOT NULL,
	"avatar_url" text,
	"hashed_password" text NOT NULL,
	"banned" boolean DEFAULT false NOT NULL,
	"banned_reason" text,
	"onboarded" boolean DEFAULT false NOT NULL,
	"bio" text,
	"last_active" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone,
	CONSTRAINT "user_email_unique" UNIQUE("email"),
	CONSTRAINT "user_handle_unique" UNIQUE("handle")
);
--> statement-breakpoint
ALTER TABLE "bookmark_meta" ADD CONSTRAINT "bookmark_meta_bookmark_id_bookmark_id_fk" FOREIGN KEY ("bookmark_id") REFERENCES "public"."bookmark"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bookmark_meta" ADD CONSTRAINT "bookmark_meta_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bookmark" ADD CONSTRAINT "bookmark_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bookmark" ADD CONSTRAINT "bookmark_link_id_link_id_fk" FOREIGN KEY ("link_id") REFERENCES "public"."link"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "follower" ADD CONSTRAINT "follower_follower_id_user_id_fk" FOREIGN KEY ("follower_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "follower" ADD CONSTRAINT "follower_followee_id_user_id_fk" FOREIGN KEY ("followee_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "link_meta" ADD CONSTRAINT "link_meta_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "link_meta" ADD CONSTRAINT "link_meta_link_id_link_id_fk" FOREIGN KEY ("link_id") REFERENCES "public"."link"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "embeddingIndex" ON "bookmark_embedding" USING hnsw ("embedding" vector_cosine_ops);--> statement-breakpoint
CREATE INDEX "bookmark_tags_idx" ON "bookmark" USING btree ("tag_names");--> statement-breakpoint
CREATE INDEX "link_tag_idx" ON "link" USING btree ("tag_names");--> statement-breakpoint
CREATE INDEX "tag_usage_count_idx" ON "tag" USING btree ("usage_count");