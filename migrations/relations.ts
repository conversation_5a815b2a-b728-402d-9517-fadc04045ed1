import { relations } from "drizzle-orm/relations";
import { user, account, linkMeta, link, session, bookmark, bookmarkMeta, follower } from "./schema";

export const accountRelations = relations(account, ({one}) => ({
	user: one(user, {
		fields: [account.userId],
		references: [user.id]
	}),
}));

export const userRelations = relations(user, ({many}) => ({
	accounts: many(account),
	linkMetas: many(linkMeta),
	sessions: many(session),
	bookmarks: many(bookmark),
	bookmarkMetas: many(bookmarkMeta),
	followers_followerId: many(follower, {
		relationName: "follower_followerId_user_id"
	}),
	followers_followeeId: many(follower, {
		relationName: "follower_followeeId_user_id"
	}),
}));

export const linkMetaRelations = relations(linkMeta, ({one}) => ({
	user: one(user, {
		fields: [linkMeta.userId],
		references: [user.id]
	}),
	link: one(link, {
		fields: [linkMeta.linkId],
		references: [link.id]
	}),
}));

export const linkRelations = relations(link, ({many}) => ({
	linkMetas: many(linkMeta),
	bookmarks: many(bookmark),
}));

export const sessionRelations = relations(session, ({one}) => ({
	user: one(user, {
		fields: [session.userId],
		references: [user.id]
	}),
}));

export const bookmarkRelations = relations(bookmark, ({one, many}) => ({
	user: one(user, {
		fields: [bookmark.userId],
		references: [user.id]
	}),
	link: one(link, {
		fields: [bookmark.linkId],
		references: [link.id]
	}),
	bookmarkMetas: many(bookmarkMeta),
}));

export const bookmarkMetaRelations = relations(bookmarkMeta, ({one}) => ({
	bookmark: one(bookmark, {
		fields: [bookmarkMeta.bookmarkId],
		references: [bookmark.id]
	}),
	user: one(user, {
		fields: [bookmarkMeta.userId],
		references: [user.id]
	}),
}));

export const followerRelations = relations(follower, ({one}) => ({
	user_followerId: one(user, {
		fields: [follower.followerId],
		references: [user.id],
		relationName: "follower_followerId_user_id"
	}),
	user_followeeId: one(user, {
		fields: [follower.followeeId],
		references: [user.id],
		relationName: "follower_followeeId_user_id"
	}),
}));