// https://nuxt.com/docs/api/configuration/nuxt-config
import { fileURLToPath } from "node:url";
import tailwindcss from "@tailwindcss/vite";

export default defineNuxtConfig({
  compatibilityDate: "2024-11-01",
  devtools: { enabled: true },
  future: {
    compatibilityVersion: 4,
  },
  modules: [
    "@nuxt/eslint",
    "@nuxt/fonts",
    "@nuxt/icon",
    "@nuxt/image",
    "@nuxt/scripts",
    "shadcn-nuxt",
    "@nuxtjs/color-mode",
  ],
  alias: {
    $: fileURLToPath(new URL("./server", import.meta.url)),
    "@": fileURLToPath(new URL("./app", import.meta.url)),
  },
  runtimeConfig: {
    // Server-side environment variables
    database: {
      url: process.env.DATABASE_URL,
    },
  },
  shadcn: {
    componentDir: "./app/components/ui",
    prefix: "",
  },
  css: ["~/assets/css/main.css"],
  vite: {
    plugins: [tailwindcss()],
    optimizeDeps: {
      exclude: ["@electric-sql/pglite", '@electric-sql/pglite/live', '@electric-sql/pglite/vector'],
    },
    worker: {
      format: "es",
    },
  },
  colorMode: {
    preference: "dark", // default value of $colorMode.preference
    fallback: "light", // fallback value if not system preference found
    // hid: 'nuxt-color-mode-script',
    globalName: "__NUXT_COLOR_MODE__",
    componentName: "ColorScheme",
    classPrefix: "",
    storage: "localStorage", // or 'sessionStorage' or 'cookie'
    storageKey: "nuxt-color-mode",
  },
  vue: {
    compilerOptions: {
      isCustomElement: (tag: string) => tag.startsWith('pglite-')
    }
  },
  ssr: true,
});
