{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "claude": "pnpx @anthropic-ai/claude-code", "lint": "eslint .", "typecheck": "nuxt typecheck", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:migrate": "drizzle-kit migrate", "db:drop": "drizzle-kit drop", "db:studio": "drizzle-kit studio"}, "dependencies": {"@electric-sql/pglite": "^0.3.2", "@electric-sql/pglite-vue": "^0.2.20", "@iconify/vue": "^5.0.0", "@neondatabase/serverless": "^1.0.0", "@nuxt/eslint": "1.3.1", "@nuxt/fonts": "0.11.3", "@nuxt/icon": "1.12.0", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.6", "@nuxtjs/color-mode": "^3.5.2", "@tailwindcss/vite": "^4.1.6", "@unhead/vue": "^2.0.3", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.1.0", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.43.1", "drizzle-zod": "^0.7.1", "eslint": "^9.0.0", "lucide-vue-next": "^0.510.0", "pg": "^8.16.0", "reka-ui": "^2.2.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "tw-animate-css": "^1.2.9", "uuid": "^11.1.0", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue-sonner": "^1.3.2", "ws": "^8.18.2", "zod": "^3.24.4"}, "devDependencies": {"@electric-sql/pglite-repl": "^0.2.20", "@faker-js/faker": "^9.8.0", "@iconify-json/lucide": "^1.2.44", "drizzle-kit": "^0.31.1", "drizzle-seed": "^0.3.1", "nuxt": "^3.17.4", "shadcn-nuxt": "^2.1.0"}}