<script setup>
  import { ref, watch, onMounted } from 'vue'
  import { But<PERSON> } from '@/components/ui/button'

  definePageMeta({
    title: 'Fa.vorit.es',
    description: 'Discover the web',
    layout: 'empty'
  })

  // State variables
  const timeframe = ref('day')

  // Sample data - would be replaced with API calls
  const featuredBookmark = {
    id: 'featured-1',
    title: 'The Ultimate Guide to Web Development in 2025',
    url: 'https://webdev-trends.com/2025-guide',
    description: 'A comprehensive overview of the latest frameworks, tools, and best practices for modern web development.',
    domain: 'webdev-trends.com',
    tags: ['webdev', 'tutorial', 'javascript', 'frameworks'],
    savedBy: 235,
    favicon: 'https://webdev-trends.com/favicon.ico'
  }

  const popularBookmarks = ref([
    {
      id: 'bk-1',
      title: 'How AI is Transforming Content Creation',
      url: 'https://techinsights.com/ai-content-revolution',
      domain: 'techinsights.com',
      tags: ['ai', 'content', 'technology'],
      savedBy: 189,
      favicon: 'https://techinsights.com/favicon.ico'
    },
    {
      id: 'bk-2',
      title: 'The Psychology of Productivity: 7 Science-Backed Strategies',
      url: 'https://productivityhub.org/psychology-strategies',
      domain: 'productivityhub.org',
      tags: ['productivity', 'psychology', 'wellbeing'],
      savedBy: 163,
      favicon: 'https://productivityhub.org/favicon.ico'
    },
    {
      id: 'bk-3',
      title: '10 Design Principles Every Developer Should Know',
      url: 'https://uidesignpatterns.com/dev-principles',
      domain: 'uidesignpatterns.com',
      tags: ['design', 'ui', 'development'],
      savedBy: 142,
      favicon: 'https://uidesignpatterns.com/favicon.ico'
    },
    {
      id: 'bk-4',
      title: 'The Future of Remote Work: Trends for 2026 and Beyond',
      url: 'https://futureworktrends.com/remote-2026',
      domain: 'futureworktrends.com',
      tags: ['remote', 'work', 'trends'],
      savedBy: 130,
      favicon: 'https://futureworktrends.com/favicon.ico'
    },
    {
      id: 'bk-5',
      title: 'Understanding TypeScript: A Comprehensive Guide',
      url: 'https://typescriptguides.dev/comprehensive',
      domain: 'typescriptguides.dev',
      tags: ['typescript', 'javascript', 'programming'],
      savedBy: 128,
      favicon: 'https://typescriptguides.dev/favicon.ico'
    }
  ])

  // Mock data for different timeframes
  const weeklyPopularBookmarks = [
    {
      id: 'bk-w1',
      title: 'The Evolution of Frontend Development: 2020-2025',
      url: 'https://frontendtrends.dev/evolution-2025',
      domain: 'frontendtrends.dev',
      tags: ['frontend', 'javascript', 'webdev'],
      savedBy: 312,
      favicon: 'https://frontendtrends.dev/favicon.ico'
    },
    {
      id: 'bk-w2',
      title: 'Mastering Modern CSS Techniques',
      url: 'https://cssmastery.dev/modern-techniques',
      domain: 'cssmastery.dev',
      tags: ['css', 'design', 'frontend'],
      savedBy: 287,
      favicon: 'https://cssmastery.dev/favicon.ico'
    }
    // ...other weekly bookmarks
  ]

  const monthlyPopularBookmarks = [
    {
      id: 'bk-m1',
      title: 'Full-Stack Development Roadmap for 2025',
      url: 'https://fullstackworld.com/roadmap-2025',
      domain: 'fullstackworld.com',
      tags: ['fullstack', 'career', 'learning'],
      savedBy: 542,
      favicon: 'https://fullstackworld.com/favicon.ico'
    },
    {
      id: 'bk-m2',
      title: 'The Definitive Guide to PostgreSQL Performance',
      url: 'https://pgperformance.dev/definitive-guide',
      domain: 'pgperformance.dev',
      tags: ['postgresql', 'database', 'performance'],
      savedBy: 498,
      favicon: 'https://pgperformance.dev/favicon.ico'
    }
    // ...other monthly bookmarks
  ]

  // Functions
  const fetchPopularBookmarks = async (period = 'day') => {
    try {
      // In a real application, this would be an API call
      // const response = await $fetch(`/api/bookmarks/popular?period=${period}`);
      // popularBookmarks.value = response;

      // For now, just use our mock data
      if (period === 'day') {
        // Keep the current data
      }
      else if (period === 'week') {
        popularBookmarks.value = weeklyPopularBookmarks
      }
      else if (period === 'month') {
        popularBookmarks.value = monthlyPopularBookmarks
      }
    }
    catch (error) {
      console.error('Error fetching popular bookmarks:', error)
    }
  }

  const fetchFeaturedBookmark = async () => {
    try {
      // In a real application, this would be an API call
      // const response = await $fetch('/api/bookmarks/featured');
      // featuredBookmark.value = response;

      // For now, we're using the mock data defined above
    }
    catch (error) {
      console.error('Error fetching featured bookmark:', error)
    }
  }

  // Image error handling
  function onImageError(event) {
    // Replace broken favicon with a placeholder
    const target = event.target
    target.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>'
  }

  // Handle timeframe changes
  watch(timeframe, (newTimeframe) => {
    fetchPopularBookmarks(newTimeframe)
  })

  // Initialize data on component mount
  onMounted(() => {
    fetchPopularBookmarks('day')
    fetchFeaturedBookmark()
  })
</script>

<template>
  <div class="flex flex-col min-h-screen">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-blue-700 to-blue-700 text-white py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="md:flex md:items-center md:justify-between">
          <div class="md:w-1/2">
            <h1 class="text-4xl font-extrabold tracking-tight sm:text-5xl md:text-6xl">
              <span class="block">Organize your web.</span>
              <span class="block text-blue-200">Discover the internet.</span>
            </h1>
            <p class="mt-3 text-lg text-blue-100 sm:mt-5 sm:text-xl md:mt-5 md:text-2xl">
              Save, manage, and discover web bookmarks with our social community.
            </p>
            <div class="mt-8 flex">
              <div class="rounded-md shadow">
                <NuxtLink to="/register">
                  <Button variant="default" size="lg" class="bg-white text-blue-700 hover:bg-blue-50">
                    Get Started
                  </Button>
                </NuxtLink>
              </div>
              <div class="ml-3 rounded-md shadow">
                <NuxtLink to="/login">
                  <Button variant="default" size="lg" class="bg-blue-800 text-white hover:bg-blue-700">
                    Sign In
                  </Button>
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Search Bar -->
     <section>
       <div class="bg-white shadow-sm border-b">
         <div class="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
           <Marquee class="text-gray-500">
             <a
v-for="tag in ['news', 'technology', 'ai', 'webdev', 'productivity', 'design', 'frontend', 'backend', 'database', 'typescript', 'javascript', 'css', 'html', 'remote', 'career', 'learning', 'trends', 'tutorial', 'frameworks', 'wellbeing']"
               :key="tag"
href="#"
class="text-blue-700 hover:text-blue-900">
               #{{ tag }}
             </a>
           </Marquee>
         </div>
       </div>
     </section>

    <!-- Main Content -->
    <div class="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-extrabold text-gray-900 mb-6">
        Featured Bookmark
      </h2>
      <div class="flex flex-col md:flex-row gap-6 bg-white p-6 rounded-lg shadow-lg border border-gray-200">
        <!-- <NuxtImg
          :src="featuredBookmark.favicon"
          :alt="`${featuredBookmark.domain} favicon`"
          class="w-10 h-10 rounded-md block md:hidden mb-2"
          @error="onImageError"
        /> -->
        <NuxtImg
          src="https://screenshotone.com/_astro/stripe.DktQZYtB_2nk9Aw.webp"
          alt="Featured Bookmark Screenshot"
          class="w-full md:w-1/2 h-auto object-cover rounded-lg shadow-md filter sepia-[.40] hue-rotate-[190deg] saturate-150 contrast-90 brightness-110"
          @error="onImageError"
        />
        <div class="md:w-1/2 flex flex-col justify-center">
          <div class="flex items-center mb-2">
            <!-- <NuxtImg
              :src="featuredBookmark.favicon"
              :alt="`${featuredBookmark.domain} favicon`"
              class="w-6 h-6 mr-2 rounded hidden md:block"
              @error="onImageError"
            /> -->
            <h3 class="text-2xl font-semibold text-gray-800">
              {{ featuredBookmark.title }}
            </h3>
          </div>
          <a
            :href="featuredBookmark.url"
            target="_blank"
            rel="noopener noreferrer"
            class="text-blue-600 hover:text-blue-800 hover:underline truncate mb-1 text-sm"
          >
            {{ featuredBookmark.domain }}
          </a>
          <p class="text-gray-600 text-sm mb-3">
            {{ featuredBookmark.description }}
          </p>
          <div class="flex flex-wrap gap-2 mb-3">
            <span
              v-for="tag in featuredBookmark.tags"
              :key="tag"
              class="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full"
            >
              #{{ tag }}
            </span>
          </div>
          <p class="text-sm text-gray-500">
            Saved by {{ featuredBookmark.savedBy }} users
          </p>
        </div>
      </div>
    </div>

    <!-- Popular Bookmarks Section (example, can be expanded) -->
    <section class="py-12 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-extrabold text-gray-900">
            Popular This {{ timeframe.charAt(0).toUpperCase() + timeframe.slice(1) }}
          </h2>
          <div class="flex space-x-2">
            <Button :variant="timeframe === 'day' ? 'default' : 'outline'" size="sm" @click="timeframe = 'day'">
              Day
            </Button>
            <Button :variant="timeframe === 'week' ? 'default' : 'outline'" size="sm" @click="timeframe = 'week'">
              Week
            </Button>
            <Button :variant="timeframe === 'month' ? 'default' : 'outline'" size="sm" @click="timeframe = 'month'">
              Month
            </Button>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="bookmark in popularBookmarks" :key="bookmark.id" class="bg-white p-4 rounded-lg shadow border border-gray-200">
            <div class="flex items-center mb-2">
              <!-- <NuxtImg
                :src="bookmark.favicon"
                :alt="`${bookmark.domain} favicon`"
                class="w-5 h-5 mr-2 rounded"
                @error="onImageError"
              /> -->
              <h4 class="text-lg font-semibold text-gray-800 truncate" :title="bookmark.title">
                {{ bookmark.title }}
              </h4>
            </div>
            <a
:href="bookmark.url"
target="_blank"
rel="noopener noreferrer"
class="text-xs text-blue-500 hover:underline truncate block mb-1">
              {{ bookmark.domain }}
            </a>
            <div class="flex flex-wrap gap-1 mb-2">
              <span
                v-for="tag in bookmark.tags"
                :key="tag"
                class="px-1.5 py-0.5 bg-gray-100 text-gray-600 text-xs font-medium rounded-full"
              >
                #{{ tag }}
              </span>
            </div>
            <p class="text-xs text-gray-500">
              Saved by {{ bookmark.savedBy }} users
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="bg-blue-700 text-white">
      <div class="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between">
        <h2 class="text-3xl font-extrabold tracking-tight sm:text-4xl">
          <span class="block">Ready to dive in?</span>
          <span class="block text-blue-300">Start organizing your bookmarks today.</span>
        </h2>
        <div class="mt-8 flex lg:mt-0 lg:flex-shrink-0">
          <div class="inline-flex rounded-md shadow">
            <NuxtLink to="/register">
              <Button variant="default" size="lg" class="bg-white text-blue-700 hover:bg-blue-50">
                Sign up for free
              </Button>
            </NuxtLink>
          </div>
          <div class="ml-3 inline-flex rounded-md shadow">
            <NuxtLink to="/about">
              <Button variant="default" size="lg" class="bg-blue-800 text-white hover:bg-blue-900">
                Learn more
              </Button>
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div>
            <h3 class="text-sm font-semibold uppercase tracking-wider">
              Product
            </h3>
            <ul class="mt-4 space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white">Features</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Pricing</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">FAQ</a></li>
            </ul>
          </div>
          <div>
            <h3 class="text-sm font-semibold uppercase tracking-wider">
              Community
            </h3>
            <ul class="mt-4 space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white">Popular Users</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Trending Tags</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Collections</a></li>
            </ul>
          </div>
          <div>
            <h3 class="text-sm font-semibold uppercase tracking-wider">
              Company
            </h3>
            <ul class="mt-4 space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white">About</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Blog</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Careers</a></li>
            </ul>
          </div>
          <div>
            <h3 class="text-sm font-semibold uppercase tracking-wider">
              Legal
            </h3>
            <ul class="mt-4 space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white">Privacy</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Terms</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Cookie Policy</a></li>
            </ul>
          </div>
        </div>
        <div class="mt-8 border-t border-gray-800 pt-8 md:flex md:items-center md:justify-between">
          <div class="flex space-x-6 md:order-2">
            <a href="#" class="text-gray-400 hover:text-white">
              <span class="sr-only">Twitter</span>
              <svg
class="h-6 w-6"
fill="currentColor"
viewBox="0 0 24 24"
aria-hidden="true">
                <path
                  d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
              </svg>
            </a>
            <a href="#" class="text-gray-400 hover:text-white">
              <span class="sr-only">GitHub</span>
              <svg
class="h-6 w-6"
fill="currentColor"
viewBox="0 0 24 24"
aria-hidden="true">
                <path
fill-rule="evenodd"
                  d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                  clip-rule="evenodd" />
              </svg>
            </a>
          </div>
          <p class="mt-8 text-base text-gray-400 md:mt-0 md:order-1">
            &copy; 2025 fa.vorit.es. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>
