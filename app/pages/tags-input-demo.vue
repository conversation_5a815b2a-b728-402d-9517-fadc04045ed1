<script setup lang="ts">
import { ref, computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  TagsInput,
  TagsInputInput,
  TagsInputItem,
  TagsInputItemDelete,
  TagsInputItemText
} from '@/components/ui/tags-input'

definePageMeta({
  title: 'TagsInput Demo',
  description: 'Demo of the enhanced TagsInput component with hashtags, mentions, and text'
})

const tags = ref<string[]>(['#javascript', '@user', 'regular text', '#vue', '@admin', 'some longer text'])

const handleTagsChange = (newTags: string[]) => {
  tags.value = newTags
  console.log('Tags changed:', newTags)
}

// Helper function to get tag type
const getTagType = (tag: string) => {
  if (tag.startsWith('#')) return 'hashtag'
  if (tag.startsWith('@')) return 'mention'
  return 'text'
}

// Computed property to create tag objects for display
const tagObjects = computed(() => {
  return tags.value.map((tag, index) => ({
    id: `tag-${index}`,
    text: tag,
    type: getTagType(tag)
  }))
})
</script>

<template>
  <div class="container mx-auto px-4 py-8 max-w-4xl">
    <div class="space-y-8">
      <!-- Header -->
      <div class="space-y-2">
        <h1 class="text-3xl font-bold">TagsInput Demo</h1>
        <p class="text-muted-foreground">
          Enhanced TagsInput with support for hashtags (#), mentions (@), and regular text
        </p>
      </div>

      <!-- TagsInput Component -->
      <Card>
        <CardHeader>
          <CardTitle>Enhanced TagsInput</CardTitle>
        </CardHeader>
        <CardContent>
          <TagsInput
            v-model="tags"
            @update:model-value="handleTagsChange"
            class="w-full"
          >
            <TagsInputItem
              v-for="(tag, index) in tags"
              :key="`tag-${index}`"
              :value="tag"
            >
              <TagsInputItemText />
              <TagsInputItemDelete />
            </TagsInputItem>

            <TagsInputInput placeholder="Type #hashtag, @mention, or regular text..." />
          </TagsInput>
        </CardContent>
      </Card>

      <!-- Current Tags Display -->
      <Card>
        <CardHeader>
          <CardTitle>Current Tags</CardTitle>
        </CardHeader>
        <CardContent>
          <div v-if="tags.length > 0" class="space-y-4">
            <div class="grid grid-cols-1 gap-3">
              <div v-for="tag in tagObjects" :key="tag.id" class="flex items-center justify-between p-3 border rounded-lg">
                <div class="flex items-center gap-3">
                  <span
                    class="px-2 py-1 rounded text-sm font-medium"
                    :class="{
                      'bg-blue-100 text-blue-800': tag.type === 'hashtag',
                      'bg-green-100 text-green-800': tag.type === 'mention',
                      'bg-gray-100 text-gray-800': tag.type === 'text'
                    }"
                  >
                    {{ tag.text }}
                  </span>
                  <span class="text-xs text-muted-foreground">{{ tag.type }}</span>
                </div>
                <span class="text-xs text-muted-foreground font-mono">{{ tag.id }}</span>
              </div>
            </div>

            <!-- JSON Output -->
            <details class="mt-4">
              <summary class="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                View JSON Output
              </summary>
              <pre class="mt-2 p-3 bg-muted rounded-md text-xs overflow-auto">{{ JSON.stringify(tagObjects, null, 2) }}</pre>
            </details>
          </div>
          <div v-else class="text-muted-foreground text-sm">
            No tags added yet. Try typing in the input above.
          </div>
        </CardContent>
      </Card>

      <!-- Instructions -->
      <Card>
        <CardHeader>
          <CardTitle>How to Use</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <h3 class="font-semibold">Tag Types</h3>
            <ul class="space-y-2 text-sm">
              <li class="flex items-center gap-2">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">#hashtag</span>
                <span>Words starting with # become hashtags (light blue background)</span>
              </li>
              <li class="flex items-center gap-2">
                <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">@mention</span>
                <span>Words starting with @ become mentions (light green background)</span>
              </li>
              <li class="flex items-center gap-2">
                <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs font-medium">regular text</span>
                <span>Regular words become transparent tags</span>
              </li>
            </ul>
            
            <h3 class="font-semibold">Usage</h3>
            <ul class="space-y-2 text-sm">
              <li>• Type words separated by spaces</li>
              <li>• <strong>Hashtags/Mentions:</strong> Space automatically creates tag (no spaces allowed inside)</li>
              <li>• <strong>Regular text:</strong> Space is allowed, press Enter or comma to create tag</li>
              <li>• <strong>Double-click</strong> any tag to edit it in-place</li>
              <li>• <strong>Enter</strong> to save edit, <strong>Escape</strong> to cancel</li>
              <li>• Only hashtags and mentions show delete buttons</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
