<template>
  <main class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-6">
      Import Bookmarks
    </h1>
    <p>
      {{ user && hasUserName(user) ? `Welcome back, ${user.name}!` : 'Please log in to import your bookmarks.' }}
      <br>
      {{ session ? `Session ID: ${session.id}` : 'No active session found.' }}
    </p>
    <Card class="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle class="text-lg font-semibold">
          Upload File
        </CardTitle>
        <CardDescription class="text-sm text-gray-500 dark:text-gray-400">
          Import your bookmarks from an HTML file
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form class="space-y-4" @submit.prevent="handleSubmit">
          <FileInputDragZone
            accept=".html"
            :maxSizeMB="10"
            @change="handleFileInputChange"
            @remove="handleRemoveFile"
            @error="handleFileError"
          />
          <div v-if="selectedFile" class="text-sm text-gray-500 dark:text-gray-400">
            Selected: {{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})
          </div>
          <Button
            type="submit"
            :disabled="!selectedFile || isUploading"
            class="w-full"
          >
            <Loader2 v-if="isUploading" class="h-4 w-4 animate-spin mr-2" />
            <span v-if="isUploading">Uploading...</span>
            <span v-else>Import Bookmarks</span>
          </Button>
        </form>
      </CardContent>
    </Card>

    <div v-if="importResult || importJobId" class="mt-6 w-full max-w-md mx-auto">
      <Card>
        <CardHeader>
          <div class="flex justify-between items-center">
            <CardTitle class="text-lg font-semibold">
              Import Results
            </CardTitle>
            <Button
              v-if="isPolling && jobStatus?.status === 'running'"
              variant="destructive"
              size="sm"
              @click="handleCancelImport"
            >
              Cancel
            </Button>
          </div>
          <CardDescription v-if="importJobId" class="text-sm text-gray-500 dark:text-gray-400">
            Job ID: {{ importJobId }}
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div v-if="isPolling && jobStatus?.progress" class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="capitalize">{{ formatStep(jobStatus.progress.currentStep) }}</span>
              <span>{{ calculateProgress(jobStatus) }}%</span>
            </div>
            <Progress :value="calculateProgress(jobStatus)" />
            <div class="flex justify-between text-sm text-gray-500 dark:text-gray-400">
              <span>{{ jobStatus.progress.processed }} / {{ jobStatus.progress.total }} processed</span>
              <span>{{ jobStatus.progress.imported }} imported</span>
              <span>{{ jobStatus.progress.skipped }} skipped</span>
            </div>
            <p v-if="jobStatus.progress.error" class="text-sm text-red-500 mt-2">
              Error: {{ jobStatus.progress.error }}
            </p>
          </div>

          <div v-else-if="importResult || (jobStatus?.status !== 'pending' && jobStatus?.status !== 'running')" class="space-y-2">
            <p>
              <strong>Status:</strong>
              <Badge :variant="getStatusColor">
                {{ jobStatus?.status || (isSuccessResponse(importResult) ? 'Success' : 'Failed') }}
              </Badge>
            </p>

            <template v-if="resultData">
              <p v-if="isSuccessResponse(importResult)">
                <strong>Imported:</strong> {{ resultData.imported }} bookmarks
              </p>
              <p v-if="isSuccessResponse(importResult) && resultData.skipped">
                <strong>Skipped:</strong> {{ resultData.skipped }} bookmarks
              </p>
              <div v-if="resultData.errors && resultData.errors.length" class="mt-4">
                <p class="font-semibold">
                  Errors:
                </p>
                <ul class="list-disc pl-5 text-sm text-gray-500 dark:text-gray-400">
                  <li v-for="(errorItem, i) in resultData.errors.slice(0, 5)" :key="i">
                    {{ errorItem }}
                  </li>
                  <li v-if="resultData.errors.length > 5">
                    ...and {{ resultData.errors.length - 5 }} more errors
                  </li>
                </ul>
              </div>
            </template>

            <p>
              <strong>Message:</strong> {{ resultData?.message || 'No message available' }}
            </p>

            <div v-if="jobStatus?.status === 'completed' || (isSuccessResponse(importResult) && importResult.success)" class="mt-4">
              <Button to="/bookmarks" class="w-full">
                View my bookmarks
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </main>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeUnmount } from 'vue'
import { useAuth, useSession, getSession } from '@/composables/auth'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Loader2 } from 'lucide-vue-next'
import { FileInputDragZone } from '@/components/ui/file-input'
import { toast } from 'vue-sonner';

// Types
type JobProgress = {
  total: number
  processed: number
  imported: number
  skipped: number
  currentStep: string
  error?: string
  startedAt: Date
  completedAt?: Date
}

type JobResult = {
  success: boolean
  imported: number
  skipped: number
  message: string
  errors?: string[]
}

type JobStatus = {
  jobId: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: JobProgress
  result?: JobResult
}

type ImportSuccessResponse = {
  success: true
  message: string
  imported: number
  skipped: number
  errors?: string[]
  jobId?: string
}

type ImportErrorResponse = {
  error: string
  success: false
  message: string
}

type ImportResponse = ImportSuccessResponse | ImportErrorResponse

// Get session data from betterAuth
// useSession() returns a reactive object that contains session data
const { data, isPending, error,  } = await useSession(useFetch);
const { session, user } = data.value || {};

// Create computed references for user and login status


// State
const selectedFile = ref<File | null>(null)
const isUploading = ref(false)
const isPolling = ref(false)
const isFileValid = ref(true)
const pollingInterval = ref<ReturnType<typeof setInterval> | null>(null)
const importResult = ref<ImportResponse | null>(null)
const importJobId = ref<string | null>(null)
const jobStatus = ref<JobStatus | null>(null)

// Computed
const getStatusColor = computed(() => {
  if (jobStatus.value?.status === 'completed' || (isSuccessResponse(importResult.value) && importResult.value.success)) {
    return 'secondary'
  }
  if (jobStatus.value?.status === 'failed' || (importResult.value && !isSuccessResponse(importResult.value))) {
    return 'destructive'
  }
  return 'default'
})

const resultData = computed(() => {
  if (jobStatus.value?.result) {
    return jobStatus.value.result
  }
  if (isSuccessResponse(importResult.value)) {
    return {
      imported: importResult.value.imported,
      skipped: importResult.value.skipped,
      message: importResult.value.message,
      errors: importResult.value.errors
    }
  }
  return null
})

// Utility
const formatStep = (step: string): string => {
  return step.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}
const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return `${bytes} bytes`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}
const calculateProgress = (status: JobStatus): number => {
  if (!status || !status.progress) return 0
  if (status.progress.total > 0) return Math.round((status.progress.processed / status.progress.total) * 100)
  if (status.status === 'completed') return 100
  if (status.status === 'pending') return 0
  if (status.status === 'running') return 50
  return 0
}

// Event handlers
const handleFileInputChange = (files: File[]) => {
  if (files && files[0] instanceof File) {
    selectedFile.value = files[0]
    isFileValid.value = true
    resetImportState()
  } else {
    selectedFile.value = null
  }
}

const handleRemoveFile = () => {
  selectedFile.value = null
  isFileValid.value = true
  resetImportState()
}

const handleFileError = (error: string) => {
  toast.error(error)
  selectedFile.value = null
  isFileValid.value = false
}

const resetImportState = () => {
  importResult.value = null
  importJobId.value = null
  jobStatus.value = null
  stopPolling()
}

// API interaction
const handleSubmit = async () => {
  if (!selectedFile.value) return
  try {
    isUploading.value = true
    resetImportState()
    const fileContent = await selectedFile.value.text()
    if (!fileContent || !fileContent.includes('<DT>') || !fileContent.includes('<A HREF')) {
      toast.error('The selected file does not appear to be a valid bookmarks HTML file.')
      isFileValid.value = false
      isUploading.value = false
      return
    }
    const response = await $fetch<ImportResponse>('/api/import', { method: 'POST', body: { html: fileContent } })
    if (isSuccessResponse(response)) {
      if ('jobId' in response && response.jobId) {
        importJobId.value = response.jobId
        toast.success('Your bookmarks are being processed in the background.')
        startPolling(response.jobId)
      }
      else {
        importResult.value = response
        toast.success(`Imported ${response.imported} bookmarks.`)
      }
    }
    else {
      toast.error(response.message)
      importResult.value = response
    }
  }
  catch (err: unknown) {
    const errorMsg = err instanceof Error ? err.message : 'An error occurred during import.'
    toast.error(errorMsg)
  }
  finally {
    isUploading.value = false
  }
}

const handleCancelImport = async () => {
  if (!importJobId.value) return
  try {
    await $fetch(`/api/import/cancel/${importJobId.value}`, { method: 'POST' })
    toast.success('The import job has been cancelled.')
    await fetchJobStatus(importJobId.value)
    stopPolling()
  }
  catch (err: unknown) {
    const errorMsg = err instanceof Error ? err.message : 'Could not cancel the import job.'
    toast.error(errorMsg)
  }
}

// Polling
const startPolling = (jobId: string) => {
  isPolling.value = true
  fetchJobStatus(jobId)
  pollingInterval.value = setInterval(() => {
    fetchJobStatus(jobId)
  }, 2000)
}
const stopPolling = () => {
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value)
  }
  pollingInterval.value = null
  isPolling.value = false
}
const fetchJobStatus = async (jobId: string) => {
  try {
    const status = await $fetch<JobStatus>(`/api/import/status/${jobId}`)
    jobStatus.value = status
    if (status.status === 'completed' || status.status === 'failed') {
      stopPolling()
    }
  }
  catch (err: unknown) {
    const errorMsg = err instanceof Error ? err.message : 'Could not check import status.'
    toast.error(errorMsg)
    stopPolling()
  }
}

// Type guard
const isSuccessResponse = (response: ImportResponse | null): response is ImportSuccessResponse => {
  return !!response && response.success === true && typeof (response as ImportSuccessResponse).imported === 'number'
}

// Type guard for user.name
const hasUserName = (u: unknown): u is { name: string } => {
  return typeof u === 'object' && u !== null && 'name' in u && typeof (u as { name: unknown }).name === 'string'
}

onBeforeUnmount(() => {
  stopPolling()
})
</script>
