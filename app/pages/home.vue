<script setup lang="ts">
import { ref, onMounted, computed, shallowRef } from 'vue'
import { toast } from 'vue-sonner'
import { useAsyncState, useThrottleFn } from '@vueuse/core'
import type { Bookmark } from '../types/bookmarks'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Icon } from '@iconify/vue'
import { Skeleton } from '@/components/ui/skeleton'
import BookmarkSave from '@/components/BookmarkSave.vue'
import BookmarkList from '@/components/BookmarkList.vue'

definePageMeta({
  layout: 'sidebar',
})

useHead({
  title: 'Home | fa.vorit.es',
  meta: [
    { name: 'description', content: 'Your personalized bookmarking dashboard' }
  ]
})

// Optimized state with shallow reactivity for large arrays
const bookmarks = shallowRef<Bookmark[]>([])
const isLoading = ref(true)
const showBookmarkSave = ref(false)
const newBookmarkUrl = ref('')
const newBookmarkTitle = ref('')
const isSearchMode = ref(false)
const searchResults = shallowRef<Bookmark[]>([])
const searchCursor = ref<string | null>(null)

// We now use NuxtTime component for date formatting

// Define response type
interface BookmarksResponse {
  success: boolean;
  bookmarks: Bookmark[];
}

// Fetch user's bookmarks
const fetchBookmarks = async () => {
  isLoading.value = true
  
  try {
    const response = await $fetch<BookmarksResponse>('/api/bookmarks')
    
    if (response.success && response.bookmarks) {
      bookmarks.value = response.bookmarks
      // Sort bookmarks by creation date, newest first
      bookmarks.value.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )
    } else {
      toast.error('Failed to fetch bookmarks')
    }
  } catch (error) {
    console.error('Error fetching bookmarks:', error)
    toast.error('Failed to fetch bookmarks')
  } finally {
    isLoading.value = false
  }
}

// Handle new bookmark
const handleAddBookmark = () => {
  newBookmarkUrl.value = ''
  newBookmarkTitle.value = ''
  showBookmarkSave.value = true
}

// Handle bookmark saved
const handleBookmarkSaved = () => {
  showBookmarkSave.value = false
  fetchBookmarks() // Refresh bookmarks list
  toast.success('Bookmark saved successfully')
}

// Search functionality
interface ParsedQuery {
  searchTerm: string
  tags: string[]
  scope: 'user' | 'global'
  scopeUser?: string
  isEmpty: boolean
}

interface SearchResponse {
  success: boolean
  bookmarks: Bookmark[]
  nextCursor?: string | null
  total?: number
}

// Optimized search function with proper async handling
const handleSearch = async (query: ParsedQuery) => {
  isSearchMode.value = true
  isLoading.value = true
  
  try {
    const params = new URLSearchParams()
    if (query.searchTerm) params.append('q', query.searchTerm)
    if (query.tags.length) query.tags.forEach(tag => params.append('tags', tag))
    if (query.scope) params.append('scope', query.scope)
    if (query.scopeUser) params.append('scopeUser', query.scopeUser)
    params.append('limit', '20')

    const response = await $fetch<SearchResponse>(`/api/bookmarks/search?${params.toString()}`)
    
    if (response.success && response.bookmarks) {
      console.log('Search results received:', response.bookmarks.length, response.bookmarks)
      // Force reactivity update for shallowRef
      searchResults.value = [...response.bookmarks]
      searchCursor.value = response.nextCursor || null
      console.log('Search results set:', searchResults.value.length)
    } else {
      console.log('Search failed or empty response:', response)
      searchResults.value = []
      toast.error('Search failed')
    }
  } catch (error) {
    console.error('Search error:', error)
    searchResults.value = []
    toast.error('Search failed')
  } finally {
    isLoading.value = false
  }
}

// Throttled version for performance
const throttledSearch = useThrottleFn(handleSearch, 100)

const handleSearchClear = () => {
  isSearchMode.value = false
  searchResults.value = []
  searchCursor.value = null
}

// Get random placeholder for quick add
const placeholders = [
  'https://github.com/trending',
  'https://news.ycombinator.com',
  'https://medium.com/topics/programming',
  'https://dev.to',
]

// Computed properties
const displayedBookmarks = computed(() => {
  return isSearchMode.value ? searchResults.value : bookmarks.value
})

const displayTitle = computed(() => {
  return isSearchMode.value ? 'Search Results' : 'Recent Bookmarks'
})

const displaySubtitle = computed(() => {
  if (isSearchMode.value) {
    const count = searchResults.value.length
    return `Found ${count} bookmark${count !== 1 ? 's' : ''}`
  }
  return 'Your recently saved bookmarks'
})

// Lifecycle
onMounted(() => {
  fetchBookmarks()
})
</script>

<template>
  <div class="flex flex-col h-full py-6">
    <Search @search="throttledSearch" @clear="handleSearchClear" />
    <Separator class="my-4" />
    <!-- Header with filter -->
    <div class="flex justify-between items-center mb-6 flex-shrink-0">
      <div>
        <h1 class="text-2xl font-semibold">{{ displayTitle }}</h1>
        <p class="text-muted-foreground mt-1">{{ displaySubtitle }}</p>
      </div>
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="handleAddBookmark">
          <Icon icon="lucide:plus" class="w-4 h-4 mr-2" />
          Add Bookmark
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" class="h-8 w-8">
              <Icon icon="lucide:filter" class="h-4 w-4" />
              <span class="sr-only">Filter</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-48">
            <DropdownMenuLabel>Filter By</DropdownMenuLabel>
            <DropdownMenuItem>
              <Icon icon="lucide:clock" class="h-4 w-4 mr-2" />
              Most Recent
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Icon icon="lucide:book-open" class="h-4 w-4 mr-2" />
              Read Later
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Icon icon="lucide:lock" class="h-4 w-4 mr-2" />
              Private Only
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>

    <!-- Loading State with v-memo optimization -->
    <div v-if="isLoading" class="space-y-4 py-4 flex-shrink-0">
      <div 
        v-for="i in 5" 
        :key="i" 
        class="border-b border-gray-200 dark:border-gray-800 py-4 last:border-b-0"
        v-memo="[isLoading, i]"
      >
        <div class="flex justify-between items-start">
          <div class="space-y-2">
            <Skeleton class="h-5 w-60" />
            <Skeleton class="h-4 w-96" />
          </div>
          <Skeleton class="h-4 w-16" />
        </div>
      </div>
    </div>
    
    <!-- Bookmarks List -->
    <div v-else-if="displayedBookmarks.length > 0" class="flex-1 min-h-0">
      <BookmarkList 
        :bookmarks="isSearchMode ? searchResults : undefined"
        :static-mode="isSearchMode"
      />
    </div>
    
    <!-- Empty State -->
    <div v-else class="py-8 text-center flex-shrink-0">
      <Icon 
        :icon="isSearchMode ? 'lucide:search-x' : 'lucide:bookmark-plus'" 
        class="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" 
      />
      <h3 class="text-lg font-medium">
        {{ isSearchMode ? 'No search results' : 'No bookmarks yet' }}
      </h3>
      <p class="text-muted-foreground mt-2 max-w-sm mx-auto">
        {{ isSearchMode 
          ? 'Try adjusting your search terms or clearing the search to see all bookmarks.' 
          : 'Start saving your favorite websites by clicking the "Add Bookmark" button.'
        }}
      </p>
      <Button v-if="!isSearchMode" class="mt-4" @click="handleAddBookmark">
        Add Your First Bookmark
      </Button>
    </div>
    
    <!-- Bookmark Save Dialog -->
    <BookmarkSave
      v-if="showBookmarkSave"
      :url="newBookmarkUrl"
      :title="newBookmarkTitle"
      :open="showBookmarkSave"
      @close="showBookmarkSave = false"
      @saved="handleBookmarkSaved"
    />
  </div>
</template>