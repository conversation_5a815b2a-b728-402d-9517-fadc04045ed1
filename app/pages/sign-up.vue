<script lang="ts" setup>
import { useAuth } from "@/composables/auth"
import { Icon } from "@iconify/vue"
const { signUp } = useAuth()

const firstName = ref("")
const lastName = ref("")
const email = ref("")
const password = ref("")
const isLoading = ref(false)
const errorMessage = ref("")

const handleSignUp = async () => {
	if (isLoading.value) return

	isLoading.value = true
	errorMessage.value = ""

	const user = {
		firstName: firstName.value,
		lastName: lastName.value,
		email: email.value,
		password: password.value,
	}

	try {
		console.log("Starting sign up process with data:", {
			email: user.email,
			name: `${user.firstName} ${user.lastName}`,
		})

		// Make sure we're using the correct callback URL
		const response = await signUp.email({
			email: user.email,
			password: user.password,
			name: `${user.firstName} ${user.lastName}`,
			callbackURL: "/home", // Explicitly set callback URL to /home
			fetchOptions: {
				onError(context) {
					console.error("Sign up onError callback:", context.error)
					errorMessage.value = context.error.message || "Failed to sign up. Please try again."
				},
				onSuccess(result) {
					// This will be called after successful registration
					console.log("Sign up successful, result:", result)
					console.log("Redirecting to /home")

					// Force navigation to home page
					window.location.href = "/home"
				},
			},
		})

		console.log("Sign up API response:", response)
	} catch (error) {
		console.error("Sign up error:", error)
		// Handle error safely without TypeScript errors
		if (error && typeof error === 'object' && 'message' in error) {
			errorMessage.value = String(error.message)
		} else {
			errorMessage.value = "An unexpected error occurred. Please try again."
		}
	} finally {
		isLoading.value = false
	}
}
</script>

<template>
	<div class="h-screen flex justify-center items-center">
		<Card class="mx-auto max-w-sm">
			<CardHeader>
				<CardTitle class="text-xl">Sign Up</CardTitle>
				<CardDescription>
					Enter your information to create an account
				</CardDescription>
			</CardHeader>
			<CardContent>
				<div class="grid gap-4">
					<div class="grid grid-cols-2 gap-4">
						<div class="grid gap-2">
							<Label for="first-name">First name</Label>
							<Input
id="first-name"
v-model="firstName"
placeholder="Max"
required />
						</div>
						<div class="grid gap-2">
							<Label for="last-name">Last name</Label>
							<Input
id="last-name"
v-model="lastName"
placeholder="Robinson"
required />
						</div>
					</div>
					<div class="grid gap-2">
						<Label for="email">Email</Label>
						<Input
id="email"
v-model="email"
type="email"
placeholder="<EMAIL>"
required />
					</div>
					<div class="grid gap-2">
						<Label for="password">Password</Label>
						<Input id="password" v-model="password" type="password" />
					</div>
					<div v-if="errorMessage" class="text-red-500 text-sm mt-2 mb-2">
						{{ errorMessage }}
					</div>
					<Button
						type="button"
						class="w-full"
						:disabled="isLoading"
						@click="handleSignUp"
					>
						<span v-if="isLoading">
							<Icon icon="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
							Creating account...
						</span>
						<span v-else>Create an account</span>
					</Button>
				</div>
				<div class="mt-4 text-center text-sm">
					Already have an account?
					<a href="/sign-in" class="underline"> Sign in </a>
				</div>
			</CardContent>
		</Card>
	</div>
</template>