<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { toast } from 'vue-sonner'
import { 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>List,
  TabsTrigger 
} from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent } from '@/components/ui/card'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { Icon } from '@iconify/vue'

definePageMeta({
  layout: 'sidebar',
})

useHead({
  title: 'Popular | fa.vorit.es',
  meta: [
    { name: 'description', content: 'Discover the most popular bookmarks across the community' }
  ]
})

// Types
type PopularUrl = {
  linkId: string
  url: string
  title: string | null
  description: string | null
  isNsfw: boolean
  saveCount: number
  uniqueUsers: number
  firstSavedAt: string
  lastSavedAt: string
  recentSaveCount: number
  totalVisits: number
  avgReadPercentage: number
  completionCount: number
  tags: string[]
  popularityScore: number
  dailyScore: number
  weeklyScore: number
  monthlyScore: number
  yearlyScore: number
  calculatedAt: string
}

type PopularResponse = {
  success: boolean
  urls: PopularUrl[]
  total: number
}

// State
const popularUrls = ref<PopularUrl[]>([])
const isLoading = ref(true)
const selectedPeriod = ref('all')
const searchQuery = ref('')
const selectedTag = ref('')
const limit = ref(20)

// Computed
const filteredUrls = computed(() => {
  let urls = [...popularUrls.value]
  
  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    urls = urls.filter(url => 
      url.title?.toLowerCase().includes(query) ||
      url.url.toLowerCase().includes(query) ||
      url.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }
  
  // Filter by selected tag
  if (selectedTag.value) {
    urls = urls.filter(url => url.tags.includes(selectedTag.value))
  }
  
  return urls
})

const allTags = computed(() => {
  const tagCounts = new Map<string, number>()
  popularUrls.value.forEach(url => {
    url.tags.forEach(tag => {
      tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
    })
  })
  return Array.from(tagCounts.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 20)
    .map(([tag]) => tag)
})

// Methods
const fetchPopularUrls = async () => {
  isLoading.value = true
  
  try {
    const params = new URLSearchParams({
      period: selectedPeriod.value,
      limit: limit.value.toString()
    })
    
    const response = await $fetch<PopularResponse>(`/api/popular?${params}`)
    
    if (response.success) {
      popularUrls.value = response.urls
    } else {
      toast.error('Failed to fetch popular URLs')
    }
  } catch (error) {
    console.error('Error fetching popular URLs:', error)
    toast.error('Failed to fetch popular URLs')
  } finally {
    isLoading.value = false
  }
}

const handleTagClick = (tag: string) => {
  selectedTag.value = selectedTag.value === tag ? '' : tag
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedTag.value = ''
}

const formatNumber = (num: number) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const getScoreForPeriod = (url: PopularUrl, period: string) => {
  switch (period) {
    case 'daily': return url.dailyScore
    case 'weekly': return url.weeklyScore
    case 'monthly': return url.monthlyScore
    case 'yearly': return url.yearlyScore
    default: return url.popularityScore
  }
}

// Watchers
watch(selectedPeriod, fetchPopularUrls)

// Lifecycle
onMounted(() => {
  fetchPopularUrls()
})
</script>

<template>
  <div class="container py-6 max-w-6xl">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
      <div>
        <h1 class="text-3xl font-bold">Popular Bookmarks</h1>
        <p class="text-muted-foreground mt-1">Discover what the community is bookmarking</p>
      </div>
      
      <!-- Search and Filters -->
      <div class="flex items-center gap-2 w-full sm:w-auto">
        <div class="relative flex-1 sm:w-80">
          <Icon icon="lucide:search" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            v-model="searchQuery"
            placeholder="Search popular bookmarks..."
            class="pl-10"
          />
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <Icon icon="lucide:filter" class="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-48">
            <DropdownMenuLabel>Filter Options</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem @click="clearFilters">
              <Icon icon="lucide:x" class="h-4 w-4 mr-2" />
              Clear Filters
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>

    <!-- Tags Filter -->
    <div v-if="allTags.length > 0" class="mb-6">
      <div class="flex flex-wrap gap-2">
        <Badge
          v-for="tag in allTags"
          :key="tag"
          :variant="selectedTag === tag ? 'default' : 'outline'"
          class="cursor-pointer hover:bg-accent"
          @click="handleTagClick(tag)"
        >
          {{ tag }}
        </Badge>
      </div>
    </div>

    <!-- Period Tabs -->
    <Tabs v-model="selectedPeriod" class="w-full">
      <TabsList class="grid w-full grid-cols-5">
        <TabsTrigger value="all">All Time</TabsTrigger>
        <TabsTrigger value="yearly">This Year</TabsTrigger>
        <TabsTrigger value="monthly">This Month</TabsTrigger>
        <TabsTrigger value="weekly">This Week</TabsTrigger>
        <TabsTrigger value="daily">Today</TabsTrigger>
      </TabsList>

      <TabsContent value="all" class="mt-6">
        <!-- Loading State -->
        <div v-if="isLoading" class="space-y-4">
          <div v-for="i in 6" :key="i">
            <Card>
              <CardContent class="p-6">
                <div class="flex justify-between items-start">
                  <div class="space-y-2 flex-1">
                    <Skeleton class="h-5 w-3/4" />
                    <Skeleton class="h-4 w-full" />
                    <div class="flex gap-2 mt-3">
                      <Skeleton class="h-5 w-16" />
                      <Skeleton class="h-5 w-20" />
                      <Skeleton class="h-5 w-12" />
                    </div>
                  </div>
                  <div class="text-right space-y-1">
                    <Skeleton class="h-6 w-16 ml-auto" />
                    <Skeleton class="h-4 w-24 ml-auto" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <!-- URLs List -->
        <div v-else-if="filteredUrls.length > 0" class="space-y-4">
          <Card
            v-for="(url, index) in filteredUrls"
            :key="url.linkId"
            class="hover:shadow-md transition-shadow duration-200"
          >
            <CardContent class="p-6">
              <div class="flex justify-between items-start gap-4">
                <!-- Content -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center gap-2 mb-2">
                    <span class="text-2xl font-bold text-muted-foreground/60">
                      #{{ index + 1 }}
                    </span>
                    <Badge variant="secondary">
                      Score: {{ getScoreForPeriod(url, selectedPeriod).toFixed(2) }}
                    </Badge>
                  </div>
                  
                  <h3 class="text-lg font-semibold mb-2 line-clamp-2">
                    <a 
                      :href="url.url" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      class="hover:text-primary transition-colors"
                    >
                      {{ url.title || 'Untitled' }}
                    </a>
                  </h3>
                  
                  <p class="text-sm text-muted-foreground mb-3 line-clamp-2">
                    {{ url.description || url.url }}
                  </p>
                  
                  <!-- Tags -->
                  <div v-if="url.tags.length > 0" class="flex flex-wrap gap-1 mb-3">
                    <Badge
                      v-for="tag in url.tags.slice(0, 5)"
                      :key="tag"
                      variant="outline"
                      class="text-xs"
                    >
                      {{ tag }}
                    </Badge>
                    <Badge
                      v-if="url.tags.length > 5"
                      variant="outline"
                      class="text-xs"
                    >
                      +{{ url.tags.length - 5 }}
                    </Badge>
                  </div>
                  
                  <!-- Stats -->
                  <div class="flex items-center gap-4 text-sm text-muted-foreground">
                    <div class="flex items-center gap-1">
                      <Icon icon="lucide:bookmark" class="h-4 w-4" />
                      <span>{{ formatNumber(url.saveCount) }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <Icon icon="lucide:users" class="h-4 w-4" />
                      <span>{{ formatNumber(url.uniqueUsers) }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <Icon icon="lucide:eye" class="h-4 w-4" />
                      <span>{{ formatNumber(url.totalVisits) }}</span>
                    </div>
                    <div v-if="url.avgReadPercentage > 0" class="flex items-center gap-1">
                      <Icon icon="lucide:book-open" class="h-4 w-4" />
                      <span>{{ Math.round(url.avgReadPercentage) }}%</span>
                    </div>
                  </div>
                </div>
                
                <!-- Actions -->
                <div class="flex flex-col items-end gap-2">
                  <Button size="sm" variant="outline" asChild>
                    <a :href="url.url" target="_blank" rel="noopener noreferrer">
                      <Icon icon="lucide:external-link" class="h-4 w-4 mr-2" />
                      Visit
                    </a>
                  </Button>
                  
                  <div class="text-xs text-muted-foreground text-right">
                    <div>{{ formatNumber(url.recentSaveCount) }} recent</div>
                    <div>{{ new Date(url.lastSavedAt).toLocaleDateString() }}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Empty State -->
        <div v-else class="py-12 text-center">
          <Icon icon="lucide:trending-up" class="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />
          <h3 class="text-lg font-medium">No popular content found</h3>
          <p class="text-muted-foreground mt-2">
            {{ searchQuery || selectedTag ? 'Try adjusting your filters' : 'Check back later for trending bookmarks' }}
          </p>
          <Button v-if="searchQuery || selectedTag" variant="outline" class="mt-4" @click="clearFilters">
            Clear Filters
          </Button>
        </div>
      </TabsContent>
      
      <TabsContent value="yearly" class="mt-6">
        <div class="text-center py-8 text-muted-foreground">
          Content for yearly period will be shown here with the same layout as above.
        </div>
      </TabsContent>
      
      <TabsContent value="monthly" class="mt-6">
        <div class="text-center py-8 text-muted-foreground">
          Content for monthly period will be shown here with the same layout as above.
        </div>
      </TabsContent>
      
      <TabsContent value="weekly" class="mt-6">
        <div class="text-center py-8 text-muted-foreground">
          Content for weekly period will be shown here with the same layout as above.
        </div>
      </TabsContent>
      
      <TabsContent value="daily" class="mt-6">
        <div class="text-center py-8 text-muted-foreground">
          Content for daily period will be shown here with the same layout as above.
        </div>
      </TabsContent>
    </Tabs>
  </div>
</template>