<script setup lang="ts">
import { useLocalBookmarksDb } from '@/composables/useLocalBookmarksDb.client'
import BookmarkLocalList from '@/components/BookmarkLocalList.vue'
const { isReady, db } = useLocalBookmarksDb()
</script>

<template>
  <div class="container mx-auto py-8">
    <client-only>
      <pglite-repl v-if="isReady" :pg="db" class="h-12" />
    </client-only>
    <h1 class="text-2xl font-semibold mb-6">My Bookmarks</h1>
    <client-only>
      <template v-if="isReady">
        <BookmarkLocalList />
      </template>
    </client-only>
  </div>
</template>
