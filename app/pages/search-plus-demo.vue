<script setup lang="ts">


// Page metadata
definePageMeta({
  title: 'SearchPlus Demo',
  description: 'Demo of the SearchPlus component with real-time visual cues'
})

// Search results
interface ParsedQuery {
  text: string
  tags: string[]
  scope: 'user' | 'all'
  scopeUser?: string
  isEmpty: boolean
}

const searchResults = ref<ParsedQuery | null>(null)
const isSearching = ref(false)
const searchHistory = ref<ParsedQuery[]>([])

// Handle search events
const handleSearch = (query: ParsedQuery) => {
  console.log('Search query received:', query)
  searchResults.value = query
  isSearching.value = true

  // Add to history (avoid duplicates)
  const isDuplicate = searchHistory.value.some(
    h => JSON.stringify(h) === JSON.stringify(query)
  )
  if (!isDuplicate) {
    searchHistory.value.unshift(query)
    // Keep only last 5 searches
    if (searchHistory.value.length > 5) {
      searchHistory.value = searchHistory.value.slice(0, 5)
    }
  }

  // Simulate API call with 500ms delay
  setTimeout(() => {
    isSearching.value = false
  }, 500)
}

// Handle clear event
const handleClear = () => {
  searchResults.value = null
  isSearching.value = false
}

// Clear search history
const clearHistory = () => {
  searchHistory.value = []
}

// Example searches for testing
const exampleSearches = [
  '@user javascript #tutorial',
  '@all #news react',
  'vue composition api #guide',
  '@user #bookmark typescript',
  '#frontend #css flexbox'
]

// Load example search
const loadExample = (example: string) => {
  // This would ideally set the SearchPlus input value
  // For now, we'll just show it as a suggestion
  console.log('Example search:', example)
}
</script>

<template>
  <div class="container mx-auto px-4 py-8 max-w-6xl">
    <div class="space-y-8">
      <!-- Header -->
      <div class="space-y-2">
        <h1 class="text-3xl font-bold">SearchPlus Demo</h1>
        <p class="text-muted-foreground">
          Try the enhanced search component with visual highlighting as you type
        </p>
      </div>

      <!-- SearchPlus Component -->
      <Card>
        <CardHeader>
          <CardTitle>Search with Visual Cues</CardTitle>
        </CardHeader>
        <CardContent>
          <SearchPlus
            @search="handleSearch"
            @clear="handleClear"
            placeholder="Try typing @user or @all, #tags, and search terms..."
          />
        </CardContent>
      </Card>
      
      <!-- Search Results -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Current Search Result -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              Current Search
              <Badge v-if="isSearching" variant="secondary">Searching...</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div v-if="searchResults" class="space-y-4">
              <div class="grid grid-cols-1 gap-3">
                <div>
                  <span class="text-sm font-medium text-muted-foreground">Scope:</span>
                  <Badge :variant="searchResults.scope === 'user' ? 'default' : 'secondary'" class="ml-2">
                    {{ searchResults.scopeUser ? `@${searchResults.scopeUser}` : `@${searchResults.scope}` }}
                  </Badge>
                </div>

                <div v-if="searchResults.tags.length > 0">
                  <span class="text-sm font-medium text-muted-foreground">Tags:</span>
                  <div class="flex flex-wrap gap-1 mt-1">
                    <Badge v-for="tag in searchResults.tags" :key="tag" variant="outline" class="text-green-600">
                      #{{ tag }}
                    </Badge>
                  </div>
                </div>

                <div v-if="searchResults.text">
                  <span class="text-sm font-medium text-muted-foreground">Text:</span>
                  <p class="mt-1 text-sm">{{ searchResults.text }}</p>
                </div>
              </div>

              <!-- JSON Output -->
              <details class="mt-4">
                <summary class="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                  View JSON Output
                </summary>
                <pre class="mt-2 p-3 bg-muted rounded-md text-xs overflow-auto">{{ JSON.stringify(searchResults, null, 2) }}</pre>
              </details>
            </div>
            <div v-else class="text-muted-foreground text-sm">
              No search performed yet. Try typing in the search box above.
            </div>
          </CardContent>
        </Card>

        <!-- Search History -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              Search History
              <Button v-if="searchHistory.length > 0" variant="outline" size="sm" @click="clearHistory">
                Clear
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div v-if="searchHistory.length > 0" class="space-y-3">
              <div v-for="(search, index) in searchHistory" :key="index" class="p-3 border rounded-md">
                <div class="flex items-center gap-2 mb-2">
                  <Badge :variant="search.scope === 'user' ? 'default' : 'secondary'" size="sm">
                    {{ search.scopeUser ? `@${search.scopeUser}` : `@${search.scope}` }}
                  </Badge>
                  <div v-if="search.tags.length > 0" class="flex gap-1">
                    <Badge v-for="tag in search.tags" :key="tag" variant="outline" size="sm" class="text-green-600">
                      #{{ tag }}
                    </Badge>
                  </div>
                </div>
                <p v-if="search.text" class="text-sm text-muted-foreground">{{ search.text }}</p>
              </div>
            </div>
            <div v-else class="text-muted-foreground text-sm">
              No search history yet.
            </div>
          </CardContent>
        </Card>
      </div>
      
      <!-- Instructions -->
      <Card>
        <CardHeader>
          <CardTitle>How to Use SearchPlus</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <h3 class="font-semibold">Search Syntax</h3>
              <ul class="space-y-2 text-sm">
                <li class="flex items-center gap-2">
                  <Badge variant="outline" class="text-blue-600">@user</Badge>
                  <span>Search your bookmarks only</span>
                </li>
                <li class="flex items-center gap-2">
                  <Badge variant="outline" class="text-blue-600">@all</Badge>
                  <span>Search all public bookmarks</span>
                </li>
                <li class="flex items-center gap-2">
                  <Badge variant="outline" class="text-green-600">#tag</Badge>
                  <span>Filter by specific tags</span>
                </li>
                <li class="flex items-center gap-2">
                  <span class="px-2 py-1 bg-muted rounded text-xs">free text</span>
                  <span>Search content and titles</span>
                </li>
              </ul>
            </div>

            <div class="space-y-4">
              <h3 class="font-semibold">Keyboard Shortcuts</h3>
              <ul class="space-y-2 text-sm">
                <li class="flex items-center gap-2">
                  <kbd class="px-2 py-0.5 bg-muted rounded text-xs">Enter</kbd>
                  <span>Submit search</span>
                </li>
                <li class="flex items-center gap-2">
                  <kbd class="px-2 py-0.5 bg-muted rounded text-xs">Escape</kbd>
                  <span>Clear search</span>
                </li>
                <li class="flex items-center gap-2">
                  <span class="px-2 py-0.5 bg-muted rounded text-xs">Click scope icon</span>
                  <span>Toggle between @user and @all</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Example Searches -->
      <Card>
        <CardHeader>
          <CardTitle>Example Searches</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-2">
            <p class="text-sm text-muted-foreground mb-4">
              Try these example searches to see how the component works:
            </p>
            <div class="flex flex-wrap gap-2">
              <Button
                v-for="example in exampleSearches"
                :key="example"
                variant="outline"
                size="sm"
                @click="loadExample(example)"
                class="text-left"
              >
                {{ example }}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
