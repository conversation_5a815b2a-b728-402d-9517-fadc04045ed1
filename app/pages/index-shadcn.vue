<script setup>
import { ref, watch, onMounted } from 'vue'
import { Award, Bookmark, Calendar, ChevronRight, Clock, ExternalLink, Search, TrendingUp } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'

definePageMeta({
  title: 'Fa.vorit.es',
  description: 'Discover the web',
  layout: 'empty'
})

// State variables
const timeframe = ref('day')

// Sample data - would be replaced with API calls
const featuredBookmark = {
  id: 'featured-1',
  title: 'The Ultimate Guide to Web Development in 2025',
  url: 'https://webdev-trends.com/2025-guide',
  description: 'A comprehensive overview of the latest frameworks, tools, and best practices for modern web development.',
  domain: 'webdev-trends.com',
  tags: ['webdev', 'tutorial', 'javascript', 'frameworks'],
  savedBy: 235,
  favicon: 'https://webdev-trends.com/favicon.ico'
}

const popularBookmarks = ref([
  {
    id: 'bk-1',
    title: 'How AI is Transforming Content Creation',
    url: 'https://techinsights.com/ai-content-revolution',
    domain: 'techinsights.com',
    tags: ['ai', 'content', 'technology'],
    savedBy: 189,
    favicon: 'https://techinsights.com/favicon.ico'
  },
  {
    id: 'bk-2',
    title: 'The Psychology of Productivity: 7 Science-Backed Strategies',
    url: 'https://productivityhub.org/psychology-strategies',
    domain: 'productivityhub.org',
    tags: ['productivity', 'psychology', 'wellbeing'],
    savedBy: 163,
    favicon: 'https://productivityhub.org/favicon.ico'
  },
  {
    id: 'bk-3',
    title: '10 Design Principles Every Developer Should Know',
    url: 'https://uidesignpatterns.com/dev-principles',
    domain: 'uidesignpatterns.com',
    tags: ['design', 'ui', 'development'],
    savedBy: 142,
    favicon: 'https://uidesignpatterns.com/favicon.ico'
  },
  {
    id: 'bk-4',
    title: 'The Future of Remote Work: Trends for 2026 and Beyond',
    url: 'https://futureworktrends.com/remote-2026',
    domain: 'futureworktrends.com',
    tags: ['remote', 'work', 'trends'],
    savedBy: 130,
    favicon: 'https://futureworktrends.com/favicon.ico'
  },
  {
    id: 'bk-5',
    title: 'Understanding TypeScript: A Comprehensive Guide',
    url: 'https://typescriptguides.dev/comprehensive',
    domain: 'typescriptguides.dev',
    tags: ['typescript', 'javascript', 'programming'],
    savedBy: 128,
    favicon: 'https://typescriptguides.dev/favicon.ico'
  }
])

// Mock data for different timeframes
const weeklyPopularBookmarks = [
  {
    id: 'bk-w1',
    title: 'The Evolution of Frontend Development: 2020-2025',
    url: 'https://frontendtrends.dev/evolution-2025',
    domain: 'frontendtrends.dev',
    tags: ['frontend', 'javascript', 'webdev'],
    savedBy: 312,
    favicon: 'https://frontendtrends.dev/favicon.ico'
  },
  {
    id: 'bk-w2',
    title: 'Mastering Modern CSS Techniques',
    url: 'https://cssmastery.dev/modern-techniques',
    domain: 'cssmastery.dev',
    tags: ['css', 'design', 'frontend'],
    savedBy: 287,
    favicon: 'https://cssmastery.dev/favicon.ico'
  }
  // ...other weekly bookmarks
]

const monthlyPopularBookmarks = [
  {
    id: 'bk-m1',
    title: 'Full-Stack Development Roadmap for 2025',
    url: 'https://fullstackworld.com/roadmap-2025',
    domain: 'fullstackworld.com',
    tags: ['fullstack', 'career', 'learning'],
    savedBy: 542,
    favicon: 'https://fullstackworld.com/favicon.ico'
  },
  {
    id: 'bk-m2',
    title: 'The Definitive Guide to PostgreSQL Performance',
    url: 'https://pgperformance.dev/definitive-guide',
    domain: 'pgperformance.dev',
    tags: ['postgresql', 'database', 'performance'],
    savedBy: 498,
    favicon: 'https://pgperformance.dev/favicon.ico'
  }
  // ...other monthly bookmarks
]

// Functions
const fetchPopularBookmarks = async (period = 'day') => {
  try {
    // In a real application, this would be an API call
    // const response = await $fetch(`/api/bookmarks/popular?period=${period}`);
    // popularBookmarks.value = response;

    // For now, just use our mock data
    if (period === 'day') {
      // Keep the current data
    }
    else if (period === 'week') {
      popularBookmarks.value = weeklyPopularBookmarks
    }
    else if (period === 'month') {
      popularBookmarks.value = monthlyPopularBookmarks
    }
  }
  catch (error) {
    console.error('Error fetching popular bookmarks:', error)
  }
}

const fetchFeaturedBookmark = async () => {
  try {
    // In a real application, this would be an API call
    // const response = await $fetch('/api/bookmarks/featured');
    // featuredBookmark.value = response;

    // For now, we're using the mock data defined above
  }
  catch (error) {
    console.error('Error fetching featured bookmark:', error)
  }
}

// Image error handling
function onImageError(event) {
  // Replace broken favicon with a placeholder
  const target = event.target
  target.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>'
}

// Handle timeframe changes
watch(timeframe, (newTimeframe) => {
  fetchPopularBookmarks(newTimeframe)
})

// Initialize data on component mount
onMounted(() => {
  fetchPopularBookmarks('day')
  fetchFeaturedBookmark()
})
</script>

<template>
  <div class="flex flex-col min-h-screen">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-purple-600 to-blue-600 text-white py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="md:flex md:items-center md:justify-between">
          <div class="md:w-1/2">
            <h1 class="text-4xl font-extrabold tracking-tight sm:text-5xl md:text-6xl">
              <span class="block">Organize your web.</span>
              <span class="block text-blue-200">Discover the internet.</span>
            </h1>
            <p class="mt-3 text-lg text-blue-100 sm:mt-5 sm:text-xl md:mt-5 md:text-2xl">
              Save, manage, and discover web bookmarks with our social community.
            </p>
            <div class="mt-8 flex">
              <div class="rounded-md shadow">
                <NuxtLink to="/register">
                  <Button variant="default" size="lg" class="bg-white text-blue-700 hover:bg-blue-50">
                    Get Started
                  </Button>
                </NuxtLink>
              </div>
              <div class="ml-3 rounded-md shadow">
                <NuxtLink to="/login">
                  <Button variant="default" size="lg" class="bg-blue-800 text-white hover:bg-blue-700">
                    Sign In
                  </Button>
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Search Bar -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search class="h-5 w-5 text-gray-400" />
          </div>
          <Input 
            type="text"
            class="block w-full pl-10 pr-3 py-4 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 sm:text-sm"
            placeholder="Search for bookmarks, topics, or users..."
          />
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <main class="bg-gray-50 flex-grow py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Featured Bookmark Section -->
        <section class="mb-12">
          <div class="flex items-center mb-4">
            <Award class="h-6 w-6 text-yellow-500 mr-2" />
            <h2 class="text-2xl font-bold text-gray-900">
              Featured Bookmark
            </h2>
          </div>
          <Card>
            <CardContent class="p-6">
              <div class="flex items-center mb-2">
                <img
                  :src="featuredBookmark.favicon"
                  alt=""
                  class="h-5 w-5 mr-2"
                  @error="onImageError"
                >
                <a
                  :href="featuredBookmark.url"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="text-xl font-bold text-blue-600 hover:underline flex-grow"
                >{{ featuredBookmark.title }}</a>
                <span class="text-gray-500 text-sm flex items-center">
                  <ExternalLink class="h-4 w-4 ml-2" />
                </span>
              </div>
              <p class="text-gray-600 mb-4">
                {{ featuredBookmark.description }}
              </p>
              <div class="flex items-center justify-between">
                <div>
                  <Badge 
                    v-for="tag in featuredBookmark.tags" 
                    :key="tag" 
                    variant="secondary"
                    class="mr-1 bg-blue-50 text-blue-700"
                  >
                    #{{ tag }}
                  </Badge>
                </div>
                <div class="text-gray-500 text-sm flex items-center">
                  <Bookmark class="h-4 w-4 mr-1" />
                  <span>Saved by {{ featuredBookmark.savedBy }} users</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        <!-- For registered users: Recommendations Section (hidden for non-logged-in users) -->
        <section v-if="false" class="mb-12">
          <div class="flex items-center mb-4">
            <TrendingUp class="h-6 w-6 text-green-500 mr-2" />
            <h2 class="text-2xl font-bold text-gray-900">
              Recommended For You
            </h2>
          </div>
          <Card>
            <CardContent class="p-6">
              <p class="text-center text-gray-500 py-8">
                We'll show personalized recommendations based on your interests here once you've logged in and saved some bookmarks.
              </p>
            </CardContent>
          </Card>
        </section>

        <section>
          <ScrollArea class="p-4">
            <div class="flex space-x-4 overflow-x-auto py-4">
              <a
                v-for="tag in ['news', 'technology', 'ai', 'webdev', 'productivity', 'design', 'frontend', 'backend', 'database', 'typescript', 'javascript', 'css', 'html', 'remote', 'career', 'learning', 'trends', 'tutorial', 'frameworks', 'wellbeing']"
                :key="tag"
                href="#"
                class="text-blue-700 hover:bg-blue-100"
              >
                #{{ tag }}
              </a>
            </div>
          </ScrollArea>
        </section>

        <!-- Popular Bookmarks Section -->
        <section>
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <TrendingUp class="h-6 w-6 text-red-500 mr-2" />
              <h2 class="text-2xl font-bold text-gray-900">
                Popular Bookmarks
              </h2>
            </div>
            <div class="flex space-x-1 rounded-lg overflow-hidden border border-gray-300">
              <Button
                :variant="timeframe === 'day' ? 'secondary' : 'ghost'"
                size="sm"
                class="px-3 py-1"
                @click="timeframe = 'day'"
              >
                <Clock class="h-4 w-4 inline mr-1" />
                Today
              </Button>
              <Button
                :variant="timeframe === 'week' ? 'secondary' : 'ghost'"
                size="sm"
                class="px-3 py-1"
                @click="timeframe = 'week'"
              >
                <Calendar class="h-4 w-4 inline mr-1" />
                Week
              </Button>
              <Button
                :variant="timeframe === 'month' ? 'secondary' : 'ghost'"
                size="sm"
                class="px-3 py-1"
                @click="timeframe = 'month'"
              >
                <Calendar class="h-4 w-4 inline mr-1" />
                Month
              </Button>
            </div>
          </div>

          <Card>
            <ul class="divide-y divide-gray-200">
              <li v-for="bookmark in popularBookmarks" :key="bookmark.id" class="p-4 hover:bg-gray-50">
                <div class="flex items-center">
                  <img
                    :src="bookmark.favicon"
                    alt=""
                    class="h-5 w-5 mr-3"
                    @error="onImageError"
                  >
                  <div class="min-w-0 flex-1">
                    <div class="flex items-center justify-between">
                      <a
                        :href="bookmark.url"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="text-md font-medium text-blue-600 hover:underline truncate"
                      >{{ bookmark.title }}</a>
                      <div class="ml-2 flex-shrink-0 flex items-center text-gray-500 text-sm">
                        <Bookmark class="h-4 w-4 mr-1" />
                        <span>{{ bookmark.savedBy }}</span>
                      </div>
                    </div>
                    <div class="mt-2 flex items-center justify-between">
                      <div class="text-sm text-gray-500">
                        {{ bookmark.domain }}
                      </div>
                      <div>
                        <Badge
                          v-for="tag in bookmark.tags"
                          :key="tag"
                          variant="secondary"
                          class="mr-1 bg-blue-50 text-blue-700"
                        >
                          #{{ tag }}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
            <CardFooter class="bg-gray-50 px-4 py-3 border-t border-gray-200">
              <a href="/popular" class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center justify-center">
                View all popular bookmarks
                <ChevronRight class="ml-1 h-4 w-4" />
              </a>
            </CardFooter>
          </Card>
        </section>
      </div>
    </main>

    <!-- Call to Action -->
    <section class="bg-blue-700 text-white">
      <div class="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between">
        <h2 class="text-3xl font-extrabold tracking-tight sm:text-4xl">
          <span class="block">Ready to dive in?</span>
          <span class="block text-blue-300">Start organizing your bookmarks today.</span>
        </h2>
        <div class="mt-8 flex lg:mt-0 lg:flex-shrink-0">
          <div class="inline-flex rounded-md shadow">
            <NuxtLink to="/register">
              <Button variant="default" size="lg" class="bg-white text-blue-700 hover:bg-blue-50">
                Sign up for free
              </Button>
            </NuxtLink>
          </div>
          <div class="ml-3 inline-flex rounded-md shadow">
            <NuxtLink to="/about">
              <Button variant="default" size="lg" class="bg-blue-800 text-white hover:bg-blue-900">
                Learn more
              </Button>
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div>
            <h3 class="text-sm font-semibold uppercase tracking-wider">
              Product
            </h3>
            <ul class="mt-4 space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white">Features</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Pricing</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">FAQ</a></li>
            </ul>
          </div>
          <div>
            <h3 class="text-sm font-semibold uppercase tracking-wider">
              Community
            </h3>
            <ul class="mt-4 space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white">Popular Users</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Trending Tags</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Collections</a></li>
            </ul>
          </div>
          <div>
            <h3 class="text-sm font-semibold uppercase tracking-wider">
              Company
            </h3>
            <ul class="mt-4 space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white">About</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Blog</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Careers</a></li>
            </ul>
          </div>
          <div>
            <h3 class="text-sm font-semibold uppercase tracking-wider">
              Legal
            </h3>
            <ul class="mt-4 space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white">Privacy</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Terms</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white">Cookie Policy</a></li>
            </ul>
          </div>
        </div>
        <div class="mt-8 border-t border-gray-800 pt-8 md:flex md:items-center md:justify-between">
          <div class="flex space-x-6 md:order-2">
            <a href="#" class="text-gray-400 hover:text-white">
              <span class="sr-only">Twitter</span>
              <svg
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
              </svg>
            </a>
            <a href="#" class="text-gray-400 hover:text-white">
              <span class="sr-only">GitHub</span>
              <svg
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd" />
              </svg>
            </a>
          </div>
          <p class="mt-8 text-base text-gray-400 md:mt-0 md:order-1">
            &copy; 2025 fa.vorit.es. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>
