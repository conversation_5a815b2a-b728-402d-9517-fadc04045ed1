<script lang="ts" setup>
import AvatarGroup from '@/components/ui/avatar/AvatarGroup.vue'
import { ref, computed, onMounted } from 'vue'
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from '@/components/ui/avatar'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { toast } from 'vue-sonner'
import { UserPlus, UserMinus, Users } from 'lucide-vue-next'
import { useSession } from '@/composables/auth'

definePageMeta({
  layout: 'sidebar',
  title: 'Network',
  meta: [
    {
      name: 'description',
      content: 'Your network of connections and followers.'
    }
  ]
})

// Types for API responses
interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
}

interface AvatarUser {
  id: string
  name: string
  avatar?: string
}

interface ApiUser {
  id: string
  name: string | null
  username: string | null
  displayUsername: string | null
  image: string | null
  avatarUrl: string | null
  bio: string | null
  role: 'USER' | 'ADMIN'
  banned: boolean
  onboarded: boolean
  createdAt: string
  updatedAt: string | null
}

// Get session data from betterAuth - following working pattern from import.vue
const { data: sessionData, isPending, error } = await useSession(useFetch);
const { session, user } = sessionData.value || {};

// State
const currentUser = ref<{ id: string } | null>(user ? { id: user.id } : null)
const allUsers = ref<ApiUser[]>([])
const following = ref<ApiUser[]>([])
const followers = ref<ApiUser[]>([])
const feed = ref<any[]>([])
const isLoading = ref(true)
const followingMap = ref<Record<string, boolean>>({})

// Convert API users to avatar users format
const avatarUsers = computed<AvatarUser[]>(() => {
  return following.value.map(user => ({
    id: user.id,
    name: user.name || user.username || 'Unknown User',
    avatar: user.avatarUrl || user.image || undefined
  }))
})

// Get display name for a user
const getDisplayName = (user: ApiUser): string => {
  return user.displayUsername || user.username || user.name || 'Unknown User'
}

// Get avatar for a user
const getAvatar = (user: ApiUser): string | undefined => {
  return user.avatarUrl || user.image || undefined
}

// Get initials for a user
const getInitials = (user: ApiUser): string => {
  const name = user.name || user.username || 'Unknown'
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2)
}


// Fetch all users
const fetchAllUsers = async () => {
  try {
    console.log('Fetching users from /api/users...')
    const response = await $fetch<ApiResponse<ApiUser[]>>('/api/users')
    console.log('Users API response:', response)
    if (response.success) {
      allUsers.value = response.data
      console.log('Set allUsers to:', allUsers.value)
    } else {
      console.error('API returned success: false')
    }
  } catch (error) {
    console.error('Error fetching users:', error)
    toast.error('Failed to load users')
  }
}

// Fetch following users
const fetchFollowing = async () => {
  try {
    console.log('Fetching following users...')
    const response = await $fetch<ApiResponse<ApiUser[]>>('/api/following')
    console.log('Following API response:', response)
    if (response.success) {
      following.value = response.data
      console.log('Following users:', following.value)

      // Update following map
      followingMap.value = {}
      following.value.forEach(user => {
        followingMap.value[user.id] = true
      })
      console.log('Following map:', followingMap.value)
    }
  } catch (error) {
    console.error('Error fetching following:', error)
    toast.error('Failed to load following users')
  }
}

// Fetch followers
const fetchFollowers = async () => {
  try {
    const response = await $fetch<ApiResponse<ApiUser[]>>('/api/followers')
    if (response.success) {
      followers.value = response.data
    }
  } catch (error) {
    console.error('Error fetching followers:', error)
    toast.error('Failed to load followers')
  }
}

// Fetch feed
const fetchFeed = async () => {
  try {
    console.log('Fetching feed...')
    const response = await $fetch<ApiResponse<any[]>>('/api/feed')
    console.log('Feed API response:', response)
    if (response.success) {
      feed.value = response.data
      console.log('Feed data:', feed.value)
    }
  } catch (error) {
    console.error('Error fetching feed:', error)
    toast.error('Failed to load feed')
  }
}

// Follow a user
const handleFollow = async (userId: string) => {
  console.log('handleFollow called with userId:', userId)
  console.log('currentUser:', currentUser.value)
  
  if (!currentUser.value) {
    console.log('No current user, returning')
    toast.error('You must be logged in to follow users')
    return
  }

  // Prevent following yourself
  if (currentUser.value.id === userId) {
    console.log('Trying to follow self, preventing')
    toast.error('You cannot follow yourself')
    return
  }

  try {
    console.log('Making follow API call...')
    const response = await $fetch<ApiResponse<any>>(`/api/followers/${userId}`, {
      method: 'PUT'
    })
    console.log('Follow API response:', response)

    if (response.success) {
      // Update following map
      followingMap.value[userId] = true
      toast.success('User followed successfully')

      // Refresh following list and feed
      await Promise.all([
        fetchFollowing(),
        fetchFeed()
      ])
    }
  } catch (error: any) {
    console.error('Error following user:', error)
    toast.error(error.message || 'Failed to follow user')
  }
}

// Unfollow a user
const handleUnfollow = async (userId: string) => {
  console.log('handleUnfollow called with userId:', userId)
  console.log('currentUser:', currentUser.value)
  
  if (!currentUser.value) {
    console.log('No current user, returning')
    toast.error('You must be logged in to unfollow users')
    return
  }

  try {
    console.log('Making unfollow API call...')
    const response = await $fetch<ApiResponse<any>>(`/api/followers/${userId}`, {
      method: 'DELETE'
    })
    console.log('Unfollow API response:', response)

    if (response.success) {
      // Update following map
      delete followingMap.value[userId]
      toast.success('User unfollowed successfully')

      // Refresh following list and feed
      await Promise.all([
        fetchFollowing(),
        fetchFeed()
      ])
    }
  } catch (error: any) {
    console.error('Error unfollowing user:', error)
    toast.error(error.message || 'Failed to unfollow user')
  }
}

// Check if a user is the current user
const isCurrentUser = (userId: string): boolean => {
  return currentUser.value?.id === userId
}

// Check if the current user is following a user
const isFollowing = (userId: string): boolean => {
  const result = !!followingMap.value[userId]
  console.log(`isFollowing(${userId}):`, result, 'followingMap:', followingMap.value)
  return result
}

// Initialize data
onMounted(async () => {
  isLoading.value = true
  console.log('Network page mounted, starting data fetch...')
  console.log('Session user from useSession:', user)
  console.log('Current user:', currentUser.value)

  // Always try to fetch users, even if session is unclear
  await Promise.all([
    fetchAllUsers(),
    fetchFollowing(),
    fetchFollowers(),
    fetchFeed()
  ])

  isLoading.value = false
  console.log('Network page data loading complete')
})
</script>

<template>
  <div class="container mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6">Your Network</h1>

    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>

    <div v-else class="space-y-8">
      <!-- Feed Section -->
      <div class="bg-card rounded-lg p-6 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">Recent Activity</h2>
        <div v-if="feed.length > 0" class="space-y-4">
          <div v-for="bookmark in feed" :key="bookmark.id" class="border-b border-gray-200 dark:border-gray-800 pb-4 last:border-b-0">
            <div class="flex items-start gap-3">
              <Avatar class="w-8 h-8">
                <AvatarImage
                  v-if="bookmark.user.avatarUrl || bookmark.user.image"
                  :src="bookmark.user.avatarUrl || bookmark.user.image"
                  :alt="bookmark.user.name"
                />
                <AvatarFallback>{{ getInitials(bookmark.user) }}</AvatarFallback>
              </Avatar>
              <div class="flex-1 min-w-0">
                <div class="flex items-center gap-2 mb-1">
                  <span class="font-medium text-sm">{{ getDisplayName(bookmark.user) }}</span>
                  <span class="text-xs text-muted-foreground">saved</span>
                  <NuxtTime :datetime="bookmark.createdAt" class="text-xs text-muted-foreground" />
                </div>
                <h3 class="font-medium text-sm mb-1">{{ bookmark.userTitle || bookmark.link?.title || 'Untitled' }}</h3>
                <p v-if="bookmark.notes" class="text-xs text-muted-foreground line-clamp-2">{{ bookmark.notes }}</p>
                <div v-if="bookmark.tagNames && bookmark.tagNames.length > 0" class="flex flex-wrap gap-1 mt-2">
                  <Badge v-for="tagName in bookmark.tagNames" :key="tagName" variant="secondary" class="text-xs">
                    {{ tagName }}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-muted-foreground">
          <p>No recent activity from people you follow.</p>
          <p class="text-xs mt-1">Follow some users to see their bookmarks here!</p>
        </div>
      </div>

      <!-- Following Section -->
      <div class="bg-card rounded-lg p-6 shadow-sm">
        <h2 class="text-xl font-semibold mb-4">Following</h2>
        <div v-if="following.length > 0" class="bg-muted rounded-full p-2 inline-block">
          <AvatarGroup
            :users="avatarUsers"
            :limit="4"
          />
        </div>
        <p class="text-sm text-muted-foreground mt-2">
          You are following {{ following.length }} users.
        </p>
      </div>

      <!-- All Users Section -->
      <div class="bg-card rounded-lg p-6 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">All Users</h2>
          <Users class="h-5 w-5 text-muted-foreground" />
        </div>

        <div v-if="allUsers.length === 0" class="text-center py-8 text-muted-foreground">
          No users found.
        </div>

        <div v-else class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card v-for="user in allUsers" :key="user.id" class="overflow-hidden">
            <CardHeader class="pb-2">
              <div class="flex items-center gap-3">
                <Avatar>
                  <AvatarImage
                    v-if="getAvatar(user)"
                    :src="getAvatar(user) || ''"
                    :alt="getDisplayName(user)"
                  />
                  <AvatarFallback>{{ getInitials(user) }}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle class="text-base">{{ getDisplayName(user) }}</CardTitle>
                  <CardDescription v-if="user.username" class="text-xs">
                    @{{ user.username }}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>

            <CardContent class="pb-2">
              <p v-if="user.bio" class="text-sm text-muted-foreground line-clamp-2">
                {{ user.bio }}
              </p>
              <p v-else class="text-sm text-muted-foreground italic">
                No bio provided
              </p>
            </CardContent>

            <CardFooter class="flex justify-end pt-0">
              <Button
                v-if="isCurrentUser(user.id)"
                variant="outline"
                size="sm"
                disabled
              >
                You
              </Button>
              <Button
                v-else-if="isFollowing(user.id)"
                variant="outline"
                size="sm"
                @click="handleUnfollow(user.id)"
              >
                <UserMinus class="h-4 w-4 mr-1" />
                Unfollow
              </Button>
              <Button
                v-else
                variant="default"
                size="sm"
                @click="handleFollow(user.id)"
              >
                <UserPlus class="h-4 w-4 mr-1" />
                Follow
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>