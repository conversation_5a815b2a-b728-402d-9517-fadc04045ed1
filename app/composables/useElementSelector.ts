import { ref, watch, onUnmounted } from 'vue';

/**
 * Composable for selecting HTML elements on the page.
 * @param containerRef A ref to the container element that should be excluded from selection
 */
export function useElementSelector(containerRef: Ref<HTMLElement | null> = ref(null)) {
  const selectedElement = ref<HTMLElement | null>(null);
  const isSelectingElement = ref(false);
  const highlightedElement = ref<HTMLElement | null>(null);
  
  // Start the element selection process
  const startSelecting = () => {
    if (isSelectingElement.value) return;
    
    isSelectingElement.value = true;
    document.body.style.cursor = 'crosshair';
    
    // Add temporary outline style
    const styleEl = document.createElement('style');
    styleEl.id = 'companion-highlight-style';
    styleEl.textContent = `
      .companion-highlight-outline {
        outline: 2px dotted #3b82f6 !important;
        outline-offset: 2px !important;
      }
    `;
    document.head.appendChild(styleEl);
    
    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('click', handleElementClick);
  };
  
  // Handle mouse movement during element selection
  const handleMouseMove = (e: MouseEvent) => {
    // Skip highlighting if target is the container
    if (containerRef.value && containerRef.value.contains(e.target as Node)) return;
    
    // Remove previous highlight
    if (highlightedElement.value) {
      highlightedElement.value.classList.remove('companion-highlight-outline');
      highlightedElement.value = null;
    }
    
    // Add highlight to current element
    const target = e.target as HTMLElement;
    if (target && target !== document.body) {
      highlightedElement.value = target;
      target.classList.add('companion-highlight-outline');
    }
  };
  
  // Handle click on an element during selection
  const handleElementClick = (e: MouseEvent) => {
    // Skip selection if target is the container
    if (containerRef.value && containerRef.value.contains(e.target as Node)) return;
    
    // Select the element
    const target = e.target as HTMLElement;
    if (target && target !== document.body) {
      selectedElement.value = target;
      console.log('Element selected:', target.tagName, target.className);
    }
    
    // Exit selection mode
    stopSelecting();
    e.preventDefault();
    e.stopPropagation();
  };
  
  // Stop the element selection process
  const stopSelecting = () => {
    isSelectingElement.value = false;
    document.body.style.cursor = '';
    
    // Remove highlight from current element
    if (highlightedElement.value) {
      highlightedElement.value.classList.remove('companion-highlight-outline');
      highlightedElement.value = null;
    }
    
    // Remove style element
    const styleEl = document.getElementById('companion-highlight-style');
    if (styleEl) styleEl.remove();
    
    // Remove event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('click', handleElementClick);
  };
  
  // Make sure we clean up when unmounted
  onUnmounted(() => {
    if (isSelectingElement.value) {
      stopSelecting();
    }
  });
  
  return {
    selectedElement,
    isSelectingElement,
    startSelecting,
    stopSelecting
  };
}
