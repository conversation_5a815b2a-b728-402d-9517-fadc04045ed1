export interface ReadTimeEstimation {
  minutes: number;
  roundedMinutes: number;
  displayTime: string;
  wordCount: number;
}

const DEFAULT_WORDS_PER_MINUTE = 225;

/**
 * Estimates the reading time for a given text.
 * @param textContent The text content to estimate the reading time for.
 * @param wordsPerMinute The average reading speed in words per minute. Defaults to 225.
 * @returns An object containing the estimated minutes, rounded minutes, display string, and word count.
 */
export function estimateReadTime(
  textContent: string,
  wordsPerMinute: number = DEFAULT_WORDS_PER_MINUTE,
): ReadTimeEstimation {
  if (!textContent || textContent.trim() === '') {
    return {
      minutes: 0,
      roundedMinutes: 0,
      displayTime: '0 min read',
      wordCount: 0,
    };
  }

  // Remove extra whitespace and count words
  const words = textContent.trim().split(/\s+/);
  const wordCount = words.length;

  if (wordCount === 0) {
    return {
      minutes: 0,
      roundedMinutes: 0,
      displayTime: '0 min read',
      wordCount: 0,
    };
  }

  const minutes = wordCount / wordsPerMinute;
  const roundedMinutes = Math.max(1, Math.round(minutes)); // Ensure at least 1 min for very short texts if there's content

  return {
    minutes,
    roundedMinutes,
    displayTime: `${roundedMinutes} min read`,
    wordCount,
  };
}

/**
 * Composable function to use the read time estimator.
 * Provides a reactive way to estimate read time.
 */
export function useReadTimeEstimator() {
  return {
    estimateReadTime,
  };
}
