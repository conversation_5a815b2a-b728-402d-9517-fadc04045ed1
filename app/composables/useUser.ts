import { computed, readonly } from 'vue'
import { useSession } from './auth'

/**
 * Composable for accessing user data and computed user properties
 */
export const useUser = () => {
  const session = useSession()

  const user = computed(() => session.value?.data?.user)
  
  const isLoggedIn = computed(() => !!user.value)

  const userInitials = computed(() => {
    if (!user.value?.name) return '?'
    
    const names = user.value.name.split(' ').filter(Boolean)
    if (names.length === 0) return '?'
    if (names.length === 1) {
      return names[0]?.charAt(0).toUpperCase() || '?'
    }
    const first = names[0]?.charAt(0) || ''
    const last = names[names.length - 1]?.charAt(0) || ''
    return `${first}${last}`.toUpperCase()
  })

  const avatarUrl = computed(() => {
    return user.value?.image || null
  })

  const displayName = computed(() => {
    return user.value?.name || user.value?.email || 'User'
  })

  return {
    user: readonly(user),
    isLoggedIn: readonly(isLoggedIn),
    userInitials: readonly(userInitials),
    avatarUrl: readonly(avatarUrl),
    displayName: readonly(displayName),
  }
}
