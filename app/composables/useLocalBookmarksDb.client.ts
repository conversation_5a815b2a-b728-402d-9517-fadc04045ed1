import { PGlite, type PGliteInterfaceExtensions } from '@electric-sql/pglite'
import { live } from '@electric-sql/pglite/live'
import { vector } from '@electric-sql/pglite/vector'
import { makePGliteDependencyInjector } from '@electric-sql/pglite-vue'
import { ref, onMounted, shallowRef } from 'vue'

export type LocalBookmark = {
  id: string
  url: string
  title: string
  notes?: string | null
  tldr?: string
  createdAt: string
  tags: string[]
  isPrivate: boolean
  isReadLater: boolean
}

const DB_URL = 'idb://favorites-local'
const BOOKMARKS_TABLE = `CREATE TABLE IF NOT EXISTS bookmarks (
  id TEXT PRIMARY KEY,
  url TEXT NOT NULL,
  title TEXT NOT NULL,
  notes TEXT,
  tldr TEXT,
  createdAt TEXT NOT NULL,
  tags TEXT[],
  isPrivate BOOLEAN NOT NULL,
  isReadLater BOOLEAN NOT NULL
)`

const { providePGlite } = makePGliteDependencyInjector<
  PGlite & PGliteInterfaceExtensions<{ live: typeof live; vector: typeof vector }>
>()

export function useLocalBookmarksDb() {
  // Do not run in SSR
  if (process.server) {
    const db = ref<PGlite | null>(null)
    const isReady = ref(false)
    return { db, isReady }
  }
  // Initialize PGlite instance synchronously with live and vector extensions
  const p = new PGlite(DB_URL, { extensions: { live, vector } })
  // Provide typed PGlite instance in setup
  providePGlite(p as unknown as PGlite & PGliteInterfaceExtensions<{ live: typeof live; vector: typeof vector }>)
  const db = shallowRef(p)
  const isReady = ref(false)

  onMounted(async () => {
    // Create SQL extension and table
    await p.exec('CREATE EXTENSION IF NOT EXISTS vector;')
    await p.exec(BOOKMARKS_TABLE)

    // Fetch presentational data from server API
    const { bookmarks } = await $fetch<{ bookmarks: LocalBookmark[] }>('/api/bookmarks')
    if (bookmarks.length) {
      const inserts = bookmarks.map(b => {
        const tagsArr = `'{${b.tags.map(t => `"${t}"`).join(',')}}'`
        return `INSERT INTO bookmarks(
          id, url, title, notes, tldr, createdAt, tags, isPrivate, isReadLater
        ) VALUES (
          '${b.id}', '${b.url}', '${b.title}', ${b.notes ? `'${b.notes}'` : 'NULL'},
          ${b.tldr ? `'${b.tldr}'` : 'NULL'},
          '${b.createdAt}', ${tagsArr}, ${b.isPrivate}, ${b.isReadLater}
        ) ON CONFLICT (id) DO UPDATE SET
          url = EXCLUDED.url,
          title = EXCLUDED.title,
          notes = EXCLUDED.notes,
          tldr = EXCLUDED.tldr,
          createdAt = EXCLUDED.createdAt,
          tags = EXCLUDED.tags,
          isPrivate = EXCLUDED.isPrivate,
          isReadLater = EXCLUDED.isReadLater;`
      }).join('\n')
      await p.exec(inserts)
    }

    isReady.value = true
  })

  return { db, isReady }
}