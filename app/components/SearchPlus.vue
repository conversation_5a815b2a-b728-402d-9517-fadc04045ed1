<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { Icon } from '@iconify/vue'
import { cn } from '@/lib/utils'

// Props and emits
interface SearchPlusProps {
  placeholder?: string
  defaultScope?: 'user' | 'all'
  class?: string
}

const props = withDefaults(defineProps<SearchPlusProps>(), {
  placeholder: 'Search bookmarks... Try: #tag @user your search terms',
  defaultScope: 'user',
  class: ''
})

// Query parsing interface
interface ParsedQuery {
  text: string
  tags: string[]
  scope: 'user' | 'all'
  scopeUser?: string
  isEmpty: boolean
}

const emit = defineEmits<{
  search: [query: ParsedQuery]
  clear: []
}>()

// State
const searchInput = ref<HTMLInputElement | null>(null)
const searchQuery = ref('')
const hasFocus = ref(false)

// Parse the query into structured format
const parseQuery = computed(() => {
  const query = searchQuery.value.trim()

  if (!query) {
    return {
      text: '',
      tags: [],
      scope: props.defaultScope,
      isEmpty: true
    }
  }

  // Match @scope, #tags, and remaining words
  const scopeMatches = query.match(/@(\w+)/g) || []
  const tagMatches = query.match(/#(\w+)/g) || []

  let scopeUser: string | undefined
  let scope: 'user' | 'all' = props.defaultScope

  // Process scope
  if (scopeMatches.length > 0) {
    const firstScope = scopeMatches[0]
    if (firstScope) {
      const scopeValue = firstScope.substring(1).toLowerCase()
      if (scopeValue === 'all') {
        scope = 'all'
      } else if (scopeValue !== 'user') {
        scope = 'all'
        scopeUser = scopeValue
      } else {
        scope = 'user'
      }
    }
  }

  // Process tags (remove # prefix and lowercase)
  const tags = tagMatches.map((tag: string) => tag.substring(1).toLowerCase())

  // Process text (remove scopes and tags)
  let text = query
    .replace(/@\w+/g, '')
    .replace(/#\w+/g, '')
    .trim()

  return {
    text,
    tags: tags,
    scope,
    scopeUser,
    isEmpty: !text && tags.length === 0
  }
})

// Computed properties for UI
const isEmpty = computed(() => !searchQuery.value.trim())

// Tokenize text for visual highlighting
interface HighlightToken {
  text: string
  class: string
  highlight: boolean
}

const highlightTokens = computed((): HighlightToken[] => {
  const query = searchQuery.value
  if (!query) return []

  const tokens: HighlightToken[] = []
  const regex = /(@\w*)|(#\w*)|([^@#\s]+)|(\s+)/g
  let match

  while ((match = regex.exec(query)) !== null) {
    const [, scopeMatch, tagMatch, textMatch, spaceMatch] = match

    if (scopeMatch) {
      tokens.push({
        text: scopeMatch,
        class: 'text-blue-600 dark:text-blue-400 font-medium',
        highlight: true
      })
    } else if (tagMatch) {
      tokens.push({
        text: tagMatch,
        class: 'text-green-600 dark:text-green-400 font-medium',
        highlight: true
      })
    } else if (textMatch || spaceMatch) {
      tokens.push({
        text: (textMatch || spaceMatch) as string,
        class: 'text-foreground',
        highlight: false
      })
    }
  }

  return tokens
})



// Keyboard event handling
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault()
    if (!isEmpty.value) {
      emit('search', parseQuery.value)
    }
  } else if (event.key === 'Escape') {
    clearSearch()
  }
}

// Clear the search
const clearSearch = () => {
  searchQuery.value = ''
  emit('clear')
  searchInput.value?.focus()
}

// Toggle scope between user and all
const toggleScope = () => {
  if (searchQuery.value.includes('@all')) {
    searchQuery.value = searchQuery.value.replace('@all', '@user')
  } else if (searchQuery.value.includes('@user')) {
    searchQuery.value = searchQuery.value.replace('@user', '@all')
  } else if (searchQuery.value.match(/@\w+/)) {
    // If there's another @scope, replace with @user
    searchQuery.value = searchQuery.value.replace(/@\w+/, '@user')
  } else {
    // Insert @user at the beginning
    searchQuery.value = `@${props.defaultScope === 'user' ? 'all' : 'user'} ${searchQuery.value}`.trim()
  }
}

// Search debouncing
const debouncedSearch = useDebounceFn(() => {
  if (!isEmpty.value) {
    emit('search', parseQuery.value)
    console.log('Search query:', parseQuery.value)
  }
}, 200)

// Watch for changes and emit search
watch(searchQuery, (newValue) => {
  if (!newValue.trim()) {
    emit('clear')
  } else {
    debouncedSearch()
  }
})

// Focus handling
const handleFocus = () => {
  hasFocus.value = true
}

const handleBlur = () => {
  hasFocus.value = false
}



// Exposed methods
defineExpose({
  focus: () => {
    searchInput.value?.focus()
  },
  clear: clearSearch
})


</script>

<template>
  <div :class="cn('search-plus space-y-2', props.class)">
    <!-- Search Input Container -->
    <div
      class="relative flex items-center min-h-12 w-full bg-background border border-input rounded-md shadow-xs overflow-hidden"
      :class="[hasFocus ? 'ring-2 ring-ring/50 border-ring' : '']"
    >
      <!-- Scope Toggle Icon -->
      <button
        type="button"
        class="flex items-center justify-center px-3 h-full hover:bg-muted/50 transition-colors shrink-0"
        :title="parseQuery.scope === 'user' ? 'Searching your bookmarks' : 'Searching all bookmarks'"
        aria-label="Toggle search scope"
        @click="toggleScope"
      >
        <Icon
          :icon="parseQuery.scope === 'user' ? 'lucide:user' : 'lucide:globe'"
          class="size-4 text-muted-foreground"
        />
      </button>

      <!-- Input with Highlighting -->
      <div class="relative flex-1">
        <!-- Highlighted text overlay -->
        <div
          class="absolute inset-0 px-3 py-2 pointer-events-none select-none overflow-hidden whitespace-nowrap flex items-center"
          aria-hidden="true"
        >
          <template v-for="(token, index) in highlightTokens" :key="index">
            <span :class="token.class">{{ token.text }}</span>
          </template>
        </div>

        <!-- Input with matching font and transparent text -->
        <input
          ref="searchInput"
          v-model="searchQuery"
          type="text"
          :placeholder="props.placeholder"
          class="w-full px-3 py-2 bg-transparent border-0 outline-none relative z-10"
          :class="searchQuery ? 'text-transparent caret-blue-600' : 'text-foreground'"
          @keydown="handleKeyDown"
          @focus="handleFocus"
          @blur="handleBlur"
        >
      </div>

      <!-- Clear Button -->
      <button
        v-if="!isEmpty"
        type="button"
        class="flex items-center justify-center px-3 h-full hover:bg-muted/50 transition-colors shrink-0"
        title="Clear search"
        aria-label="Clear search"
        @click="clearSearch"
      >
        <Icon icon="lucide:x" class="size-4 text-muted-foreground" />
      </button>
    </div>

    <!-- Dynamic Help Text -->
    <div v-if="isEmpty" class="flex flex-wrap gap-2 text-xs text-muted-foreground">
      <div class="flex items-center">
        <span class="text-blue-600 dark:text-blue-400 mr-1">@user</span> or
        <span class="text-blue-600 dark:text-blue-400 mx-1">@all</span> for scope
      </div>
      <div class="flex items-center">
        <span class="text-green-600 dark:text-green-400 mr-1">#tag</span> for filtering by tags
      </div>
      <span>Everything else for text search</span>
    </div>

    <!-- Real-time Query Breakdown -->
    <div v-else-if="!parseQuery.isEmpty" class="flex flex-wrap gap-2 text-xs">
      <div v-if="parseQuery.scope" class="flex items-center">
        <span class="text-blue-600 dark:text-blue-400 font-medium">
          {{ parseQuery.scopeUser ? `@${parseQuery.scopeUser}` : `@${parseQuery.scope}` }}
        </span>
        <span class="text-muted-foreground ml-1">scope</span>
      </div>
      <div v-if="parseQuery.tags.length > 0" class="flex items-center gap-1">
        <span
          v-for="tag in parseQuery.tags"
          :key="tag"
          class="text-green-600 dark:text-green-400 font-medium"
        >
          #{{ tag }}
        </span>
        <span class="text-muted-foreground">tags</span>
      </div>
      <div v-if="parseQuery.text" class="flex items-center">
        <span class="text-foreground font-medium">"{{ parseQuery.text }}"</span>
        <span class="text-muted-foreground ml-1">text</span>
      </div>
    </div>
  </div>
</template>


