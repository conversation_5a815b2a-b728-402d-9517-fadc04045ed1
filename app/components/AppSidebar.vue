<script setup lang="ts">
import { type SidebarProps } from '@/components/ui/sidebar'

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  useSidebar,
} from '@/components/ui/sidebar'

import {
  Bookmark,
  Flame, 
  Home,
  Users,
  Settings,
} from 'lucide-vue-next'

import { Icon } from '@iconify/vue'
import UserAvatar from '@/components/UserAvatar.vue'
import { useUser } from '@/composables/useUser'

const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: 'icon',
})

const { state } = useSidebar()
const { displayName } = useUser()

// Menu items for simplified navigation
const menuItems = [
  {
    title: 'Home',
    url: '/home',
    icon: Home,
  },
  {
    title: 'Popular',
    url: '/popular',
    icon: Flame,
  },
  {
    title: 'Network',
    url: '/network',
    icon: Users,
  },
  {
    title: 'Settings',
    url: '/settings',
    icon: Settings,
  },
  {
    title: 'Shuffle',
    url: '#',
    icon: 'iconify:lucide:shuffle',
  },
  {
    title: 'Tags',
    url: '#',
    icon: 'iconify:lucide:hash',
  },
  {
    title: 'Bookshelf',
    url: '#',
    icon: 'iconify:lucide:library-big',
  },
  {
    title: 'Add',
    url: '#',
    icon: 'iconify:lucide:plus',
  },
]

// Use real user session data instead of hardcoded sample data
</script>

<template>
  <Sidebar v-bind="props" collapsible="icon" variant="floating">
    <SidebarHeader>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton
            size="lg"
            :tooltip="state === 'collapsed' ? 'fa.vorit.es' : undefined"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground shrink-0">
              <Bookmark class="size-4" />
            </div>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-medium">fa.vorit.es</span>
              <span class="truncate text-xs">Bookmark Manager</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>
    <SidebarContent>
      <SidebarMenu>
        <SidebarMenuItem v-for="item in menuItems" :key="item.title">
          <SidebarMenuButton 
            as-child 
            :tooltip="state === 'collapsed' ? item.title : undefined"
          >
            <a :href="item.url" class="flex items-center gap-2">
              <div class="flex aspect-square size-8 items-center justify-center shrink-0">
                <component :is="item.icon" v-if="typeof item.icon === 'function'" class="size-4" />
                <Icon v-else-if="typeof item.icon === 'string' && item.icon.startsWith('iconify:')" :icon="item.icon.replace('iconify:', '')" class="size-4" />
              </div>
              <span>{{ item.title }}</span>
            </a>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarContent>
    <SidebarFooter>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton 
            size="lg"
            :tooltip="state === 'collapsed' ? displayName : undefined"
          >
            <UserAvatar size="sm" :show-name="state !== 'collapsed'" />
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarFooter>
    <SidebarRail />
  </Sidebar>
</template>
