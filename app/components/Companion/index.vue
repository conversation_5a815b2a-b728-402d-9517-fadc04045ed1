<script setup lang="ts">
import { Icon } from "@iconify/vue"
import {
  ToolbarButton,
  ToolbarLink,
  ToolbarRoot,
  ToolbarSeparator,
  ToolbarToggleGroup,
  ToolbarToggleItem,
} from "reka-ui"
import { useDraggable } from "@vueuse/core"
import { reactive, ref, watch } from 'vue'
import { useElementSelector } from '@/composables/useElementSelector'

const toggleStateSingle = ref("center")
const isBookmarkFormOpen = ref(false)

const el = ref(null)
const handle = ref(null)

const pos = reactive({
  x: 40,
  y: 40
})

const progress = ref(13)

// Bookmark form data
const bookmarkForm = reactive({
  url: '',
  title: '',
  description: '',
  tags: '',
  isPrivate: false
})

// Initialize draggable directly, passing the refs
const { style } = useDraggable(el, {
  initialValue: pos,
  handle: handle,
  exact: false,
})

// Setup element selector - passing the toolbar element ref to exclude it from selection
const {
  selectedElement,
  isSelectingElement,
  startSelecting,
  stopSelecting
} = useElementSelector(el)

// Watch for toggle changes to activate/deactivate element selection
watch(toggleStateSingle, (newValue) => {
  if (newValue === 'select-element') {
    startSelecting()
  } else if (isSelectingElement.value) {
    stopSelecting()
  }
})

// Watch for selected element changes
watch(selectedElement, (element) => {
  if (element) {
    console.log('Element selected for reading:', element.tagName, element.className)
    // Here you could add logic to extract text from the selected element
    // or calculate reading time based on its content
  }
})

const handleSpeech = async () => {
  if (!selectedElement.value) {
    console.log("No element selected for speech.")
    return
  }

  const textContent = selectedElement.value.textContent?.trim()

  if (!textContent) {
    console.log("Selected element has no text content.")
    return
  }

  console.log("Sending text to speech API:", textContent)
  try {
    const response = await $fetch('/api/speech', {
      method: 'POST',
      body: { text: textContent },
    })
    console.log("Speech API response:", response)
  } catch (error) {
    console.error("Error calling speech API:", error)
  }
}

const handleBookmark = () => {
  isBookmarkFormOpen.value = !isBookmarkFormOpen.value
  
  // Pre-populate URL with current page URL
  if (isBookmarkFormOpen.value && typeof window !== 'undefined') {
    bookmarkForm.url = window.location.href
    bookmarkForm.title = document.title || ''
  }
}

const handleSaveBookmark = async () => {
  try {
    const response = await $fetch('/api/bookmarks', {
      method: 'POST',
      body: bookmarkForm,
    })
    console.log("Bookmark saved:", response)
    
    // Reset form and close
    Object.assign(bookmarkForm, {
      url: '',
      title: '',
      description: '',
      tags: '',
      isPrivate: false
    })
    isBookmarkFormOpen.value = false
  } catch (error) {
    console.error("Error saving bookmark:", error)
  }
}
</script>

<template>
  <div
    ref="el"
    :style="style"
    class="fixed z-[9999]"
  >
    <ToolbarRoot
      class="flex p-[10px] pr-[3px] w-full max-w-fit !min-w-max rounded-lg bg-background shadow-sm border"
      aria-label="Formatting options"
    >
    <ToolbarLink
      class="size-[25px] rounded-sm overflow-hidden"
      href="#"
      target="_blank"
    >
      <img
        src="https://api.dicebear.com/9.x/thumbs/svg?seed=Vorites"
        alt="Vorites Logo"
        class="w-full h-full object-cover rounded-sm"
      />
    </ToolbarLink>

      
      <ToolbarSeparator class="w-[1px] bg-border mx-[10px]" />
      <ToolbarButton @click="handleSpeech">
        <Icon class="w-[15px] h-[15px]" icon="lucide:audio-lines" />
      </ToolbarButton>
      <ToolbarSeparator class="w-[1px] bg-border mx-[10px]" />
      <ToolbarButton><Icon class="w-[15px] h-[15px]" icon="lucide:skip-back" /></ToolbarButton>
      <ToolbarButton><Icon class="w-[15px] h-[15px]" icon="lucide:play" /></ToolbarButton>
      <ToolbarButton><Icon class="w-[15px] h-[15px]" icon="lucide:skip-forward" /></ToolbarButton>



      <ToolbarSeparator class="w-[1px] bg-border mx-[10px]" />
      <ToolbarToggleGroup
        v-model="toggleStateSingle"
        type="single"
        aria-label="Text Selection"
      >
        <ToolbarToggleItem
          class="flex-shrink-0 flex-grow-0 basis-auto text-foreground/70 h-[25px] px-[5px] rounded inline-flex text-xs leading-none items-center justify-center bg-background ml-0.5 outline-none hover:bg-accent hover:text-accent-foreground focus:relative focus:shadow-[0_0_0_2px] focus:shadow-ring first:ml-0 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground"
          value="select-element"
          aria-label="Select text element"
          title="Select text element for reading"
        >
          <Icon class="w-[15px] h-[15px]" icon="lucide:crosshair" />
        </ToolbarToggleItem>
        <ToolbarToggleItem
          class="flex-shrink-0 flex-grow-0 basis-auto text-foreground/70 h-[25px] px-[5px] rounded inline-flex text-xs leading-none items-center justify-center bg-background ml-0.5 outline-none hover:bg-accent hover:text-accent-foreground focus:relative focus:shadow-[0_0_0_2px] focus:shadow-ring first:ml-0 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground"
          value="center"
          aria-label="Center Aligned"
        >
          <Icon class="w-[15px] h-[15px]" icon="lucide:remove-formatting" />
        </ToolbarToggleItem>
        
      </ToolbarToggleGroup>
      <ToolbarSeparator class="w-[1px] bg-border mx-[10px]" />
      <ToolbarLink
        class="bg-transparent !font-normal !text-muted-foreground inline-flex justify-center items-center hover:bg-transparent hover:cursor-pointer flex-shrink-0 flex-grow-0 basis-auto h-[25px] px-[5px] rounded text-xs leading-none bg-background ml-0.5 outline-none hover:bg-accent hover:text-accent-foreground focus:relative focus:shadow-[0_0_0_2px] focus:shadow-ring first:ml-0"
        href="#"
        target="_blank"
        style="margin-right: 10"
      >
        2.6Min
      </ToolbarLink>
      <Progress
        v-model="progress"
        class="w-[100px] h-[25px] rounded-sm mx-2"
      />
      <ToolbarSeparator class="w-[1px] bg-border mx-[10px]" />
      <ToolbarToggleGroup
        v-model="toggleStateSingle"
        type="single"
        aria-label="Text Alignment"
      >
      <ToolbarToggleItem
          class="flex-shrink-0 flex-grow-0 basis-auto text-foreground/70 h-[25px] px-[5px] rounded inline-flex text-xs leading-none items-center justify-center bg-background ml-0.5 outline-none hover:bg-accent hover:text-accent-foreground focus:relative focus:shadow-[0_0_0_2px] focus:shadow-ring first:ml-0 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground"
          value="right"
          aria-label="Right Aligned"
        >
          <Icon class="w-[15px] h-[15px]" icon="lucide:sun" />
        </ToolbarToggleItem>
        <ToolbarToggleItem
          class="flex-shrink-0 flex-grow-0 basis-auto text-foreground/70 h-[25px] px-[5px] rounded inline-flex text-xs leading-none items-center justify-center bg-background ml-0.5 outline-none hover:bg-accent hover:text-accent-foreground focus:relative focus:shadow-[0_0_0_2px] focus:shadow-ring first:ml-0 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground"
          value="settings"
          aria-label="Settings"
        >
        <Icon
          class="w-[15px] h-[15px]"
          icon="lucide:sparkles"
        />
        </ToolbarToggleItem>
        <ToolbarToggleItem
          class="flex-shrink-0 flex-grow-0 basis-auto text-foreground/70 h-[25px] px-[5px] rounded inline-flex text-xs leading-none items-center justify-center bg-background ml-0.5 outline-none hover:bg-accent hover:text-accent-foreground focus:relative focus:shadow-[0_0_0_2px] focus:shadow-ring first:ml-0 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground"
          value="settings"
          aria-label="Settings"
        >
        <Icon
          class="w-[15px] h-[15px]"
          icon="lucide:ghost"
        />
        </ToolbarToggleItem>
      <ToolbarToggleItem
          class="flex-shrink-0 flex-grow-0 basis-auto text-foreground/70 h-[25px] px-[5px] rounded inline-flex text-xs leading-none items-center justify-center bg-background ml-0.5 outline-none hover:bg-accent hover:text-accent-foreground focus:relative focus:shadow-[0_0_0_2px] focus:shadow-ring first:ml-0 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground"
          value="bookmark"
          aria-label="Bookmark"
          @click="handleBookmark"
        >
        <Icon
          class="w-[15px] h-[15px]"
          icon="lucide:bookmark"
        />
        </ToolbarToggleItem>
        <ToolbarToggleItem
          class="flex-shrink-0 flex-grow-0 basis-auto text-foreground/70 h-[25px] px-[5px] rounded inline-flex text-xs leading-none items-center justify-center bg-background ml-0.5 outline-none hover:bg-accent hover:text-accent-foreground focus:relative focus:shadow-[0_0_0_2px] focus:shadow-ring first:ml-0 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground"
          value="settings"
          aria-label="Settings"
        >
        <Icon
          class="w-[15px] h-[15px]"
          icon="lucide:settings"
        />
        </ToolbarToggleItem>
      </ToolbarToggleGroup>
      <ToolbarSeparator class="w-[1px] bg-border mx-[10px]" />
      <ToolbarButton
        class="px-[10px] text-primary-foreground font-semibold bg-primary flex-shrink-0 flex-grow-0 basis-auto h-[25px] rounded inline-flex text-xs items-center justify-center outline-none hover:bg-primary/90 focus:relative focus:shadow-[0_0_0_2px] focus:shadow-ring leading-[25px]"
      >
        save
      </ToolbarButton>
      <ToolbarSeparator class="w-[1px] bg-border ml-[10px]" />
      <div
        ref="handle"
        class="px-1 text-muted-foreground flex-shrink-0 flex-grow-0 basis-auto h-[25px] rounded inline-flex text-xs leading-none items-center justify-center outline-none focus:relative focus:shadow-[0_0_0_2px] focus:shadow-ring group cursor-move"
      >
        <Icon
          class="w-[15px] h-[15px] group-hover:text-primary pointer-events-none"
          icon="lucide:grip-vertical"
        />
      </div>
    </ToolbarRoot>
    
    <!-- Sliding Bookmark Form -->
    <div 
      class="absolute left-0 w-full overflow-hidden transition-all duration-300 ease-in-out top-[calc(100%-6px)]"
      :class="isBookmarkFormOpen ? 'max-h-96 opacity-100 translate-y-0' : 'max-h-0 opacity-0 -translate-y-2'"
    >
      <div class="bg-background border border-t-0 rounded-b-lg shadow-lg p-4 space-y-3">
        <div class="space-y-2">
          <label for="bookmark-url" class="text-xs font-medium text-foreground">URL</label>
          <input
            id="bookmark-url"
            v-model="bookmarkForm.url"
            type="url"
            class="w-full px-2 py-1 text-xs border rounded focus:ring-2 focus:ring-primary focus:border-primary outline-none"
            placeholder="Enter URL"
          />
        </div>
        
        <div class="space-y-2">
          <label for="bookmark-title" class="text-xs font-medium text-foreground">Title</label>
          <input
            id="bookmark-title"
            v-model="bookmarkForm.title"
            type="text"
            class="w-full px-2 py-1 text-xs border rounded focus:ring-2 focus:ring-primary focus:border-primary outline-none"
            placeholder="Enter title"
          />
        </div>
        
        <div class="space-y-2">
          <label for="bookmark-description" class="text-xs font-medium text-foreground">Description</label>
          <textarea
            id="bookmark-description"
            v-model="bookmarkForm.description"
            class="w-full px-2 py-1 text-xs border rounded focus:ring-2 focus:ring-primary focus:border-primary outline-none resize-none"
            rows="2"
            placeholder="Enter description"
          ></textarea>
        </div>
        
        <div class="space-y-2">
          <label for="bookmark-tags" class="text-xs font-medium text-foreground">Tags</label>
          <input
            id="bookmark-tags"
            v-model="bookmarkForm.tags"
            type="text"
            class="w-full px-2 py-1 text-xs border rounded focus:ring-2 focus:ring-primary focus:border-primary outline-none"
            placeholder="Enter tags (comma separated)"
          />
        </div>
        
        <div class="flex items-center space-x-2">
          <input
            id="bookmark-private"
            v-model="bookmarkForm.isPrivate"
            type="checkbox"
            class="w-3 h-3 text-primary border rounded focus:ring-2 focus:ring-primary"
          />
          <label for="bookmark-private" class="text-xs text-foreground">Private bookmark</label>
        </div>
        
        <div class="flex space-x-2 pt-2">
          <button
            @click="handleSaveBookmark"
            class="flex-1 px-3 py-1.5 text-xs font-medium text-primary-foreground bg-primary rounded hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:outline-none transition-colors"
          >
            Save Bookmark
          </button>
          <button
            @click="isBookmarkFormOpen = false"
            class="px-3 py-1.5 text-xs font-medium text-muted-foreground bg-muted rounded hover:bg-muted/80 focus:ring-2 focus:ring-muted focus:outline-none transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
