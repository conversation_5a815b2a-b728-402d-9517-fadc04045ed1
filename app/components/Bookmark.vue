<script setup lang="ts">
import { computed, type PropType } from 'vue';
import type { Bookmark } from '@/types/bookmarks';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Icon } from '@iconify/vue'; // Assuming you use @iconify/vue for icons

const props = defineProps({
  bookmark: {
    type: Object as PropType<Bookmark>,
    required: true,
  },
});

const hostname = computed(() => {
  if (!props.bookmark.url) return '';
  try {
    return new URL(props.bookmark.url).hostname.replace(/^www\./, '');
  } catch (error) {
    console.error('Invalid URL:', props.bookmark.url);
    return '';
  }
});

const faviconUrl = computed(() => {
  if (!hostname.value) return '';
  return `https://www.google.com/s2/favicons?domain=${hostname.value}&sz=64`;
});

const formattedDate = computed(() => {
  if (!props.bookmark.createdAt) return '';
  return new Date(props.bookmark.createdAt).toLocaleDateString('en-US', {
    year: '2-digit',
    month: 'numeric',
    day: 'numeric',
  });
});
</script>

<template>
  <div class="py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0 hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors duration-150">
    <div class="flex justify-between items-start gap-4">
      <div class="flex-grow flex items-start gap-3">
        <img
          v-if="faviconUrl"
          :src="faviconUrl"
          :alt="`${hostname} favicon`"
          class="w-4 h-4 mt-1 rounded-sm flex-shrink-0"
          width="16"
          height="16"
          loading="lazy"
          @error="($event) => ($event.target as HTMLImageElement).style.display = 'none'"
        />
        <div class="flex-grow">
          <a
            :href="bookmark.url"
            target="_blank"
            rel="noopener noreferrer"
            class="text-base font-medium text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
            :aria-label="`Open ${bookmark.title} in a new tab`"
          >
            {{ bookmark.title }}
          </a>
          <span v-if="hostname" class="ml-2 text-sm text-gray-500 dark:text-gray-400">
            {{ hostname }}
          </span>
        </div>
      </div>
      <div class="flex-shrink-0 flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap pt-0.5">
        <template v-if="bookmark.isPrivate || bookmark.isReadLater">
          <TooltipProvider v-if="bookmark.isPrivate">
            <Tooltip>
              <TooltipTrigger>
                <Icon icon="lucide:lock" class="h-3.5 w-3.5 text-gray-400 dark:text-gray-500" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Private bookmark</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider v-if="bookmark.isReadLater">
            <Tooltip>
              <TooltipTrigger>
                <Icon icon="lucide:bookmark" class="h-3.5 w-3.5 text-gray-400 dark:text-gray-500" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Read later</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <span v-if="(bookmark.isPrivate || bookmark.isReadLater) && formattedDate" class="text-gray-300 dark:text-gray-600">|</span>
        </template>
        <span v-if="formattedDate">{{ formattedDate }}</span>
      </div>
    </div>

    <p v-if="bookmark.tldr" class="mt-1 text-sm text-gray-600 dark:text-gray-300">
      {{ bookmark.tldr }}
    </p>

    <div v-if="bookmark.notes && bookmark.notes.trim() !== ''" class="mt-2 p-2 bg-gray-50 dark:bg-gray-800/50 rounded text-xs text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700">
      <strong class="font-medium">Notes:</strong> {{ bookmark.notes }}
    </div>

    <div class="mt-3 flex flex-wrap gap-x-2 gap-y-1 items-center text-sm text-gray-500 dark:text-gray-400">
      <span
        v-for="tag in bookmark.tags"
        :key="tag"
        class="text-primary hover:text-primary-600 dark:hover:text-primary-400 transition-colors cursor-pointer"
        tabindex="0"
        :aria-label="`Tag: ${tag}`"
        @click="() => console.log('Tag clicked:', tag)"
        @keydown.enter="() => console.log('Tag clicked:', tag)"
        @keydown.space="() => console.log('Tag clicked:', tag)"
      >
        #{{ tag }}
      </span>
    </div>
  </div>
</template>
