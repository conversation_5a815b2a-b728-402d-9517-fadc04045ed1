<script setup lang="ts">
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useUser } from '@/composables/useUser'
import { cn } from '@/lib/utils'

const props = withDefaults(defineProps<{
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  showName?: boolean
}>(), {
  size: 'md',
  showName: false
})

const { user, userInitials, avatarUrl, displayName } = useUser()
</script>

<template>
  <div :class="cn('flex items-center gap-2', $attrs.class as string)">
    <Avatar
      :size="props.size"
      shape="rounded"
      class="rounded-sm border"
    >
      <AvatarImage
        v-if="avatarUrl"
        :src="avatarUrl"
        :alt="displayName"
      />
      <AvatarFallback class="bg-muted text-muted-foreground text-xs font-medium">
        {{ userInitials }}
      </AvatarFallback>
    </Avatar>
    
    <div v-if="props.showName && user" class="flex flex-col min-w-0">
      <span class="text-sm font-medium truncate">
        {{ user.name }}
      </span>
      <span class="text-xs text-muted-foreground truncate">
        {{ user.email }}
      </span>
    </div>
  </div>
</template>
