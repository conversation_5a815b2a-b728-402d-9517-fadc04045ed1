<template>
  <div :class="cn(
    'group flex overflow-hidden p-2 [--duration:40s] [--gap:1rem] [gap:var(--gap)]',
    vertical ? 'flex-col' : 'flex-row',
    $props.class,
  )">
    <div v-for="index in repeat" :key="index" :class="cn(
      'flex shrink-0 justify-around [gap:var(--gap)]',
      vertical ? 'animate-marquee-vertical flex-col' : 'animate-marquee flex-row',
      reverse ? 'direction-reverse' : 'direction-normal',
      pauseOnHover ? 'pause' : '',
    )">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { cn } from '@/lib/utils';
  import type { HTMLAttributes } from 'vue';

  interface MarqueeProps {
    class?: HTMLAttributes['class'];
    reverse?: boolean;
    pauseOnHover?: boolean;
    vertical?: boolean;
    repeat?: number;
  }

  const props = withDefaults(
    defineProps<MarqueeProps>(),
    {
      pauseOnHover: true,
      reverse: false,
      vertical: false,
      repeat: 4,
    },
  );
</script>

<style scoped>
  .animate-marquee {
    animation: marquee var(--duration) linear infinite;
  }

  .animate-marquee-vertical {
    animation: marquee-vertical var(--duration) linear infinite;
  }

  .direction-reverse {
    animation-direction: reverse;
  }

  .direction-normal {
    animation-direction: normal;
  }

  @keyframes marquee {
    from {
      transform: translateX(0);
    }

    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }

    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }

  .pause {
    animation-play-state: running;
  }
  
  .group:hover .pause {
    animation-play-state: paused !important;
  }
</style>
