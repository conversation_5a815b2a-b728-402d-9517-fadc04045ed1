<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { computed, inject } from 'vue'
import { X } from 'lucide-vue-next'
import { TagsInputItemDelete, type TagsInputItemDeleteProps, useForwardProps } from 'reka-ui'
import { cn } from '@/lib/utils'
import type { ComputedRef } from 'vue'

const props = defineProps<TagsInputItemDeleteProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')

const forwardedProps = useForwardProps(delegatedProps)

// Get the current tag type from the TagsInputItem context
const tagType = inject('tagType') as ComputedRef<string> | undefined

// Only show delete button for hashtags and mentions (as per requirements)
const shouldShowDelete = computed(() => {
  if (!tagType) return false // fallback to not showing delete
  return tagType.value === 'hashtag' || tagType.value === 'mention'
})
</script>

<template>
  <TagsInputItemDelete
    v-if="shouldShowDelete"
    v-bind="forwardedProps"
    :class="cn('flex rounded bg-transparent mr-1', props.class)"
  >
    <slot>
      <X class="w-4 h-4" />
    </slot>
  </TagsInputItemDelete>
</template>
