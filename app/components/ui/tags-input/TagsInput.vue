<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { provide } from 'vue'
import { TagsInputRoot, type TagsInputRootEmits, type TagsInputRootProps, useForwardPropsEmits } from 'reka-ui'
import { cn } from '@/lib/utils'

export interface TagItem {
  id: string
  text: string
  type: 'hashtag' | 'mention' | 'text'
}

const props = defineProps<TagsInputRootProps & { class?: HTMLAttributes['class'] }>()
const emits = defineEmits<TagsInputRootEmits>()

const delegatedProps = reactiveOmit(props, 'class')

const forwarded = useForwardPropsEmits(delegatedProps, emits)

// Provide context for child components to manually add and update tags
const tagsInputContext = {
  addTag: (value: string) => {
    // Get current model value
    const currentTags = props.modelValue || []
    // Add new tag
    const newTags = [...currentTags, value]
    // Emit update
    emits('update:modelValue', newTags)
    emits('addTag', value)
  },
  updateTag: (oldValue: string, newValue: string) => {
    // Get current model value
    const currentTags = props.modelValue || []
    // Find and update the tag
    const tagIndex = currentTags.findIndex(tag => tag === oldValue)
    if (tagIndex !== -1) {
      const newTags = [...currentTags]
      newTags[tagIndex] = newValue
      // Emit update
      emits('update:modelValue', newTags)
    }
  }
}

provide('tagsInputContext', tagsInputContext)
</script>

<template>
  <TagsInputRoot
    v-bind="forwarded"
    :class="cn('flex flex-wrap gap-2 items-center rounded-md border border-input bg-background px-3 py-1.5 text-sm', props.class)"
  >
    <slot />
  </TagsInputRoot>
</template>
