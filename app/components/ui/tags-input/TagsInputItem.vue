<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { computed, provide, ref, inject } from 'vue'

import { TagsInputItem, type TagsInputItemProps, useForwardProps } from 'reka-ui'
import { cn } from '@/lib/utils'

const props = defineProps<TagsInputItemProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')

const forwardedProps = useForwardProps(delegatedProps)

// Get the tag type from the value (string)
const tagType = computed(() => {
  const value = props.value as string
  if (typeof value === 'string') {
    if (value.startsWith('#')) return 'hashtag'
    if (value.startsWith('@')) return 'mention'
  }
  return 'text'
})

// Editing state
const isEditing = ref(false)
const editValue = ref('')

// Get context to update tags
const tagsInputContext = inject('tagsInputContext', null)

// Editing functions
const startEdit = () => {
  isEditing.value = true
  editValue.value = props.value as string
}

const saveEdit = () => {
  if (editValue.value.trim() && tagsInputContext) {
    // Update the tag value
    tagsInputContext.updateTag(props.value as string, editValue.value.trim())
  }
  isEditing.value = false
}

const cancelEdit = () => {
  isEditing.value = false
  editValue.value = props.value as string
}

// Provide editing state and functions to children
provide('isEditing', isEditing)
provide('editValue', editValue)
provide('startEdit', startEdit)
provide('saveEdit', saveEdit)
provide('cancelEdit', cancelEdit)

// Provide the tag value to children
provide('tagValue', props.value)
provide('tagType', tagType)

// Get appropriate styling based on tag type
const tagClasses = computed(() => {
  const baseClasses = 'flex h-5 items-center rounded-md data-[state=active]:ring-ring data-[state=active]:ring-2 data-[state=active]:ring-offset-2 ring-offset-background'

  switch (tagType.value) {
    case 'hashtag':
      return cn(baseClasses, 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200')
    case 'mention':
      return cn(baseClasses, 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200')
    case 'text':
    default:
      return cn(baseClasses, 'bg-transparent text-foreground')
  }
})
</script>

<template>
  <TagsInputItem v-bind="forwardedProps" :class="cn(tagClasses, props.class)">
    <slot />
  </TagsInputItem>
</template>
