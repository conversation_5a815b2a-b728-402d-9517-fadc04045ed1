<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { ref, inject } from 'vue'
import { TagsInputInput, type TagsInputInputProps, useForwardProps } from 'reka-ui'
import { cn } from '@/lib/utils'

const props = defineProps<TagsInputInputProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')

const forwardedProps = useForwardProps(delegatedProps)

// Get the TagsInput context to manually add tags
const tagsInputContext = inject('tagsInputContext', null)

// Handle keydown events for space restriction in hashtags/mentions
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === ' ') {
    const target = event.target as HTMLInputElement
    const currentValue = target.value

    // Check if current input starts with # or @ (hashtag or mention)
    if (currentValue.startsWith('#') || currentValue.startsWith('@')) {
      // Prevent the space from being added
      event.preventDefault()

      // Create tag with current value (without space)
      if (currentValue.trim() && tagsInputContext) {
        // Manually trigger tag addition
        tagsInputContext.addTag(currentValue.trim())
        // Clear the input
        target.value = ''
      }
    }
    // For regular text, allow spaces (default behavior)
  }
}
</script>

<template>
  <TagsInputInput
    v-bind="forwardedProps"
    :class="cn('text-sm min-h-5 focus:outline-none flex-1 bg-transparent px-1', props.class)"
    @keydown="handleKeydown"
  />
</template>
