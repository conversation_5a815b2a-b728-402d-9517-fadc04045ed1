<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { ref, inject, nextTick } from 'vue'
import { TagsInputItemText, type TagsInputItemTextProps, useForwardProps } from 'reka-ui'
import { cn } from '@/lib/utils'

const props = defineProps<TagsInputItemTextProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')

const forwardedProps = useForwardProps(delegatedProps)

// Get editing state from parent TagsInputItem
const isEditing = inject('isEditing', ref(false))
const editValue = inject('editValue', ref(''))
const startEdit = inject('startEdit', () => {})
const saveEdit = inject('saveEdit', () => {})
const cancelEdit = inject('cancelEdit', () => {})

// Reference to the input element for focusing
const editInputRef = ref<HTMLInputElement>()

// Handle double-click to start editing
const handleDoubleClick = () => {
  startEdit()
  nextTick(() => {
    editInputRef.value?.focus()
    editInputRef.value?.select()
  })
}

// Handle keydown in edit mode
const handleEditKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault()
    saveEdit()
  } else if (event.key === 'Escape') {
    event.preventDefault()
    cancelEdit()
  }
}

// Handle blur to save changes
const handleEditBlur = () => {
  saveEdit()
}
</script>

<template>
  <!-- Edit mode -->
  <input
    v-if="isEditing"
    ref="editInputRef"
    v-model="editValue"
    :class="cn('py-0.5 px-2 text-sm rounded bg-white border border-primary outline-none', props.class)"
    @keydown="handleEditKeydown"
    @blur="handleEditBlur"
  />

  <!-- Display mode -->
  <TagsInputItemText
    v-else
    v-bind="forwardedProps"
    :class="cn('py-0.5 px-2 text-sm rounded bg-transparent cursor-pointer', props.class)"
    @dblclick="handleDoubleClick"
  />
</template>
