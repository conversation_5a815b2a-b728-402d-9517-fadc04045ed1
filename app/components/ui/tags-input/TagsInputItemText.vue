<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { ref, inject, nextTick } from 'vue'
import { TagsInputItemText, type TagsInputItemTextProps, useForwardProps } from 'reka-ui'
import { cn } from '@/lib/utils'

const props = defineProps<TagsInputItemTextProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')

const forwardedProps = useForwardProps(delegatedProps)

// Get editing state from parent TagsInputItem
const isEditing = inject('isEditing', ref(false))
const editValue = inject('editValue', ref(''))
const startEdit = inject('startEdit', () => {})
const saveEdit = inject('saveEdit', () => {})
const cancelEdit = inject('cancelEdit', () => {})

// Reference to the input element for focusing
const editInputRef = ref<HTMLInputElement>()

// Calculate input width based on content
const inputWidth = ref('auto')

// Handle double-click to start editing
const handleDoubleClick = () => {
  startEdit()
  nextTick(() => {
    if (editInputRef.value) {
      // Set initial width based on content
      updateInputWidth()
      editInputRef.value.focus()
      editInputRef.value.select()
    }
  })
}

// Update input width to fit content
const updateInputWidth = () => {
  if (editInputRef.value) {
    // Create a temporary span to measure text width
    const span = document.createElement('span')
    span.style.visibility = 'hidden'
    span.style.position = 'absolute'
    span.style.fontSize = '0.875rem' // text-sm
    span.style.fontFamily = getComputedStyle(editInputRef.value).fontFamily
    span.style.padding = '0.125rem 0.5rem' // py-0.5 px-2
    span.textContent = editValue.value || 'W' // Minimum width for one character

    document.body.appendChild(span)
    const width = span.offsetWidth
    document.body.removeChild(span)

    // Add some padding for cursor and ensure minimum width
    inputWidth.value = Math.max(width + 8, 40) + 'px'
  }
}

// Handle keydown in edit mode
const handleEditKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault()
    saveEdit()
  } else if (event.key === 'Escape') {
    event.preventDefault()
    cancelEdit()
  }
}

// Handle input to update width as user types
const handleEditInput = () => {
  nextTick(() => {
    updateInputWidth()
  })
}

// Handle blur to save changes
const handleEditBlur = () => {
  saveEdit()
}
</script>

<template>
  <!-- Edit mode -->
  <input
    v-if="isEditing"
    ref="editInputRef"
    v-model="editValue"
    :style="{ width: inputWidth }"
    :class="cn('py-0.5 px-2 text-sm rounded bg-white border border-primary outline-none min-w-10', props.class)"
    @keydown="handleEditKeydown"
    @input="handleEditInput"
    @blur="handleEditBlur"
  />

  <!-- Display mode -->
  <TagsInputItemText
    v-else
    v-bind="forwardedProps"
    :class="cn('py-0.5 px-2 text-sm rounded bg-transparent cursor-pointer', props.class)"
    @dblclick="handleDoubleClick"
  />
</template>
