<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { cn } from '@/lib/utils'
import { Input } from '@/components/ui/input'
import { UploadCloud, X } from 'lucide-vue-next'

const props = defineProps<{
  accept?: string
  maxSizeMB?: number
}>()

const emits = defineEmits<{
  (e: 'change', files: File[]): void
  (e: 'remove'): void
  (e: 'error', error: string): void
}>()

const fileInputRef = ref<HTMLInputElement | null>(null)
const isDragging = ref(false)
const selectedFile = ref<File | null>(null)

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
  isDragging.value = true
}

const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
  isDragging.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
  isDragging.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
  isDragging.value = false
  
  if (e.dataTransfer?.files) {
    processFiles(e.dataTransfer.files)
  }
}

const handleFileInput = (e: Event) => {
  const input = e.target as HTMLInputElement
  if (input.files) {
    processFiles(input.files)
  }
}

const processFiles = (fileList: FileList) => {
  if (fileList.length === 0) return
  
  const file = fileList[0]
  
  // Check file type if accept is provided
  if (props.accept) {
    const acceptedTypes = props.accept.split(',').map(type => type.trim().toLowerCase())
    const fileExtension = `.${file.name.split('.').pop()?.toLowerCase() || ''}`
    
    if (!acceptedTypes.some(type => type === fileExtension || type === file.type)) {
      emits('error', `File type not accepted. Please upload a ${props.accept} file.`)
      return
    }
  }
  
  // Check file size if maxSizeMB is provided
  if (props.maxSizeMB && file.size > props.maxSizeMB * 1024 * 1024) {
    emits('error', `File too large. Maximum file size is ${props.maxSizeMB}MB.`)
    return
  }
  
  selectedFile.value = file
  emits('change', [file])
}

const handleRemoveFile = () => {
  selectedFile.value = null
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
  emits('remove')
}

const handleClick = () => {
  fileInputRef.value?.click()
}

// Event listeners for document-level drag events
onMounted(() => {
  document.addEventListener('dragenter', handleDragEnter)
  document.addEventListener('dragover', handleDragOver)
  document.addEventListener('dragleave', handleDragLeave)
  document.addEventListener('drop', handleDrop)
})

onUnmounted(() => {
  document.removeEventListener('dragenter', handleDragEnter)
  document.removeEventListener('dragover', handleDragOver)
  document.removeEventListener('dragleave', handleDragLeave)
  document.removeEventListener('drop', handleDrop)
})
</script>

<template>
  <div class="relative">
    <Input
      ref="fileInputRef"
      type="file"
      class="sr-only"
      :accept="accept"
      @change="handleFileInput"
    />
    
    <div
      @click="handleClick"
      :class="cn(
        'border-input/50 hover:border-input focus-visible:ring-ring/50 group flex min-h-[120px] cursor-pointer flex-col items-center justify-center rounded-md border-2 border-dashed px-4 py-8 text-center transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
        isDragging ? 'border-primary bg-muted/30' : 'hover:bg-muted/20',
        selectedFile ? 'border-success bg-success/10' : ''
      )"
    >
      <div v-if="selectedFile" class="flex flex-col items-center gap-2">
        <button
          type="button"
          @click.stop="handleRemoveFile"
          class="bg-background absolute top-2 right-2 rounded-full p-1 hover:bg-muted focus:outline-none"
          aria-label="Remove file"
        >
          <X class="size-4 text-muted-foreground" />
        </button>
        <span class="font-medium">{{ selectedFile.name }}</span>
        <span class="text-muted-foreground text-sm">Click to change file</span>
      </div>
      <div v-else class="flex flex-col items-center gap-2">
        <UploadCloud class="size-8 text-muted-foreground group-hover:text-foreground" />
        <span class="text-muted-foreground font-medium">
          Drop your file here or click to browse
        </span>
        <span class="text-muted-foreground text-xs">
          {{ accept ? `Accepted file types: ${accept}` : 'All file types accepted' }}
          {{ props.maxSizeMB ? ` • Max size: ${props.maxSizeMB}MB` : '' }}
        </span>
      </div>
    </div>
  </div>
</template>
