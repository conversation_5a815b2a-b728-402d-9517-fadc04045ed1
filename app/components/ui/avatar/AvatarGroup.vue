<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { computed } from 'vue'
import Avatar from './Avatar.vue'
import AvatarImage from './AvatarImage.vue'
import AvatarFallback from './AvatarFallback.vue'
import { cn } from '@/lib/utils'

export interface User {
  id: string
  name: string
  avatar?: string
}

interface Props {
  users: User[]
  limit?: number
  size?: 'sm' | 'md' | 'lg'
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<Props>(), {
  limit: 10,
  size: 'md',
})

// Use the same size classes as the Avatar component with adjusted sizes
const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm': return 'size-6'
    case 'lg': return 'size-10' // Reduced from size-12 to be more proportional
    default: return 'size-8'
  }
})

// Calculate the negative margin value based on size
const negativeMargin = computed(() => {
  switch (props.size) {
    case 'sm': return 'ml-[-8px]'
    case 'lg': return 'ml-[-16px]'
    default: return 'ml-[-12px]'
  }
})

const visibleUsers = computed(() => {
  return props.users.slice(0, props.limit)
})

const remainingCount = computed(() => {
  return Math.max(0, props.users.length - props.limit)
})

const handleShowMore = () => {
  console.log('All users:', props.users)
}

const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2)
}
</script>

<template>
  <div :class="cn('flex flex-row -space-x-[1.2rem]', props.class)">
      <template v-for="(user, index) in visibleUsers" :key="user.id">
        <div class="-space-x-[1.2rem]">
          <Avatar :class="cn(sizeClasses, 'ring-muted ring-2')">
            <AvatarImage
              v-if="user.avatar"
              :src="user.avatar"
              :alt="user.name"
            />
            <AvatarFallback v-else class="bg-primary text-primary-foreground">
              {{ getInitials(user.name) }}
            </AvatarFallback>
          </Avatar>
        </div>
      </template>

      <div
        v-if="remainingCount > 0"
      >
        <Avatar
          :class="cn(
            sizeClasses,
          )"
          @click="handleShowMore"
          @keydown.enter="handleShowMore"
          tabindex="0"
          aria-label="Show more users"
        >
          <span :class="cn(
            'font-medium text-muted-foreground',
            props.size === 'sm' ? 'text-[10px]' : props.size === 'lg' ? 'text-sm' : 'text-xs'
          )">+{{ remainingCount }}</span>
        </Avatar>
      </div>
    </div>
</template>
