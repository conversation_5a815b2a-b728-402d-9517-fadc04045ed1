<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { AvatarRoot } from 'reka-ui'

type AvatarSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl'
type AvatarShape = 'square' | 'rounded' | 'full'

const props = defineProps<{
  class?: HTMLAttributes['class'],
  size?: AvatarSize,
  shape?: AvatarShape
}>()

const sizeClasses = {
  xs: 'size-6',
  sm: 'size-8',
  md: 'size-10',
  lg: 'size-12',
  xl: 'size-16'
}

const shapeClasses = {
  square: 'rounded-xs',
  rounded: 'rounded-md',
  full: 'rounded-full'
}

const avatarSize = props.size ? sizeClasses[props.size] : 'size-8'
const avatarShape = props.shape ? shapeClasses[props.shape] : 'rounded-full'
</script>

<template>
  <AvatarRoot
    data-slot="avatar"
    :class="cn('relative flex shrink-0 overflow-hidden', avatarSize, avatarShape, props.class)"
  >
    <slot />
  </AvatarRoot>
</template>
