<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Icon } from '@iconify/vue'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'

interface BookmarkSaveProps {
  url: string
  title: string
  open?: boolean
  onClose?: () => void
  bookmarkId?: string
  existingBookmark?: {
    id: string
    title: string
    notes?: string
    tags: string[]
    isPrivate: boolean
    isReadLater: boolean
    readPercentage?: number
  }
}

const props = withDefaults(defineProps<BookmarkSaveProps>(), {
  open: false,
  onClose: () => {},
  bookmarkId: undefined,
  existingBookmark: undefined
})

const emit = defineEmits(['close', 'saved', 'readAloud'])

// State
const isOpen = ref(props.open)
const isLoading = ref(false)
const bookmarkUrl = ref(props.url || '')
const bookmarkTitle = ref(props.existingBookmark?.title || props.title)
const bookmarkNotes = ref(props.existingBookmark?.notes || '')
const tagInput = ref('')
const tags = ref<string[]>(props.existingBookmark?.tags || [])
const isPrivate = ref(props.existingBookmark?.isPrivate || false)
const isReadLater = ref(props.existingBookmark?.isReadLater || false)
const suggestedTags = ref<string[]>([])
const bookmarkId = ref(props.existingBookmark?.id || props.bookmarkId)
const readPercentage = ref(props.existingBookmark?.readPercentage || 0)
const isReading = ref(false)

// Computed
const hostname = computed(() => {
  const url = bookmarkUrl.value || props.url;
  if (!url) return '';

  try {
    return new URL(url).hostname.replace(/^www\./, '')
  } catch (error) {
    console.error('Invalid URL:', url)
    return ''
  }
})

const faviconUrl = computed(() => {
  if (!hostname.value) return ''
  return `https://www.google.com/s2/favicons?domain=${hostname.value}&sz=64`
})

const formattedDate = computed(() => {
  return new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

// Methods
const handleClose = () => {
  isOpen.value = false
  emit('close')
  if (props.onClose) props.onClose()
}

const handleTagInputKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' || e.key === ',') {
    e.preventDefault()
    addTag()
  }
}

const addTag = () => {
  const newTag = tagInput.value.trim().toLowerCase().replace(/[^a-z0-9_]/g, '')
  if (newTag && !tags.value.includes(newTag)) {
    tags.value.push(newTag)
  }
  tagInput.value = ''
}

const removeTag = (tag: string) => {
  tags.value = tags.value.filter(t => t !== tag)
}

const addSuggestedTag = (tag: string) => {
  if (!tags.value.includes(tag)) {
    tags.value.push(tag)
  }
}

const saveBookmark = async () => {
  if (!bookmarkTitle.value.trim()) {
    console.error('Title is required')
    return
  }

  isLoading.value = true

  try {
    // Add any remaining tag from input
    if (tagInput.value.trim()) {
      addTag()
    }

    // Use the bookmarkUrl value, with fallbacks if empty
    const urlToSave = bookmarkUrl.value.trim() || props.url || (typeof window !== 'undefined' ? window.location.href : '')

    // Prepare the payload
    const payload = {
      url: urlToSave,
      title: bookmarkTitle.value.trim(),
      notes: bookmarkNotes.value.trim() || null,
      tags: tags.value,
      isPrivate: isPrivate.value,
      isReadLater: isReadLater.value
    }

    // Define response type
    interface BookmarkResponse {
      success: boolean;
      data?: {
        id: string;
        [key: string]: any;
      };
      message?: string;
    }

    let response: BookmarkResponse;

    // If we have a bookmark ID, update the existing bookmark
    if (bookmarkId.value) {
      console.log('Updating bookmark with payload:', payload)
      response = await $fetch<BookmarkResponse>(`/api/bookmarks/${bookmarkId.value}`, {
        method: 'PUT',
        body: payload
      })
    } else {
      // Otherwise create a new bookmark
      console.log('Creating new bookmark with payload:', payload)
      response = await $fetch<BookmarkResponse>('/api/bookmarks', {
        method: 'POST',
        body: payload
      })
    }

    if (response.success) {
      console.log('Bookmark saved successfully:', response.data)

      // Update the local bookmark ID if this was a new bookmark
      if (!bookmarkId.value && response.data?.id) {
        bookmarkId.value = response.data.id
      }

      emit('saved', response.data)
      handleClose()
    } else {
      throw new Error(response.message || 'Failed to save bookmark')
    }
  } catch (err: any) {
    console.error('Error saving bookmark:', err)
    // Define error payload type for Zod validation errors
    interface ZodErrorPayload {
      _errors?: string[];
      message?: string;
      [key: string]: any;
    }

    // Determine error payload from various FetchError properties
    const errorPayload = err.data || err.response?._data || err.response?.data || null
    let errorMessage = 'Failed to save bookmark'

    if (errorPayload && typeof errorPayload === 'object') {
      // Extract Zod-format errors
      const fieldErrors = Object.entries(errorPayload as ZodErrorPayload)
        .filter(([key]) => key !== '_errors')
        .flatMap(([field, val]) => {
          if (val && typeof val === 'object' && '_errors' in val) {
            const errs = val._errors
            return Array.isArray(errs) ? errs.map((msg: string) => `${field}: ${msg}`) : []
          }
          return []
        })

      if (fieldErrors.length) {
        errorMessage = fieldErrors.join('\n')
      } else if (Array.isArray((errorPayload as ZodErrorPayload)._errors)) {
        errorMessage = (errorPayload as ZodErrorPayload)._errors!.join('\n')
      } else if ((errorPayload as ZodErrorPayload).message) {
        // Some errors may come with a message field
        errorMessage = (errorPayload as ZodErrorPayload).message!
      }
    } else if (err.message) {
      errorMessage = err.message
    }
    alert(errorMessage)
  } finally {
    isLoading.value = false
  }
}

// Handle read aloud button click
const handleReadAloud = () => {
  isReading.value = !isReading.value

  // Emit event to parent component to handle the actual reading
  emit('readAloud', {
    isReading: isReading.value,
    bookmarkId: bookmarkId.value,
    url: props.url,
    title: bookmarkTitle.value
  })

  // Close the dialog if reading is started
  if (isReading.value) {
    handleClose()
  }
}

// Fetch reading progress if bookmark exists
const fetchReadingProgress = async () => {
  if (!bookmarkId.value) return

  try {
    const response = await $fetch<{
      success: boolean;
      data?: {
        readStatus: string;
        readPercentage: number;
        visitCount: number;
        timeSpentSeconds: number;
        lastReadAt: string | null;
      }
    }>(`/api/bookmarks/${bookmarkId.value}/bookmark-meta`)

    if (response.success && response.data) {
      readPercentage.value = response.data.readPercentage
    }
  } catch (error) {
    console.error('Failed to fetch reading progress:', error)
  }
}

// Fetch suggested tags based on the URL and title
const fetchSuggestedTags = async () => {
  try {
    // This would be replaced with an actual API call in a real implementation
    // For now, we'll just simulate some suggested tags based on the title
    const titleWords = props.title.toLowerCase().split(/\s+/)
    const commonTags = ['webdev', 'article', 'reading', 'technology', 'programming']

    // Filter out common words and keep potential tags
    suggestedTags.value = [
      ...new Set([
        ...titleWords
          .filter(word => word.length > 3 && !['the', 'and', 'that', 'with', 'for', 'from'].includes(word))
          .map(word => word.replace(/[^a-z0-9]/g, ''))
          .filter(word => word.length > 3),
        ...commonTags
      ])
    ].slice(0, 8)
  } catch (error) {
    console.error('Error fetching suggested tags:', error)
  }
}

// Initialize
fetchSuggestedTags()
if (bookmarkId.value) {
  fetchReadingProgress()
}

// Watch for prop changes
watch(() => props.open, (newVal) => {
  isOpen.value = newVal
  if (newVal && bookmarkId.value) {
    fetchReadingProgress()
  }
})

watch(() => props.url, (newVal) => {
  if (newVal) {
    bookmarkUrl.value = newVal
  }
})

watch(() => props.title, (newVal) => {
  bookmarkTitle.value = newVal
  fetchSuggestedTags()
})

watch(() => props.bookmarkId, (newVal) => {
  if (newVal) {
    bookmarkId.value = newVal
    fetchReadingProgress()
  }
})

watch(() => props.existingBookmark, (newVal) => {
  if (newVal) {
    // If we have an existing bookmark, we should have a URL from props
    if (props.url) {
      bookmarkUrl.value = props.url
    }
    bookmarkTitle.value = newVal.title
    bookmarkNotes.value = newVal.notes || ''
    tags.value = newVal.tags
    isPrivate.value = newVal.isPrivate
    isReadLater.value = newVal.isReadLater
    bookmarkId.value = newVal.id
    readPercentage.value = newVal.readPercentage || 0
  }
})
</script>

<template>
  <Dialog :open="isOpen" @update:open="handleClose">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <div class="flex items-center gap-3">
          <img v-if="faviconUrl" :src="faviconUrl" :alt="hostname" class="w-6 h-6 rounded-sm" />
          <div>
            <DialogTitle>Save Bookmark</DialogTitle>
            <DialogDescription>
              You're saving this link on {{ formattedDate }}
            </DialogDescription>
          </div>
        </div>
      </DialogHeader>

      <div class="space-y-4 py-2">
        <!-- URL -->
        <div class="space-y-2">
          <div class="relative">
            <Icon icon="lucide:link" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="url"
              v-model="bookmarkUrl"
              placeholder="https://example.com"
              class="pl-9"
            />
          </div>
        </div>

        <!-- Title -->
        <div class="space-y-2">
          <div class="relative">
            <Icon icon="lucide:type" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="title"
              v-model="bookmarkTitle"
              placeholder="Title of the bookmark"
              class="pl-9"
            />
          </div>
          <div v-if="hostname" class="flex items-center gap-1 mt-1">
            <Icon icon="lucide:globe" class="h-3 w-3 text-muted-foreground" />
            <span class="text-xs text-muted-foreground">{{ hostname }}</span>
          </div>
        </div>

        <!-- Tags -->
        <div class="space-y-2">
          <div class="flex flex-wrap gap-2 mb-2">
            <Badge
              v-for="tag in tags"
              :key="tag"
              variant="secondary"
              class="flex items-center gap-1"
            >
              {{ tag }}
              <button
                @click="removeTag(tag)"
                class="text-xs hover:text-destructive"
                aria-label="Remove tag"
              >
                <Icon icon="lucide:x" class="h-3 w-3" />
              </button>
            </Badge>
          </div>
          <div class="relative">
            <Icon icon="lucide:tag" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="tags"
              v-model="tagInput"
              placeholder="Add tags (comma separated)"
              @keydown="handleTagInputKeydown"
              @blur="addTag"
              class="pl-9"
            />
          </div>
          <p class="text-xs text-muted-foreground mt-1">Separate tags with commas</p>
        </div>

        <!-- Suggested Tags -->
        <div v-if="suggestedTags.length > 0" class="space-y-2">
          <div class="text-xs text-muted-foreground mb-1">Suggested Tags</div>
          <div class="flex flex-wrap gap-2">
            <Badge
              v-for="tag in suggestedTags"
              :key="tag"
              variant="outline"
              class="cursor-pointer hover:bg-primary hover:text-primary-foreground"
              @click="addSuggestedTag(tag)"
            >
              {{ tag }}
            </Badge>
          </div>
        </div>

        <!-- Notes -->
        <div class="space-y-2">
          <div class="relative">
            <Icon icon="lucide:file-text" class="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Textarea
              id="notes"
              v-model="bookmarkNotes"
              placeholder="Add your notes or comments about this bookmark (optional)"
              rows="3"
              class="pl-9"
            />
          </div>
        </div>

        <!-- Options -->
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <Label for="private" class="cursor-pointer">Private</Label>
            <Switch id="private" v-model:checked="isPrivate" />
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <Label for="readLater" class="cursor-pointer">Read Later</Label>
              <div class="text-xs text-muted-foreground">(Enables reading progress tracking)</div>
            </div>
            <Switch id="readLater" v-model:checked="isReadLater" />
          </div>

          <!-- Reading Progress (shown only when isReadLater is true and bookmark exists) -->
          <div v-if="isReadLater && bookmarkId" class="mt-4 border-t pt-4">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium">Reading Progress</span>
              <div class="relative h-8 w-8">
                <!-- Background circle -->
                <svg class="h-8 w-8" viewBox="0 0 32 32">
                  <circle
                    cx="16"
                    cy="16"
                    r="14"
                    fill="none"
                    stroke="currentColor"
                    stroke-opacity="0.2"
                    stroke-width="3"
                  />
                  <!-- Progress circle with stroke-dasharray animation -->
                  <circle
                    cx="16"
                    cy="16"
                    r="14"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="3"
                    stroke-linecap="round"
                    :stroke-dasharray="`${readPercentage * 88 / 100} 88`"
                    transform="rotate(-90 16 16)"
                  />
                </svg>
                <span class="absolute inset-0 flex items-center justify-center text-xs">
                  {{ readPercentage }}%
                </span>
              </div>
            </div>

            <!-- Read Aloud Button -->
            <Button
              variant="outline"
              class="w-full mt-2"
              @click="handleReadAloud"
            >
              <Icon
                :icon="isReading ? 'lucide:pause' : 'lucide:play'"
                class="h-4 w-4 mr-2"
              />
              {{ isReading ? 'Pause Reading' : 'Read Aloud' }}
            </Button>
          </div>
        </div>
      </div>

      <DialogFooter class="flex justify-between sm:justify-between">
        <Button variant="outline" @click="handleClose">Cancel</Button>
        <Button type="submit" @click="saveBookmark" :disabled="isLoading">
          <Icon v-if="isLoading" icon="lucide:loader-2" class="mr-2 h-4 w-4 animate-spin" />
          Save Bookmark
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
