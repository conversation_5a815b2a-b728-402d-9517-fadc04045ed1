<script setup lang="ts">
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@iconify/vue'
import { ref, computed, watch } from 'vue'
import { useDebounceFn } from '@vueuse/core'

// Props and emits
interface SearchProps {
  placeholder?: string
  defaultScope?: 'user' | 'global'
}

const props = withDefaults(defineProps<SearchProps>(), {
  placeholder: 'Search bookmarks... Try: #tag @user search terms',
  defaultScope: 'user'
})

const emit = defineEmits<{
  search: [query: ParsedQuery]
  clear: []
}>()

// State
const isUserScope = ref(props.defaultScope === 'user')
const searchQuery = ref('')
const inputRef = ref<HTMLInputElement>()

// Query parsing interface
interface ParsedQuery {
  searchTerm: string
  tags: string[]
  scope: 'user' | 'global'
  scopeUser?: string
  isEmpty: boolean
}

// Enhanced parseQuery with better regex and scope handling
const parseQuery = (input: string): ParsedQuery => {
  if (!input.trim()) {
    return {
      searchTerm: '',
      tags: [],
      scope: isUserScope.value ? 'user' : 'global',
      isEmpty: true
    }
  }

  // Enhanced regex to better handle complex patterns
  const regex = /(?<tag>#[\w-]+)|(?<scopeUser>@[\w-]+)|(?<term>(?:[^\s#@]+(?:\s+(?![#@])[^\s#@]+)*))/g

  const tags: string[] = []
  let scopeUser: string | undefined
  const terms: string[] = []

  let match
  while ((match = regex.exec(input)) !== null) {
    if (match.groups?.tag) {
      const tagName = match.groups.tag.substring(1).toLowerCase()
      if (tagName && !tags.includes(tagName)) {
        tags.push(tagName)
      }
    } else if (match.groups?.scopeUser && !scopeUser) {
      scopeUser = match.groups.scopeUser.substring(1).toLowerCase()
    } else if (match.groups?.term) {
      const term = match.groups.term.trim()
      if (term) {
        terms.push(term)
      }
    }
  }

  return {
    searchTerm: terms.join(' ').trim(),
    tags: tags,
    scope: scopeUser ? 'global' : (isUserScope.value ? 'user' : 'global'),
    scopeUser,
    isEmpty: terms.length === 0 && tags.length === 0 && !scopeUser
  }
}

// Computed properties for UI
const parsedQuery = computed(() => parseQuery(searchQuery.value))
const hasContent = computed(() => searchQuery.value.trim().length > 0)

// Optimized debouncing with 200ms delay (optimal for UX)
const debouncedSearch = useDebounceFn(() => {
  const query = parsedQuery.value
  if (!query.isEmpty) {
    emit('search', query)
  }
}, 200)

// Watchers
watch(searchQuery, (newValue) => {
  if (!newValue.trim()) {
    emit('clear')
  } else {
    debouncedSearch()
  }
})

// Methods
const toggleScope = () => {
  isUserScope.value = !isUserScope.value
  if (hasContent.value) {
    debouncedSearch()
  }
}

const clearSearch = () => {
  searchQuery.value = ''
  inputRef.value?.focus()
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    clearSearch()
  }
}

// Focus management
const focusInput = () => {
  inputRef.value?.focus()
}

// Expose methods
defineExpose({
  focusInput,
  clearSearch
})
</script>

<template>
  <div class="space-y-3">
    <!-- Search Input -->
    <div class="relative w-full">
      <Input 
        ref="inputRef"
        v-model="searchQuery"
        type="text" 
        :placeholder="props.placeholder"
        class="pl-10 pr-10 !text-lg !ring-0 !ring-offset-0 h-12 py-2 border border-input"
        @keydown="handleKeydown"
      />
      
      <!-- Scope Toggle Icon -->
      <button 
        type="button"
        class="absolute left-0 inset-y-0 flex items-center justify-center px-3 hover:bg-muted/50 rounded-l-md transition-colors"
        @click="toggleScope"
        :title="isUserScope ? 'Searching your bookmarks' : 'Searching all bookmarks'"
      >
        <Icon 
          :icon="isUserScope ? 'lucide:user' : 'lucide:globe'" 
          class="size-4 text-muted-foreground"
        />
      </button>
      
      <!-- Clear Button -->
      <button 
        v-if="hasContent"
        type="button"
        class="absolute right-0 inset-y-0 flex items-center justify-center px-3 hover:bg-muted/50 rounded-r-md transition-colors"
        @click="clearSearch"
        title="Clear search"
      >
        <Icon icon="lucide:x" class="size-4 text-muted-foreground" />
      </button>
    </div>

    <!-- Query Breakdown Visual Indicators -->
    <div v-if="hasContent && !parsedQuery.isEmpty" class="flex flex-wrap gap-2 text-sm">
      <!-- Search Terms -->
      <div v-if="parsedQuery.searchTerm" class="flex items-center gap-1">
        <Icon icon="lucide:search" class="size-3 text-muted-foreground" />
        <span class="text-muted-foreground">Text:</span>
        <Badge variant="secondary" class="text-xs">
          {{ parsedQuery.searchTerm }}
        </Badge>
      </div>

      <!-- Tags -->
      <div v-if="parsedQuery.tags.length > 0" class="flex items-center gap-1">
        <Icon icon="lucide:tag" class="size-3 text-muted-foreground" />
        <span class="text-muted-foreground">Tags:</span>
        <Badge 
          v-for="tag in parsedQuery.tags" 
          :key="tag"
          variant="outline" 
          class="text-xs border-blue-200 text-blue-700 dark:border-blue-800 dark:text-blue-300"
        >
          #{{ tag }}
        </Badge>
      </div>

      <!-- Scope User -->
      <div v-if="parsedQuery.scopeUser" class="flex items-center gap-1">
        <Icon icon="lucide:at-sign" class="size-3 text-muted-foreground" />
        <span class="text-muted-foreground">User:</span>
        <Badge 
          variant="outline" 
          class="text-xs border-green-200 text-green-700 dark:border-green-800 dark:text-green-300"
        >
          @{{ parsedQuery.scopeUser }}
        </Badge>
      </div>

      <!-- Scope Indicator -->
      <div class="flex items-center gap-1">
        <Icon :icon="parsedQuery.scope === 'user' ? 'lucide:user' : 'lucide:globe'" class="size-3 text-muted-foreground" />
        <span class="text-muted-foreground text-xs">
          {{ parsedQuery.scope === 'user' ? 'Your bookmarks' : 'All bookmarks' }}
        </span>
      </div>
    </div>

    <!-- Search Help Text -->
    <div v-if="!hasContent" class="text-xs text-muted-foreground space-y-1">
      <p>Search syntax:</p>
      <div class="flex flex-wrap gap-4">
        <span><Badge variant="outline" class="text-xs mr-1">#tag</Badge> for tags</span>
        <span><Badge variant="outline" class="text-xs mr-1">@user</Badge> for specific users</span>
        <span>Free text for content search</span>
      </div>
    </div>
  </div>
</template>