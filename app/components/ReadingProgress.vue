<template>
  <div class="fixed right-4 top-1/4 flex flex-col items-center space-y-4 z-50">
    <!-- Reading Progress Circle - Simple Donut Chart Style -->
    <div
      ref="progressCircleRef"
      class="relative w-10 h-10 cursor-pointer"
      tabindex="0"
      role="slider"
      :aria-valuenow="readPercentage"
      aria-valuemin="0"
      aria-valuemax="100"
      :aria-valuetext="`${readPercentage}% read`"
      aria-label="Reading progress"
      @click="handleProgressClick"
      @keydown.up.prevent="incrementProgress(5)"
      @keydown.down.prevent="decrementProgress(5)"
      @keydown.right.prevent="incrementProgress(1)"
      @keydown.left.prevent="decrementProgress(1)"
    >
      <!-- Simple pie chart style progress indicator -->
      <svg class="w-full h-full" viewBox="0 0 32 32">
        <!-- Background Circle - Light Gray -->
        <circle
          cx="16"
          cy="16"
          r="16"
          fill="#e5e7eb"
        />

        <!-- Pie Chart Mask with unique ID -->
        <mask :id="'progress-mask-' + bookmarkId">
          <rect x="0" y="0" width="32" height="32" fill="white" />
          <path
            :d="getProgressPath(readPercentage)"
            fill="black"
          />
        </mask>

        <!-- Blue Fill -->
        <circle
          cx="16"
          cy="16"
          r="16"
          fill="#3b82f6"
          :mask="'url(#progress-mask-' + bookmarkId + ')'"
        />
      </svg>
    </div>

    <!-- Action Buttons - Simplified -->
    <div class="flex flex-col space-y-2">
      <!-- Mark as Completed Button -->
      <button
        aria-label="Mark as completed"
        title="Mark as completed"
        class="p-2 rounded-full bg-blue-500 text-white focus:outline-none focus:ring-2 focus:ring-blue-400 hover:bg-blue-600 transition-colors"
        @click="markAsCompleted"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
      </button>

      <!-- Mark as Unread Button -->
      <button
        aria-label="Mark as unread"
        title="Mark as unread"
        class="p-2 rounded-full bg-gray-200 text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300 hover:bg-gray-300 transition-colors"
        @click="markAsUnread"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

// Props
const props = defineProps({
  bookmarkId: {
    type: String,
    required: true
  },
  initialReadPercentage: {
    type: Number,
    default: 0
  },
  initialReadStatus: {
    type: String,
    default: 'unread'
  }
})

// Emits
const emit = defineEmits(['update:readPercentage', 'update:readStatus'])

// State
const readPercentage = ref(props.initialReadPercentage)
const readStatus = ref(props.initialReadStatus)
const progressCircleRef = ref<HTMLElement | null>(null)
const isScrolling = ref(false)
const scrollTimeout = ref<NodeJS.Timeout | null>(null)
const updateTimeout = ref<NodeJS.Timeout | null>(null)
const lastUpdateTime = ref(Date.now())
const UPDATE_THROTTLE = 1000 // 1 second between updates

// Calculate reading progress based on scroll position
const calculateReadingProgress = () => {
  const scrollTop = window.scrollY || document.documentElement.scrollTop
  const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight

  if (scrollHeight > 0) {
    const newPercentage = Math.min(Math.round((scrollTop / scrollHeight) * 100), 100)

    // Only update if the percentage has changed
    if (newPercentage !== readPercentage.value) {
      readPercentage.value = newPercentage

      // Update read status based on percentage
      if (newPercentage > 0 && readStatus.value === 'unread') {
        readStatus.value = 'in-progress'
      }
      else if (newPercentage >= 95) {
        readStatus.value = 'completed'
      }

      // Emit events
      emit('update:readPercentage', readPercentage.value)
      emit('update:readStatus', readStatus.value)

      // Throttle API updates to prevent too many requests
      // Only schedule API update if not already scheduled
      if (!updateTimeout.value) {
        const now = Date.now()
        const timeToNextUpdate = Math.max(0, UPDATE_THROTTLE - (now - lastUpdateTime.value))

        updateTimeout.value = setTimeout(() => {
          updateReadingProgress()
          updateTimeout.value = null
          lastUpdateTime.value = Date.now()
        }, timeToNextUpdate)
      }
    }
  }
}

// Scroll handling with requestAnimationFrame for better performance
let ticking = false

const handleScroll = () => {
  if (!ticking) {
    window.requestAnimationFrame(() => {
      calculateReadingProgress()
      ticking = false
    })
    ticking = true
  }

  // Only update API when scrolling stops
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value)
  }

  scrollTimeout.value = setTimeout(() => {
    isScrolling.value = false
    // Final update when scrolling stops
    updateReadingProgress()
  }, 200)
}

// Handle click on progress circle
const handleProgressClick = (event: MouseEvent) => {
  if (!progressCircleRef.value) return

  const rect = progressCircleRef.value.getBoundingClientRect()
  const centerX = rect.left + rect.width / 2
  const centerY = rect.top + rect.height / 2

  // Calculate angle from center to click point
  const angle = Math.atan2(event.clientY - centerY, event.clientX - centerX)

  // Convert angle to degrees and adjust to start from top (270 degrees)
  let degrees = angle * (180 / Math.PI) + 90
  if (degrees < 0) degrees += 360

  // Convert degrees to percentage (0-360 degrees to 0-100%)
  const clickPercentage = Math.min(Math.max(Math.round(degrees / 3.6), 0), 100)

  readPercentage.value = clickPercentage

  // Update read status based on percentage
  if (clickPercentage > 0 && readStatus.value === 'unread') {
    readStatus.value = 'in-progress'
  }
  else if (clickPercentage >= 95) {
    readStatus.value = 'completed'
  }

  // Scroll to the corresponding position in the document
  const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight
  const scrollTarget = (clickPercentage / 100) * scrollHeight

  window.scrollTo({
    top: scrollTarget,
    behavior: 'smooth'
  })

  // Update reading progress
  updateReadingProgress()
}

// Mark as completed
const markAsCompleted = async () => {
  try {
    const response = await $fetch(`/api/bookmarks/${props.bookmarkId}/bookmark-meta/complete`, {
      method: 'PUT'
    })

    if (response.success && response.data) {
      readStatus.value = 'completed'
      readPercentage.value = 100

      emit('update:readStatus', readStatus.value)
      emit('update:readPercentage', readPercentage.value)

      console.log('Bookmark marked as completed:', response)
    }
  }
  catch (error) {
    console.error('Failed to mark bookmark as completed:', error)
  }
}

// Mark as unread
const markAsUnread = async () => {
  try {
    const response = await $fetch(`/api/bookmarks/${props.bookmarkId}/bookmark-meta/unread`, {
      method: 'PUT'
    })

    if (response.success && response.data) {
      readStatus.value = 'unread'
      readPercentage.value = 0

      emit('update:readStatus', readStatus.value)
      emit('update:readPercentage', readPercentage.value)

      console.log('Bookmark marked as unread:', response)
    }
  }
  catch (error) {
    console.error('Failed to mark bookmark as unread:', error)
  }
}

// Update reading progress via API
const updateReadingProgress = async () => {
  try {
    const response = await $fetch(`/api/bookmarks/${props.bookmarkId}/bookmark-meta`, {
      method: 'PUT',
      body: {
        readPercentage: readPercentage.value,
        readStatus: readStatus.value
      }
    })

    console.log('Reading progress updated:', response)
  }
  catch (error) {
    console.error('Failed to update reading progress:', error)
  }
}

// Keyboard accessibility methods
const setProgress = (value: number) => {
  const newValue = Math.min(Math.max(value, 0), 100)
  readPercentage.value = newValue

  // Update read status based on percentage
  if (newValue > 0 && readStatus.value === 'unread') {
    readStatus.value = 'in-progress'
  }
  else if (newValue >= 95) {
    readStatus.value = 'completed'
  }

  // Scroll to the corresponding position in the document
  const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight
  const scrollTarget = (newValue / 100) * scrollHeight

  window.scrollTo({
    top: scrollTarget,
    behavior: 'smooth'
  })

  // Update reading progress
  updateReadingProgress()
}

const incrementProgress = (amount: number) => {
  setProgress(readPercentage.value + amount)
}

const decrementProgress = (amount: number) => {
  setProgress(readPercentage.value - amount)
}

// Lifecycle hooks
onMounted(() => {
  // Use passive event listener for better performance
  window.addEventListener('scroll', handleScroll, { passive: true })
  // Initial calculation
  calculateReadingProgress()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)

  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value)
  }

  if (updateTimeout.value) {
    clearTimeout(updateTimeout.value)
  }

  // Final update when component is unmounted
  updateReadingProgress()
})

// Function to generate pie chart path for the progress indicator
const getProgressPath = (percentage: number) => {
  // For a pie chart that fills up, we need to mask the part that's NOT filled
  // So we invert the percentage (100 - percentage)
  const remainingPercentage = 100 - percentage

  // If percentage is 100%, return empty path (no mask needed, show full blue circle)
  if (remainingPercentage <= 0) return ''

  // If percentage is 0%, return a full circle path (mask everything)
  if (remainingPercentage >= 100) {
    return 'M 0,0 L 32,0 L 32,32 L 0,32 Z'
  }

  const centerX = 16
  const centerY = 16
  const radius = 16

  // Convert percentage to radians (0-100% -> 0-2π)
  const angle = (remainingPercentage / 100) * 2 * Math.PI

  // Calculate end point - start from the top (12 o'clock position)
  // and move clockwise for the remaining percentage
  const endX = centerX + radius * Math.sin(angle)
  const endY = centerY - radius * Math.cos(angle)

  // Create path: Move to center, line to top center, arc to end point, close path
  const largeArcFlag = remainingPercentage > 50 ? 1 : 0

  return `M ${centerX},${centerY} L ${centerX},${centerY - radius} A ${radius},${radius} 0 ${largeArcFlag} 1 ${endX},${endY} Z`
}

// Watch for prop changes
watch(() => props.initialReadPercentage, (newValue) => {
  if (newValue !== readPercentage.value) {
    readPercentage.value = newValue
  }
})

watch(() => props.initialReadStatus, (newValue) => {
  if (newValue !== readStatus.value) {
    readStatus.value = newValue
  }
})
</script>