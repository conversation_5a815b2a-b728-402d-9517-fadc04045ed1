<script setup lang="ts">
import { computed, ref } from 'vue'
import { Pencil, Trash2, Bookmark as BookmarkIcon, Copy, Lock, Eye } from 'lucide-vue-next';
import type { Bookmark } from '@/types/bookmarks';
import Tag from '@/components/ui/tag/Tag.vue';

const props = defineProps<{ bookmark: Bookmark }>()

const emit = defineEmits<{
  edit: [bookmark: Bookmark]
  delete: [bookmark: Bookmark]
  'read-later': [bookmark: Bookmark]
  copy: [bookmark: Bookmark]
  'tag-click': [tag: string]
}>()

const hostname = computed(() => {
  if (!props.bookmark.url) return '';
  try {
    return new URL(props.bookmark.url).hostname.replace(/^www\./, '');
  } catch {
    return '';
  }
});

const faviconUrl = computed(() => {
  if (!hostname.value) return '';
  return `https://www.google.com/s2/favicons?domain=${hostname.value}&sz=64`;
});

// Removed formattedDate computed property

const handleEdit = () => {
  emit('edit', props.bookmark);
};
const handleDelete = () => {
  emit('delete', props.bookmark);
};
const handleReadLater = () => {
  emit('read-later', props.bookmark);
};
const handleCopy = async () => {
  await navigator.clipboard.writeText(props.bookmark.url);
  emit('copy', props.bookmark);
};

const handleCardClick = () => {
  window.open(props.bookmark.url, '_blank');
};
</script>

<template>
  <div
    class="flex flex-col gap-2 p-4 bg-white group cursor-pointer"
    role="group"
    tabindex="0"
    :aria-label="`Bookmark: ${props.bookmark.title}`"
    @keydown.enter="handleCardClick"
    @keydown.space.prevent="handleCardClick"
  >
    <div class="flex items-center gap-4">
      <!-- Favicon -->
      <img
        v-if="faviconUrl"
        :src="faviconUrl"
        :alt="`${hostname} favicon`"
        class="w-6 h-6 rounded-sm flex-shrink-0 group-hover:scale-105 transition-transform duration-150"
        width="24"
        height="24"
        loading="lazy"
        @error="($event) => ($event.target as HTMLImageElement).style.display = 'none'"
      />
      <!-- Main content -->
      <div class="flex flex-col flex-1 min-w-0">
        <a
          :href="props.bookmark.url"
          target="_blank"
          rel="noopener noreferrer"
          class="font-semibold text-base text-gray-900 dark:text-white truncate hover:text-primary-600 dark:hover:text-primary-400 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 rounded-sm"
          :aria-label="`Open ${props.bookmark.title} in a new tab`"
          tabindex="0"
          @click.stop
        >
          {{ props.bookmark.title }}
        </a>
        <span v-if="hostname" class="text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5">{{ hostname }}</span>
      </div>
      <NuxtTime
        v-if="props.bookmark.createdAt"
        :datetime="props.bookmark.createdAt"
        relative
        class="text-xs text-gray-400 dark:text-gray-500 ml-2 whitespace-nowrap"
      />
      <!-- Private/Public Indicator -->
      <div class="flex items-center gap-1 ml-1" role="img" :aria-label="props.bookmark.isPrivate ? 'Private bookmark' : 'Public bookmark'">
        <Lock v-if="props.bookmark.isPrivate" class="w-3 h-3 text-gray-400 dark:text-gray-500" />
        <Eye v-else class="w-3 h-3 text-gray-400 dark:text-gray-500" />
      </div>
      <!-- Actions -->
      <div class="flex items-center gap-1 ml-2 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity duration-150" role="toolbar" aria-label="Bookmark actions">
        <button
          class="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-800 focus:bg-gray-200 dark:focus:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 transition-all duration-150"
          aria-label="Edit bookmark"
          tabindex="0"
          @click.stop="handleEdit"
          @keydown.enter.stop="handleEdit"
          @keydown.space.stop.prevent="handleEdit"
        >
          <Pencil class="w-4 h-4 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors" />
        </button>
        <button
          class="p-1.5 rounded hover:bg-red-50 dark:hover:bg-red-900/20 focus:bg-red-100 dark:focus:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 transition-all duration-150"
          aria-label="Delete bookmark"
          tabindex="0"
          @click.stop="handleDelete"
          @keydown.enter.stop="handleDelete"
          @keydown.space.stop.prevent="handleDelete"
        >
          <Trash2 class="w-4 h-4 text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors" />
        </button>
        <button
          class="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-800 focus:bg-gray-200 dark:focus:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 transition-all duration-150"
          :aria-label="props.bookmark.isReadLater ? 'Remove from Read Later' : 'Mark as Read Later'"
          tabindex="0"
          @click.stop="handleReadLater"
          @keydown.enter.stop="handleReadLater"
          @keydown.space.stop.prevent="handleReadLater"
        >
          <BookmarkIcon :class="['w-4 h-4 transition-colors', props.bookmark.isReadLater ? 'text-primary hover:text-primary-600' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300']" />
        </button>
        <button
          class="p-1.5 rounded hover:bg-green-50 dark:hover:bg-green-900/20 focus:bg-green-100 dark:focus:bg-green-900/30 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 transition-all duration-150"
          aria-label="Copy link"
          tabindex="0"
          @click.stop="handleCopy"
          @keydown.enter.stop="handleCopy"
          @keydown.space.stop.prevent="handleCopy"
        >
          <Copy class="w-4 h-4 text-gray-500 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors" />
        </button>
      </div>
    </div>
    <div v-if="props.bookmark.tldr && props.bookmark.tldr.trim() !== ''" class="text-sm text-gray-600 dark:text-gray-300 bg-blue-50 dark:bg-blue-900/20 rounded px-3 py-2 border border-blue-200 dark:border-blue-800 mt-1">
      <strong class="font-medium text-blue-800 dark:text-blue-200">TL;DR:</strong> {{ props.bookmark.tldr }}
    </div>
    <blockquote v-if="props.bookmark.notes && props.bookmark.notes.trim() !== ''" class="text-sm text-gray-600 dark:text-gray-400 ml-6 my-3 relative italic leading-relaxed before:content-[&quot;] before:text-3xl before:text-gray-300 dark:before:text-gray-600 before:absolute before:-left-4 before:-top-1 before:font-serif">
      {{ props.bookmark.notes }}
    </blockquote>
    <div v-if="props.bookmark.tags && props.bookmark.tags.length" class="flex flex-wrap gap-2 mt-1">
      <Tag
        v-for="tag in props.bookmark.tags"
        :key="tag"
        :tag="tag"
        @tag-click="emit('tag-click', $event)"
      />
    </div>
  </div>
</template>
