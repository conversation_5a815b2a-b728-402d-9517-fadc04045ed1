<script setup lang="ts">
import { ref, useTemplateRef, watch, computed } from 'vue';
import { useInfiniteScroll } from '@vueuse/core';
import { useFetch } from '#app';
import type { Bookmark } from '@/types/bookmarks';
import BookmarkItem from '@/components/bookmark/BookmarkItem.vue';
import { Icon } from '@iconify/vue';

// Props
interface BookmarkListProps {
  bookmarks?: Bookmark[]
  staticMode?: boolean
}

const props = withDefaults(defineProps<BookmarkListProps>(), {
  staticMode: false
})

// State for infinite scroll mode
const internalBookmarks = ref<Bookmark[]>([]);
const cursor = ref<string | null>(null);
const isLoading = ref(false);
const hasMoreData = ref(true);

// Use external bookmarks if provided, otherwise use internal state
const displayedBookmarks = computed(() => {
  return props.staticMode && props.bookmarks ? props.bookmarks : internalBookmarks.value
})

const fetchBookmarks = async () => {
  if (isLoading.value || !hasMoreData.value || props.staticMode) return;
  isLoading.value = true;

  try {
    const { data, error } = await useFetch('/api/bookmarks', {
      params: {
        cursor: cursor.value,
        limit: 20,
      },
    });

    if (error.value) {
      console.error('Failed to fetch bookmarks:', error.value);
      return;
    }

    if (data.value?.bookmarks) {
      internalBookmarks.value.push(...data.value.bookmarks);
      cursor.value = data.value.nextCursor;
      
      // If no nextCursor, we've reached the end
      if (!data.value.nextCursor) {
        hasMoreData.value = false;
      }
    }
  } finally {
    isLoading.value = false;
  }
};

const el = useTemplateRef<HTMLElement>('scrollContainer');

// Only setup infinite scroll if not in static mode
const { reset } = !props.staticMode ? useInfiniteScroll(
  el,
  () => fetchBookmarks(),
  {
    distance: 10,
    canLoadMore: () => hasMoreData.value && !isLoading.value,
  }
) : { reset: () => {} };

// Initial load for infinite scroll mode
if (!props.staticMode) {
  fetchBookmarks();
}

// Watch for prop changes in static mode
watch(() => props.bookmarks, (newBookmarks) => {
  if (props.staticMode && newBookmarks) {
    // Reset scroll position when new search results come in
    const container = el.value;
    if (container) {
      container.scrollTop = 0;
    }
  }
}, { immediate: true })
</script>

<template>
  <div ref="scrollContainer" class="h-full overflow-auto">
    <div class="space-y-4">
      <BookmarkItem 
        v-for="bookmark in displayedBookmarks" 
        :key="bookmark.id" 
        :bookmark="bookmark"
        v-memo="[bookmark.id, bookmark.title, bookmark.tags, bookmark.isPrivate]"
      />
    </div>
    <div v-if="!staticMode && isLoading" class="text-center py-4 text-gray-500 dark:text-gray-400">
      <Icon icon="lucide:loader-2" class="w-5 h-5 animate-spin mx-auto mb-2" />
      Loading more bookmarks...
    </div>
    <div v-if="!staticMode && !hasMoreData && displayedBookmarks.length > 0" class="text-center py-4 text-gray-500 dark:text-gray-400">
      No more bookmarks to load
    </div>
  </div>
</template>