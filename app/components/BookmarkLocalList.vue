<script setup lang="ts">
import { ref, watch } from 'vue'
import { useLocalBookmarksDb } from '@/composables/useLocalBookmarksDb.client'
import type { LocalBookmark } from '@/composables/useLocalBookmarksDb.client'


const { db, isReady } = useLocalBookmarksDb()
const bookmarks = ref<LocalBookmark[]>([])
let unsubscribe: () => Promise<void> | undefined

watch(isReady, async (ready) => {
  if (!ready || !db.value) return
  // Execute live query
  const liveQuery = await db.value.live.query(
    'SELECT id, url, title, notes, tldr, createdAt, tags, isPrivate, isReadLater FROM bookmarks ORDER BY createdAt DESC',
    [],
    (res) => {
      bookmarks.value = res.rows as LocalBookmark[]
    }
  )
  unsubscribe = async () => await liveQuery.unsubscribe()
})
onScopeDispose(() => {
  unsubscribe?.()
})
</script>

<template>
  
  <ul>
    <li v-for="b in bookmarks" :key="b.id" class="mb-4">
      <a :href="b.url" class="text-blue-600 hover:underline" target="_blank" rel="noopener">
        {{ b.title }}
      </a>
      <p>{{ b.notes }}</p>
    </li>
  </ul>
</template>
