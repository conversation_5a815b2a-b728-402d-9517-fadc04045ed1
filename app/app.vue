<template>
  <NuxtLoadingIndicator />

  <NuxtLayout>
    <NuxtPage />
  </NuxtLayout>

  <ClientOnly>
    <Toaster />
  </ClientOnly>
</template>

<script setup>

useHead({
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
  ],
  link: [
    { rel: 'icon', href: '/favicon.ico' }
  ],
  htmlAttrs: {
    lang: 'en'
  }
})

const title = 'fa.vorit.es - Social Bookmarking'
const description = 'Save, organize, and share your favorite websites with fa.vorit.es social bookmarking platform.'

useSeoMeta({
  title,
  description,
  ogTitle: title,
  ogDescription: description
})
</script>
