import { PGlite } from '@electric-sql/pglite'
import { live } from '@electric-sql/pglite/live'
import { vector } from '@electric-sql/pglite/vector'
import { makePGliteDependencyInjector } from '@electric-sql/pglite-vue'
import { defineNuxtPlugin } from '#app'

const DB_URL = 'idb://favorites-local'
const BOOKMARKS_TABLE = `CREATE TABLE IF NOT EXISTS bookmarks (
  id TEXT PRIMARY KEY,
  url TEXT NOT NULL,
  title TEXT NOT NULL,
  notes TEXT,
  tldr TEXT,
  createdAt TEXT NOT NULL,
  tags TEXT[],
  isPrivate BOOLEAN NOT NULL,
  isReadLater BOOLEAN NOT NULL
)`

export default defineNuxtPlugin(async () => {
  const { providePGlite } = makePGliteDependencyInjector()
  const db = await PGlite.create({ url: DB_URL, extensions: { live, vector } })
  await db.exec('CREATE EXTENSION IF NOT EXISTS vector;')
  await db.exec(BOOKMARKS_TABLE)
  providePGlite(db)
})
