@import "tailwindcss";
@import "tw-animate-css";

@layer base {
  [role="button"],
  button {
    cursor: pointer;
  }
  :disabled {
    cursor: default; /* Or cursor: not-allowed; */
  }
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --background: oklch(0.985 0.003 240); /* Light Gray Page Background */
  --foreground: oklch(0.35 0.01 250); /* Dark Gray Text/Iconography */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.35 0.01 250);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.35 0.01 250);
  --primary: oklch(0.51 0.18 258); /* Primary Blue */
  --primary-foreground: oklch(0.985 0 0); /* Assuming white/light text on primary */
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.35 0.01 250);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.6 0.19 255); /* Lighter Blue for accents/hovers */
  --accent-foreground: oklch(0.985 0 0); /* Assuming white/light text on accent */
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0 0); /* Adjusted for better contrast */
  --border: oklch(0.92 0.005 240); /* Light Gray Border */
  --input: oklch(0.92 0.005 240);
  --ring: oklch(0.6 0.19 255); /* Lighter Blue for rings */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.51 0.18 258); /* Primary Blue for Sidebar Background */
  --sidebar-foreground: oklch(0.985 0 0); /* White/Light text for Sidebar */
  --sidebar-primary: oklch(0.985 0 0); /* White/Light for active/icon color in sidebar */
  --sidebar-primary-foreground: oklch(0.51 0.18 258); /* Primary blue for background of active element text */
  --sidebar-accent: oklch(0.6 0.19 255); /* Lighter Blue for Sidebar hover/active */
  --sidebar-accent-foreground: oklch(0.985 0 0); /* White/Light text for Sidebar hover/active */
  --sidebar-border: oklch(0.45 0.18 258); /* Slightly darker blue for sidebar borders if needed */
  --sidebar-ring: oklch(0.985 0 0); /* White/Light for rings in sidebar */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.6 0.19 255); /* Lighter Blue for dark mode primary */
  --primary-foreground: oklch(0.145 0 0); /* Darker text on light blue */
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.51 0.18 258); /* Primary Blue for dark mode accents */
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.985 0 0); /* Adjusted for better contrast */
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.6 0.19 255); /* Lighter Blue for rings in dark mode */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0); /* Darker background for sidebar in dark mode */
  --sidebar-foreground: oklch(0.985 0 0); /* Light text for sidebar */
  --sidebar-primary: oklch(0.6 0.19 255); /* Lighter Blue for active/icon color */
  --sidebar-primary-foreground: oklch(0.145 0 0); /* Darker text on active element */
  --sidebar-accent: oklch(0.269 0.01 250); /* Slightly lighter dark for hover */
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.145 0 0); /* Even darker for borders */
  --sidebar-ring: oklch(0.6 0.19 255);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
