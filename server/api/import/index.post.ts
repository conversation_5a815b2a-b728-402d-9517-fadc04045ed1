import { z } from 'zod'
import { v7 as uuidv7 } from 'uuid'
import { extractBookmarksFromHtml } from '$/services/import'
import { importBookmarksBulk } from '$/db/operations/import'

// 5MB in bytes
const MAX_FILE_SIZE = 5 * 1024 * 1024
// Optimal chunk size for processing bookmarks in parallel
const CHUNK_SIZE = 100

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  
  // Additional safety check in case middleware didn't properly populate the session
  if (!session?.user?.id) {
    throw createError({
      statusCode: 401,
      message: 'Unauthorized - User session not found'
    })
  }
  
  const userId = session.user.id

  // Validate input
  const body = await readBody(event)

  // Check file size limit
  if (typeof body?.html === 'string' && body.html.length > MAX_FILE_SIZE) {
    throw createError({
      statusCode: 413,
      message: 'File too large. Maximum upload size is 5MB.'
    })
  }

  const validationResult = z.object({ html: z.string().min(1) }).safeParse(body)
  if (!validationResult.success) {
    throw createError({ 
      statusCode: 400, 
      message: 'Invalid HTML data' 
    })
  }

  try {
    // Extract bookmarks from HTML
    const importedBookmarks = extractBookmarksFromHtml(validationResult.data.html)
    if (!importedBookmarks.length) {
      return { success: false, message: 'No bookmarks found in the uploaded HTML file' }
    }

    console.log(`Extracted ${importedBookmarks.length} bookmarks from HTML`)

    // Generate a unique job ID for this import
    const jobId = uuidv7()

    // Process the import using our optimized bulk import function
    const totalBookmarks = importedBookmarks.length

    // Import all bookmarks in bulk with optimized chunking
    const result = await importBookmarksBulk(
      importedBookmarks,
      userId, // Use the userId obtained from the session
      CHUNK_SIZE
    )

    // Return the final result with job ID
    return {
      ...result,
      jobId,
      totalBookmarks
    }
  }
  catch (err) {
    console.error('Error processing import:', err)
    throw createError({
      statusCode: 500,
      message: err instanceof Error ? err.message : 'Failed to process bookmarks'
    })
  }
})
