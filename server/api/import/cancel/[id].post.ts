import { cancelImportJob } from '$/services/import'

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  // Get job ID from route params
  const jobId = event.context.params?.id

  if (!jobId) {
    return sendError(event, createError({
      statusCode: 400,
      statusMessage: 'Missing job ID'
    }))
  }

  // Attempt to cancel the job
  const cancelled = cancelImportJob(jobId)

  if (!cancelled) {
    return sendError(event, createError({
      statusCode: 404,
      statusMessage: 'Job not found or not running'
    }))
  }

  // Return success
  return {
    success: true,
    message: 'Import job cancelled successfully'
  }
})
