import { z } from 'zod'
import { getImportJobStatus } from '$/services/import'

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  // Get job ID from route params
  const jobId = event.context.params?.id

  if (!jobId) {
    return sendError(event, createError({
      statusCode: 400,
      statusMessage: 'Missing job ID'
    }))
  }

  // Get job status
  const jobStatus = getImportJobStatus(jobId)

  if (!jobStatus) {
    return sendError(event, createError({
      statusCode: 404,
      statusMessage: 'Job not found'
    }))
  }

  // Return job status
  return jobStatus
})