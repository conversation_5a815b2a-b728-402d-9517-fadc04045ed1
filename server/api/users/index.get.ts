import { eq, ne } from 'drizzle-orm'
import { db, tables } from '$/db'
import { sanitizedUserSchema } from '$/db/schema/user'
// import { auth } from '$/services/auth' - Auth handled by middleware

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  try {
    // Get all users except the current user
    const allUsers = await db.query.user.findMany({
      where: ne(tables.user.id, userId),
      orderBy: (user, { asc }) => [asc(user.name)]
    })
    
    // Sanitize user data to remove sensitive information
    const sanitizedUsers = allUsers.map(user => sanitizedUserSchema.parse(user))
    
    return {
      success: true,
      data: sanitizedUsers
    }
  } catch (error) {
    console.error('Error getting users list:', error)
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch users list'
    })
  }
})
