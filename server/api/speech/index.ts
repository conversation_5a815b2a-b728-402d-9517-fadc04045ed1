import { defineEventHand<PERSON>, readBody, setResponseHeader, send } from 'h3'

// Define response interface
type WordTimestamp = {
  word: string
  start: number
  end: number
}

type TTSResponse = {
  audio: string // base64 encoded audio
  wordTimestamps: WordTimestamp[]
}

const LEMONFOX_API_KEY = process.env.LEMONFOX_API_KEY || 'demo-key' // Fallback for testing
const LEMONFOX_URL = 'https://api.lemonfox.ai/v1/audio/speech'

export default defineEventHandler(async (event) => {
  if (event.method !== 'POST') {
    setResponseHeader(event, 'Allow', 'POST')
    return send(event, JSON.stringify({ error: 'Method Not Allowed', statusCode: 405 }))
  }

  const { text, voice = 'sarah', language = 'en-us', speed = 1.0 } = await readBody(event)
  if (!text || typeof text !== 'string') {
    return send(event, JSON.stringify({ error: 'Missing or invalid text', statusCode: 400 }))
  }

  // For demonstration: mock response if no API key
  if (!LEMONFOX_API_KEY || LEMONFOX_API_KEY === 'demo-key') {
    const mockResponse = mockTTSResponse(text)
    setResponseHeader(event, 'Content-Type', 'application/json')
    return send(event, JSON.stringify(mockResponse))
  }

  try {
    const lemonfoxRes = await fetch(LEMONFOX_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${LEMONFOX_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        input: text,
        voice,
        language,
        response_format: 'mp3',
        speed,
        word_timestamps: true
      })
    })

    if (!lemonfoxRes.ok) {
      console.error('API error:', await lemonfoxRes.text())
      return send(event, JSON.stringify({ 
        error: `TTS API error: ${lemonfoxRes.status}`, 
        statusCode: 502 
      }))
    }

    const data = await lemonfoxRes.json()
    
    // Standardize the response format
    const response: TTSResponse = {
      audio: data.audio || '', // Base64 encoded audio
      wordTimestamps: data.word_timestamps?.map(ts => ({
        word: ts.word,
        start: ts.start,
        end: ts.end
      })) || []
    }

    setResponseHeader(event, 'Content-Type', 'application/json')
    return send(event, JSON.stringify(response))
  } catch (error) {
    console.error('TTS API error:', error)
    return send(event, JSON.stringify({ 
      error: 'Failed to generate speech', 
      statusCode: 500 
    }))
  }
})

// Mock response for testing without API key
const mockTTSResponse = (text: string): TTSResponse => {
  const words = text.split(/\s+/).filter(Boolean)
  let currentTime = 0
  
  const wordTimestamps = words.map(word => {
    const start = currentTime
    const duration = word.length * 0.1 // Rough approximation
    currentTime += duration + 0.1 // Add small gap between words
    
    return {
      word,
      start,
      end: currentTime
    }
  })
  
  return {
    audio: 'bW9jayBhdWRpbyBkYXRhIGluIGJhc2U2NA==', // "mock audio data in base64"
    wordTimestamps
  }
}