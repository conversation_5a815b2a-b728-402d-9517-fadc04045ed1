import { getPopularUrlsByTag } from '$/db/operations/tags'

export default defineEventHandler(async (event) => {
  try {
    // Get tag name from route params
    const tagName = event.context.params?.tagName

    if (!tagName) {
      throw createError({
        statusCode: 400,
        message: 'Tag name is required'
      })
    }

    // Parse query parameters
    const query = getQuery(event)
    const period = query.period as string || 'all'
    const limit = parseInt(query.limit as string || '20', 10)
    const offset = parseInt(query.offset as string || '0', 10)
    const excludeNsfw = query.excludeNsfw !== 'false'

    // Validate period
    if (!['day', 'week', 'month', 'year', 'all'].includes(period)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid period. Must be one of: day, week, month, year, all'
      })
    }

    // Validate limit and offset
    if (isNaN(limit) || limit < 1 || limit > 100) {
      throw createError({
        statusCode: 400,
        message: 'Invalid limit. Must be a number between 1 and 100'
      })
    }

    if (isNaN(offset) || offset < 0) {
      throw createError({
        statusCode: 400,
        message: 'Invalid offset. Must be a non-negative number'
      })
    }

    // Get popular URLs for the tag
    const popularUrls = await getPopularUrlsByTag({
      tagName,
      period: period as 'day' | 'week' | 'month' | 'year' | 'all',
      limit,
      offset,
      excludeNsfw
    })

    // Format the response
    return {
      success: true,
      data: popularUrls.map(url => ({
        urlId: url.urlId,
        url: url.url,
        title: url.title || '',
        description: url.description || '',
        saveCount: url.saveCount,
        uniqueUsers: url.uniqueUsers,
        tags: url.tags || [],
        popularityScore: period === 'day'
          ? url.dailyScore
          : period === 'week'
            ? url.weeklyScore
            : period === 'month'
              ? url.monthlyScore
              : period === 'year'
                ? url.yearlyScore
                : url.popularityScore,
        firstSavedAt: url.firstSavedAt,
        lastSavedAt: url.lastSavedAt
      })),
      meta: {
        tagName,
        period,
        limit,
        offset,
        total: popularUrls.length,
        hasMore: popularUrls.length === limit
      }
    }
  }
  catch (error: unknown) {
    console.error('Error fetching popular URLs by tag:', error)

    if (error instanceof Error) {
      throw createError({
        statusCode: 500,
        message: error.message || 'Failed to fetch popular URLs for tag'
      })
    }

    throw createError({
      statusCode: 500,
      message: 'Failed to fetch popular URLs for tag'
    })
  }
})
