import { getPopularTags } from '$/db/operations/tags'

/**
 * API endpoint to get popular tags
 * GET /api/tags/popular
 * Query parameters:
 *   - limit: maximum number of tags to return (default: 20)
 *   - userId: optional user ID to get tags for a specific user
 */
export default defineEventHandler(async (event) => {
  // Optionally get user session for personalized results
  const session = await getUserSession(event)
  
  // Parse query parameters
  const query = getQuery(event)
  const limit = parseInt(query.limit as string) || 20
  
  // Get userId from query or from session
  let userId = query.userId as string
  if (!userId && query.me === 'true' && session) {
    userId = session.user.id
  }

  try {
    const popularTags = await getPopularTags(limit, userId)
    
    return { tags: popularTags }
  } catch (error: any) {
    throw createError({
      statusCode: 500,
      message: error.message || 'Failed to fetch popular tags'
    })
  }
})
