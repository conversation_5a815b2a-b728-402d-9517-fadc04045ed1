import { getFollowingFeed } from '$/db/operations/followers'

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  const query = getQuery(event)
  const limit = parseInt(query.limit as string) || 20

  console.log('Feed API - userId:', userId, 'limit:', limit)

  try {
    // Get recent bookmarks from followed users
    const feedBookmarks = await getFollowingFeed(userId, limit)
    console.log('Feed API - feed data count:', feedBookmarks.length)
    
    return {
      success: true,
      data: feedBookmarks
    }
  } catch (error) {
    console.error('Error getting feed:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get feed'
    })
  }
})