import { z } from 'zod'
import { db } from '$/db'
import { popularUrlsView } from '$/db/views/popular/popular-urls'
import { sql, desc, and } from 'drizzle-orm'

const querySchema = z.object({
  period: z.enum(['all', 'daily', 'weekly', 'monthly', 'yearly']).default('all'),
  limit: z.coerce.number().min(1).max(100).default(20),
  offset: z.coerce.number().min(0).default(0),
  search: z.string().optional(),
  tags: z.string().optional(), // comma-separated tags
  minScore: z.coerce.number().min(0).optional()
})

export default defineEventHandler(async (event) => {
  try {
    // Parse and validate query parameters
    const query = await getValidatedQuery(event, querySchema.parse)
    
    // Build the base query
    let baseQuery = db.select().from(popularUrlsView)
    
    // Build conditions array
    const conditions = []
    
    // Period-based filtering and ordering
    let orderByColumn
    switch (query.period) {
      case 'daily':
        orderByColumn = desc(popularUrlsView.dailyScore)
        conditions.push(sql`${popularUrlsView.dailyScore} > 0`)
        break
      case 'weekly':
        orderByColumn = desc(popularUrlsView.weeklyScore)
        conditions.push(sql`${popularUrlsView.weeklyScore} > 0`)
        break
      case 'monthly':
        orderByColumn = desc(popularUrlsView.monthlyScore)
        conditions.push(sql`${popularUrlsView.monthlyScore} > 0`)
        break
      case 'yearly':
        orderByColumn = desc(popularUrlsView.yearlyScore)
        conditions.push(sql`${popularUrlsView.yearlyScore} > 0`)
        break
      default:
        orderByColumn = desc(popularUrlsView.popularityScore)
    }
    
    // Search filtering
    if (query.search) {
      const searchTerm = `%${query.search.toLowerCase()}%`
      conditions.push(
        sql`(
          LOWER(${popularUrlsView.title}) LIKE ${searchTerm} OR 
          LOWER(${popularUrlsView.url}) LIKE ${searchTerm} OR
          EXISTS (
            SELECT 1 FROM unnest(${popularUrlsView.tags}) AS tag 
            WHERE LOWER(tag) LIKE ${searchTerm}
          )
        )`
      )
    }
    
    // Tag filtering
    if (query.tags) {
      const tagList = query.tags.split(',').map(tag => tag.trim()).filter(Boolean)
      if (tagList.length > 0) {
        conditions.push(
          sql`${popularUrlsView.tags} && ${tagList}`
        )
      }
    }
    
    // Minimum score filtering
    if (query.minScore !== undefined) {
      switch (query.period) {
        case 'daily':
          conditions.push(sql`${popularUrlsView.dailyScore} >= ${query.minScore}`)
          break
        case 'weekly':
          conditions.push(sql`${popularUrlsView.weeklyScore} >= ${query.minScore}`)
          break
        case 'monthly':
          conditions.push(sql`${popularUrlsView.monthlyScore} >= ${query.minScore}`)
          break
        case 'yearly':
          conditions.push(sql`${popularUrlsView.yearlyScore} >= ${query.minScore}`)
          break
        default:
          conditions.push(sql`${popularUrlsView.popularityScore} >= ${query.minScore}`)
      }
    }
    
    // Apply conditions
    if (conditions.length > 0) {
      baseQuery = baseQuery.where(and(...conditions))
    }
    
    // Apply ordering, limit, and offset
    const urls = await baseQuery
      .orderBy(orderByColumn)
      .limit(query.limit)
      .offset(query.offset)
    
    // Get total count for pagination (without limit/offset)
    const countResult = await db.execute(sql`
      SELECT count(*)::integer as total 
      FROM popular_urls 
      ${conditions.length > 0 ? sql`WHERE ${and(...conditions)}` : sql``}
    `)
    
    const total = (countResult.rows[0] as { total: number }).total
    
    return {
      success: true,
      urls,
      total,
      period: query.period,
      pagination: {
        limit: query.limit,
        offset: query.offset,
        hasMore: query.offset + query.limit < total
      }
    }
    
  } catch (error) {
    console.error('Error fetching popular URLs:', error)
    
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid query parameters',
        data: error.errors
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch popular URLs'
    })
  }
})
