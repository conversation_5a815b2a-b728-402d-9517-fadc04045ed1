import { getPopularLinks } from '$/db/operations/links'

export default defineEventHandler(async (event) => {
  try {
    // Get period from route params
    const period = event.context.params?.period

    // Validate period
    if (!period || !['today', 'week', 'month', 'year'].includes(period)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid period. Must be one of: today, week, month, year'
      })
    }

    // Map route param to period value
    const periodValue = period === 'today' ? 'day' : period

    // Parse query parameters
    const query = getQuery(event)
    const limit = parseInt(query.limit as string || '20', 10)
    const offset = parseInt(query.offset as string || '0', 10)
    const excludeNsfw = query.excludeNsfw !== 'false'

    // Validate limit and offset
    if (isNaN(limit) || limit < 1 || limit > 100) {
      throw createError({
        statusCode: 400,
        message: 'Invalid limit. Must be a number between 1 and 100'
      })
    }

    if (isNaN(offset) || offset < 0) {
      throw createError({
        statusCode: 400,
        message: 'Invalid offset. Must be a non-negative number'
      })
    }

    // Get popular URLs
    const popularLinks = await getPopularLinks({
      period: periodValue as 'day' | 'week' | 'month' | 'year',
      limit,
      offset,
      excludeNsfw
    })

    // Format the response
    return {
      success: true,
      data: popularLinks.map(link => ({
        urlId: link.linkId,
        url: link.url,
        title: link.title || '',
        description: link.description || '',
        saveCount: link.saveCount,
        uniqueUsers: link.uniqueUsers,
        tags: link.tags || [],
        popularityScore: periodValue === 'day'
          ? link.dailyScore
          : periodValue === 'week'
            ? link.weeklyScore
            : periodValue === 'month'
              ? link.monthlyScore
              : link.yearlyScore,
        firstSavedAt: link.firstSavedAt,
        lastSavedAt: link.lastSavedAt
      })),
      meta: {
        period: periodValue,
        limit,
        offset,
        total: popularLinks.length,
        hasMore: popularLinks.length === limit
      }
    }
  }
  catch (error: unknown) {
    console.error('Error fetching popular URLs:', error)

    if (error instanceof Error) {
      throw createError({
        statusCode: 500,
        message: error.message || 'Failed to fetch popular URLs'
      })
    }

    throw createError({
      statusCode: 500,
      message: 'Failed to fetch popular URLs'
    })
  }
})
