import { searchBookmarks } from '$/db/operations/bookmarks'
import { requireUserSession } from '$/utils/requireUserSession'
import { createError } from 'h3'

export default defineEventHandler(async (event) => {
  const session = await requireUserSession(event)
  const query = getQuery(event)

  // Parse query parameters
  const searchTerm = query.q as string || ''
  const tags = query.tags ? (Array.isArray(query.tags) ? query.tags : [query.tags]) as string[] : []
  const scope = query.scope as 'user' | 'global' || 'user'
  const scopeUser = query.scopeUser as string
  const cursor = query.cursor as string
  const limit = parseInt(query.limit as string, 10) || 20

  try {
    const results = await searchBookmarks({
      userId: session.user.id,
      searchTerm: searchTerm.trim(),
      tags,
      scope,
      scopeUser,
      cursor,
      limit
    })

    return {
      success: true,
      bookmarks: results.bookmarks,
      nextCursor: results.nextCursor,
      total: results.total
    }
  } catch (error) {
    console.error('Search error:', error)
    throw createError({
      statusCode: 500,
      message: 'Search failed'
    })
  }
})