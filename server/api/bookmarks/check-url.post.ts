import { z } from 'zod'
import { db } from '$/db'
import { schema } from '$/db/schema'
import { and, eq } from 'drizzle-orm'

// Schema for validating request body
const checkUrlSchema = z.object({
  url: z.string().url('Invalid URL format')
})

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  
  // Additional safety check in case middleware didn't properly populate the session
  if (!session?.user?.id) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized - User session not found'
    })
  }
  
  const userId = session.user.id

  // Validate request body
  const body = await readBody(event)
  const validationResult = checkUrlSchema.safeParse(body)
  
  if (!validationResult.success) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid request body',
      data: validationResult.error.format()
    })
  }

  const { url } = validationResult.data

  try {
    // First, find the link record for the URL
    const linkRecord = await db.query.link.findFirst({
      where: eq(schema.link.url, url)
    })
    
    if (!linkRecord) {
      // URL is not bookmarked by anyone
      return {
        success: true,
        exists: false,
        data: null
      }
    }
    
    // Check if the current user has bookmarked this URL
    const bookmarkRecord = await db.query.bookmark.findFirst({
      where: and(
        eq(schema.bookmark.userId, userId),
        eq(schema.bookmark.linkId, linkRecord.id)
      )
    })
    
    if (!bookmarkRecord) {
      // URL exists but not bookmarked by this user
      return {
        success: true,
        exists: false,
        data: null
      }
    }
    
    // URL is bookmarked by this user
    return {
      success: true,
      exists: true,
      data: {
        id: bookmarkRecord.id,
        url: linkRecord.url,
        title: bookmarkRecord.userTitle || linkRecord.title || '',
        isReadLater: bookmarkRecord.isReadLater
      }
    }
  } catch (error) {
    console.error('Error checking bookmark existence:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to check bookmark existence'
    })
  }
})
