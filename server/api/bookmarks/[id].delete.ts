import { deleteBookmark } from '$/db/operations/bookmarks'

export default defineEventHandler(async (event) => {
  // Get user session from context (handled by middleware)
  const session = event.context.session
  if (!session?.user?.id) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized'
    })
  }

  // Get bookmark ID from route params
  const id = getRouterParam(event, 'id')
  if (!id) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing bookmark ID'
    })
  }

  try {
    // Delete the bookmark
    await deleteBookmark(id, session.user.id)

    return {
      success: true,
      message: 'Bookmark deleted successfully'
    }
  }
  catch (error: any) {
    if (error.message === 'Bookmark not found or does not belong to the user') {
      throw createError({
        statusCode: 404,
        statusMessage: error.message
      })
    }
    
    console.error('Error deleting bookmark:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to delete bookmark'
    })
  }
})
