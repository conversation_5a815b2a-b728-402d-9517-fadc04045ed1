import { getBookmarksByUserId } from '$/db/operations/bookmarks';
import { createError } from 'h3';

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session;

  // Additional safety check in case middleware didn't properly populate the session
  if (!session?.user?.id) {
    throw createError({
      statusCode: 401,
      message: 'Unauthorized - User session not found'
    });
  }

  const userId = session.user.id;
  const query = getQuery(event);
  const cursor = query.cursor as string | undefined;
  const limit = parseInt(query.limit as string, 10) || 20;

  try {
    // Get paginated bookmarks for the current user
    const bookmarks = await getBookmarksByUserId(userId, cursor, limit);

    return {
      success: true,
      bookmarks,
      nextCursor: bookmarks.length ? bookmarks[bookmarks.length - 1].createdAt : null
    };
  } catch (error) {
    console.error('Error fetching bookmarks:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to fetch bookmarks'
    });
  }
});
