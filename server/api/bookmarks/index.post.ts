import { z } from 'zod'
import { createBookmark, updateBookmark } from '$/db/operations/bookmarks'
import { bookmarkInsertSchema } from '$/db/schema/bookmark'
import { linkInsertSchema } from '$/db/schema/link'

// Schema for validating request body using proper schema imports
const bookmarkSchema = z.object({
  url: linkInsertSchema.shape.url,
  title: z.string().min(1, 'Title is required'),
  notes: bookmarkInsertSchema.shape.notes,
  tags: z.array(z.string().regex(/^[a-z0-9_]+$/, 'Tags must be lowercase alphanumeric with underscores only')).optional().default([]),
  isPrivate: bookmarkInsertSchema.shape.isPrivate.default(false),
  isReadLater: bookmarkInsertSchema.shape.isReadLater.default(false)
})

// Replace existing zod schema with bookmarkUpdateSchema
const bookmarkUpdateSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  notes: bookmarkInsertSchema.shape.notes,
  tags: z.array(z.string().regex(/^[a-z0-9_]+$/, 'Tags must be lowercase alphanumeric with underscores only')).optional().default([]),
  isPrivate: bookmarkInsertSchema.shape.isPrivate.default(false),
  isReadLater: bookmarkInsertSchema.shape.isReadLater.default(false)
})

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  
  // Additional safety check in case middleware didn't properly populate the session
  if (!session?.user?.id) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized - User session not found'
    })
  }
  
  const userId = session.user.id

  // Use updateBookmark
  if (event.httpMethod === 'PUT') {
    const { id } = event.context.params
    const body = await readBody(event)
    const parseResult = bookmarkUpdateSchema.safeParse(body)
    if (!parseResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request body',
        data: parseResult.error.format()
      })
    }
    const updated = await updateBookmark(id, session.user.id, parseResult.data)
    return { success: true, message: 'Bookmark updated successfully', data: updated }
  }

  // Validate request body
  const body = await readBody(event)
  const validationResult = bookmarkSchema.safeParse(body)
  
  if (!validationResult.success) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid request body',
      data: validationResult.error.format()
    })
  }

  const { url, title, notes, tags, isPrivate, isReadLater } = validationResult.data

  try {
    // Create bookmark - createBookmark already handles URL and tag upserts internally
    const bookmark = await createBookmark({
      url,
      title,
      notes,
      tags,
      userId, // Use userId from context
      isPrivate,
      isReadLater
    })

    return {
      success: true,
      message: 'Bookmark created successfully',
      data: bookmark
    }
  } catch (error: any) {
    if (error.message === 'You\'ve already bookmarked this URL') {
      throw createError({
        statusCode: 409,
        statusMessage: error.message
      })
    } else {
      console.error('Error creating bookmark:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to create bookmark'
      })
    }
  }
})
