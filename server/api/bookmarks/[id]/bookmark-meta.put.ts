import { z } from 'zod'
import { updateReadingProgress } from '$/db/operations/reading'

// Schema for validating request body
const updateReadingSchema = z.object({
  readPercentage: z.number().min(0).max(100),
  readStatus: z.enum(['unread', 'in-progress', 'completed'])
})

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  
  // Additional safety check in case middleware didn't properly populate the session
  if (!session?.user?.id) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized - User session not found'
    })
  }
  
  const userId = session.user.id

  // Get bookmark ID from route params
  const id = getRouterParam(event, 'id')
  if (!id) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing bookmark ID'
    })
  }

  // Validate request body
  const body = await readBody(event)
  const validationResult = updateReadingSchema.safeParse(body)

  if (!validationResult.success) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid request body',
      data: validationResult.error.format()
    })
  }

  const { readPercentage, readStatus } = validationResult.data

  try {
    // Update reading progress
    const updatedReading = await updateReadingProgress(
      id,
      session.user.id,
      { readStatus, readPercentage }
    )

    return {
      success: true,
      message: 'Reading progress updated',
      data: updatedReading
    }
  }
  catch (error: any) {
    if (error.message === 'Bookmark not found or does not belong to the user') {
      throw createError({
        statusCode: 404,
        statusMessage: error.message
      })
    } else {
      console.error('Error updating reading progress:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to update reading progress'
      })
    }
  }
})
