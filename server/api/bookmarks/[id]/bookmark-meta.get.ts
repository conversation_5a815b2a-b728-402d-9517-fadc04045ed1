import { getReadingProgress } from '$/db/operations/reading'

export default defineEventHandler(async (event) => {
  // Get session directly from event context
  const session = event.context.session

  // Get bookmark ID from route params
  const id = getRouterParam(event, 'id')
  if (!id) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing bookmark ID'
    })
  }

  try {
    // Get reading progress for the bookmark
    const readingProgress = await getReadingProgress(id, session.user.id)

    if (!readingProgress) {
      return {
        success: true,
        data: {
          readStatus: 'unread',
          readPercentage: 0,
          visitCount: 0,
          timeSpentSeconds: 0,
          lastReadAt: null
        }
      }
    }

    return {
      success: true,
      data: {
        readStatus: readingProgress.readStatus,
        readPercentage: readingProgress.readPercentage,
        visitCount: readingProgress.visitCount,
        timeSpentSeconds: readingProgress.timeSpentSeconds,
        lastReadAt: readingProgress.lastReadAt
      }
    }
  }
  catch (error) {
    console.error('Error getting reading progress:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get reading progress'
    })
  }
})
