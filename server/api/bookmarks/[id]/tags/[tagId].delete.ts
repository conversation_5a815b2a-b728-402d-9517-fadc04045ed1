import { removeTagsFromBookmark } from '$/db/operations/tags'

/**
 * API endpoint to remove a tag from a bookmark
 * DELETE /api/bookmarks/:id/tags/:tagName
 */
export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  const id = getRouterParam(event, 'id')
  const tagName = getRouterParam(event, 'tagId') // Parameter is still named tagId for backward compatibility

  if (!id || !tagName) {
    throw createError({
      statusCode: 400,
      message: 'Missing bookmark ID or tag name'
    })
  }

  try {
    // Remove tag from bookmark
    const updatedBookmark = await removeTagsFromBookmark(id, [tagName])

    return { bookmark: updatedBookmark }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to remove tag from bookmark'
    throw createError({
      statusCode: 500,
      message: errorMessage
    })
  }
})
