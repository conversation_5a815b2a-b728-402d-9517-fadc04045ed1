import { z } from 'zod'
import { addTagsToBookmark } from '$/db/operations/tags'

/**
 * API endpoint to add tags to a bookmark
 * POST /api/bookmarks/:id/tags
 */
export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  const id = getRouterParam(event, 'id')
  if (!id) {
    throw createError({
      statusCode: 400,
      message: 'Missing bookmark ID'
    })
  }

  // Validate request body
  const body = await readValidatedBody(event, z.object({
    tags: z.array(z.string()).min(1)
  }).parse)

  if (!body) {
    throw createError({
      statusCode: 400,
      message: 'Invalid request body'
    })
  }

  try {
    // Add tags to bookmark
    const updatedBookmark = await addTagsToBookmark(id, body.tags)
    
    return { bookmark: updatedBookmark }
  } catch (error: any) {
    throw createError({
      statusCode: 500,
      message: error.message || 'Failed to add tags to bookmark'
    })
  }
})
