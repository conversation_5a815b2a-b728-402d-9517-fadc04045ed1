import { markBookmarkAsCompleted } from '$/db/operations/bookmark-meta'

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  
  // Get bookmark ID from route params
  const id = getRouterParam(event, 'id')
  if (!id) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing bookmark ID'
    })
  }

  try {
    // Mark bookmark as completed
    const updatedMeta = await markBookmarkAsCompleted(id, session.user.id)

    return {
      success: true,
      message: 'Bookmark marked as completed',
      data: updatedMeta
    }
  } 
  catch (error: any) {
    if (error.message === 'Bookmark not found or does not belong to the user') {
      throw createError({
        statusCode: 404,
        statusMessage: error.message
      })
    } else {
      console.error('Error marking bookmark as completed:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to mark bookmark as completed'
      })
    }
  }
})
