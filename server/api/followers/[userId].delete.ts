import { unfollowUser } from '$/db/operations/followers'

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  // Get target user ID from route params
  const targetUserId = event.context.params?.userId
  if (!targetUserId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing user ID'
    })
  }

  try {
    // Unfollow the user
    const result = await unfollowUser(session.user.id, targetUserId)
    
    if (!result) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Follow relationship not found'
      })
    }
    
    return {
      success: true,
      message: 'Successfully unfollowed user'
    }
  } catch (error: any) {
    if (error.statusCode) {
      throw error
    } else {
      console.error('Error unfollowing user:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to unfollow user'
      })
    }
  }
})
