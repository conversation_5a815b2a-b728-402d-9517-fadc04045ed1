import { getFollowCounts } from '$/db/operations/followers'

export default defineEventHandler(async (event) => {
  // Get user ID from route params
  const userId = event.context.params?.userId
  if (!userId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing user ID'
    })
  }

  try {
    // Get follow counts for the user
    const counts = await getFollowCounts(userId)
    
    return counts
  } catch (error) {
    console.error('Error getting follow counts:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get follow counts'
    })
  }
})
