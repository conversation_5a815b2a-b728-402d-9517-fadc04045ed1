import { getFollowers } from '$/db/operations/followers'

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session // todo: move all auth to global auth middleware
  
  if (!session?.user?.id) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized: No valid session'
    })
  }
  
  const userId = session.user.id

  try {
    // Get users following the current user
    const followers = await getFollowers(userId)
    
    return {
      success: true,
      data: followers
    }
  } catch (error) {
    console.error('Error getting followers list:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get followers list'
    })
  }
})
