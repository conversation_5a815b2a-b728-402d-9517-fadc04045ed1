import { z } from 'zod'
import { followUser } from '$/db/operations/followers'

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  // Get target user ID from route params
  const targetUserId = event.context.params?.userId
  if (!targetUserId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing user ID'
    })
  }

  try {
    // Follow the user
    const relationship = await followUser(session.user.id, targetUserId)
    
    return {
      success: true,
      message: 'Successfully followed user',
      data: relationship
    }
  } catch (error: any) {
    if (error.message === 'Users cannot follow themselves') {
      throw createError({
        statusCode: 400,
        statusMessage: 'You cannot follow yourself'
      })
    } else if (error.message === 'Already following this user') {
      throw createError({
        statusCode: 409,
        statusMessage: 'Already following this user'
      })
    } else if (error.message === 'Follower user not found' || error.message === 'Following user not found') {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    } else {
      console.error('Error following user:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to follow user'
      })
    }
  }
})
