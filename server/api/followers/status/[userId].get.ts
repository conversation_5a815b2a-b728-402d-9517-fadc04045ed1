import { checkFollowStatus } from '$/db/operations/followers'

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  // Get target user ID from route params
  const targetUserId = event.context.params?.userId
  if (!targetUserId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing user ID'
    })
  }

  try {
    // Check if the current user follows the target user
    const isFollowing = await checkFollowStatus(session.user.id, targetUserId)
    
    return {
      success: true,
      isFollowing
    }
  } catch (error) {
    console.error('Error checking follow status:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to check follow status'
    })
  }
})
