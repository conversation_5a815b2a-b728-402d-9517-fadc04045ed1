import { getReadingProgress } from '$/db/operations/reading'

export default defineEventHandler(async (event) => {
  // Get session directly from event context
  const session = event.context.session

  // Get bookmark ID from route params
  const bookmarkId = event.context.params?.bookmarkId
  if (!bookmarkId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing bookmark ID'
    })
  }

  try {
    // Get reading progress for the bookmark
    const readingProgress = await getReadingProgress(bookmarkId, session.user.id)
    // Transform response to match bookmark-meta schema
    if (!readingProgress) {
      return {
        success: true,
        data: {
          readStatus: 'unread',
          readPercentage: 0,
          visitCount: 0,
          timeSpentSeconds: 0,
          lastReadAt: null
        }
      }
    }
    return {
      success: true,
      data: readingProgress
    }
  }
  catch (error) {
    console.error('Error getting reading progress:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get reading progress'
    })
  }
})
