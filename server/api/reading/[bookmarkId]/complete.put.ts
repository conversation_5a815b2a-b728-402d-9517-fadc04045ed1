import { markBookmarkAsCompleted } from '$/db/operations/reading'

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  // Get bookmark ID from route params
  const bookmarkId = event.context.params?.bookmarkId
  if (!bookmarkId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing bookmark ID'
    })
  }

  try {
    // Mark bookmark as completed
    const updatedReading = await markBookmarkAsCompleted(bookmarkId, session.user.id)

    return {
      success: true,
      message: 'Bookmark marked as completed',
      data: updatedReading
    }
  } 
  catch (error: any) {
    if (error.message === 'Bookmark not found or does not belong to the user') {
      throw createError({
        statusCode: 404,
        statusMessage: error.message
      })
    } else {
      console.error('Error marking bookmark as completed:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to mark bookmark as completed'
      })
    }
  }
})
