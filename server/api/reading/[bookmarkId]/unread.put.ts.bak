import { markBookmarkAsUnread } from '$/db/operations/reading'

export default defineEventHandler(async (event) => {
  // Authenticate user
  const session = await requireUserSession(event)
  if (!session?.user?.id) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized'
    })
  }

  // Get bookmark ID from route params
  const bookmarkId = event.context.params?.bookmarkId
  if (!bookmarkId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing bookmark ID'
    })
  }

  try {
    // Mark bookmark as unread
    const updatedReading = await markBookmarkAsUnread(bookmarkId, session.user.id)

    return {
      success: true,
      message: 'Bookmark marked as unread',
      data: updatedReading
    }
  } 
  catch (error: any) {
    if (error.message === 'Bookmark not found or does not belong to the user') {
      throw createError({
        statusCode: 404,
        statusMessage: error.message
      })
    } else {
      console.error('Error marking bookmark as unread:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to mark bookmark as unread'
      })
    }
  }
})
