import { getFollowing } from '$/db/operations/followers'

export default defineEventHandler(async (event) => {
  // Auth handled by middleware
  const session = event.context.session
  const userId = session.user.id

  console.log('Following API - userId:', userId)

  try {
    // Get users the current user is following
    const following = await getFollowing(userId)
    console.log('Following API - following data:', following)
    
    return {
      success: true,
      data: following
    }
  } catch (error) {
    console.error('Error getting following list:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get following list'
    })
  }
})
