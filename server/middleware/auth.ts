import { auth } from '$/services/auth'

// Routes that don't require authentication
const PUBLIC_ROUTES = [
  '/sign-in',
  '/sign-up',
  '/api/auth/session',
  '/_nuxt/',
  '/public/',
  '/favicon.ico',
  '/robots.txt'
]

// Routes that require authentication
const PROTECTED_ROUTES = [
  '/', // Home page
  '/home',
  '/bookmarks',
  '/import',
  '/article',
  '/network',
  '/settings'
]

export default defineEventHandler(async (event) => {
  const { url } = event.node.req

  if (!url) return

  // Skip authentication for public routes and assets
  if (PUBLIC_ROUTES.some(route =>
    url === route || url.startsWith(route === '/' ? route : route + '/'))) {
    return
  }

  // Get user session with betterAuth
  const session = await auth.api.getSession({ headers: event.headers })

  // Add session to context for downstream handlers
  event.context.session = session

  // For debugging - log session to help identify the issue
  console.log(`Auth middleware - URL: ${url}, Session exists: ${!!session}, User exists: ${!!session?.user}${session?.user ? `, User ID: ${session.user.id}` : ''}`)

  // For API routes: return 401 if unauthorized
  if (url.startsWith('/api/') && !url.startsWith('/api/auth/')) {
    if (!session?.user?.id) {
      throw createError({
        statusCode: 401,
        message: 'Unauthorized'
      })
    }
    return // Continue to API handler
  }

  // For protected page routes: redirect to sign-in if unauthorized
  if (PROTECTED_ROUTES.some(route =>
    url === route || url.startsWith(route === '/' ? route : route + '/'))
  ) {
    if (!session?.user?.id) {
      await sendRedirect(event, '/sign-in', 302)
    }
  }
})
