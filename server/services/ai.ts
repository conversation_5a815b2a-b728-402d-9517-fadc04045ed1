import { OpenAI } from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

/**
 * Generates an embedding vector for the given text using OpenAI's API.
 * @param input - The text to generate an embedding for.
 * @returns Promise<number[]> - The embedding vector.
 */

export const MODEL_NAME = 'rag_bge_small_en_v15';

export async function generateEmbedding(input: string): Promise<number[]> {
  const response = await openai.embeddings.create({
    model: MODEL_NAME,
    input
  });

  // OpenAI returns an array of data, each with an embedding
  // We assume single input, so take the first embedding
  return response.data[0].embedding;
}

/**
 * Generates a summary for the given text using OpenAI's API.
 * @param input - The text to summarize.
 * @param maxLength - The maximum length of the summary in characters (default: 200).
 * @returns Promise<string> - The summary text.
 */
export async function generateSummary(input: string, maxLength = 200): Promise<string> {
  const response = await openai.chat.completions.create({
    model: 'gpt-3.5-turbo',
    messages: [
      {
        role: 'system',
        content: `Summarize the following text in less than ${maxLength} characters.`
      },
      {
        role: 'user',
        content: input
      }
    ],
    max_tokens: Math.ceil(maxLength * 1.5 / 4), // rough estimate: 1 token ≈ 4 chars
    temperature: 0.5
  });

  return response.choices[0]?.message?.content?.trim() ?? '';
}

/**
 * Extracts taxonomy-style tags (keywords) from the given text using OpenAI's API.
 * The tags are suitable for connecting related bookmarks for recommendation and exploratory browsing.
 * @param input - The text to extract tags from.
 * @param maxTags - The maximum number of tags to extract (default: 8).
 * @returns Promise<string[]> - The extracted tags.
 */
export async function generateTags(input: string, maxTags = 8): Promise<string[]> {
  const response = await openai.chat.completions.create({
    model: 'gpt-3.5-turbo',
    messages: [
      {
        role: 'system',
        content:
          `Extract up to ${maxTags} concise, taxonomy-style tags (keywords) from the following text. ` +
          `Return only a comma-separated list of tags, all lowercase, no explanations, no hashtags.`
      },
      {
        role: 'user',
        content: input
      }
    ],
    max_tokens: 64,
    temperature: 0.2
  });

  const tagString = response.choices[0]?.message?.content ?? '';
  return tagString
    .split(',')
    .map(tag => tag.trim().toLowerCase())
    .filter(tag => tag.length > 0);
}