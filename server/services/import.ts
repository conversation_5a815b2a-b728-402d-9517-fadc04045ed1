import type { NewBookmark } from '~~/server/db/schema/bookmark'
import type { NewLink } from '~~/server/db/schema/link'
import type { NewTag } from '~~/server/db/schema/tag'

/**
 * Represents a flattened bookmark ready for database insertion
 */
export interface ImportedBookmark {
  title: string
  url: string
  addDate?: number
  tags: string[]
  faviconUrl?: string // Kept for backward compatibility, but renamed to favicon in the database
}

/**
 * Represents a processed entity ready for database insertion
 */
export interface ProcessedEntities {
  urls: NewLink[]
  bookmarks: NewBookmark[]
  tags: NewTag[]
}

/**
 * Extract hashtags from a string
 * @param text The text to extract hashtags from
 * @returns Array of unique hashtags without the # symbol
 */
function extractHashTags(text: string): string[] {
  if (!text) return []

  const regex = /#(\w+)/g
  const matches = text.match(regex)

  if (!matches) return []

  // Remove the # symbol and return unique tags
  const tagSet = new Set<string>()
  matches.forEach((tag) => {
    tagSet.add(tag.substring(1).toLowerCase())
  })
  return Array.from(tagSet)
}

/**
 * Normalize a tag name to conform to the tag naming constraints
 * - Lowercase alphanumeric characters and underscores only
 * - No spaces, hyphens, or special characters
 * @param tagName The tag name to normalize
 * @returns Normalized tag name
 */
function normalizeTagName(tagName: string): string {
  if (!tagName) return ''

  // Convert to lowercase
  let normalized = tagName.toLowerCase()

  // Replace spaces and hyphens with underscores
  normalized = normalized.replace(/[\s-]+/g, '_')

  // Remove any characters that aren't alphanumeric or underscore
  normalized = normalized.replace(/[^a-z0-9_]/g, '')

  // Ensure it's not empty
  return normalized || ''
}

/**
 * Parses HTML bookmark content and extracts links with enhanced metadata
 * @param htmlContent HTML content from a browser bookmark export
 * @returns Array of imported bookmarks with metadata
 */
export function extractBookmarksFromHtml(htmlContent: string): ImportedBookmark[] {
  const result: ImportedBookmark[] = []

  try {
    if (!htmlContent || typeof htmlContent !== 'string') {
      console.error('Invalid HTML content provided for bookmark extraction')
      return result
    }
    
    // Extract all anchor tags with their attributes
    const anchorRegex = /<a\s+([^>]+)>([^<]*)<\/a>/gi
    let match

    // Track the current folder structure
    const folderStack: string[] = []

    // Process the HTML line by line to track folder structure
    const lines = htmlContent.split('\n')

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]

      // Check for folder start (H3 tags in bookmarks HTML)
      const folderMatch = typeof line === 'string' ? line.match(/<h3[^>]*>([^<]*)<\/h3>/i) : null
      if (folderMatch && folderMatch[1]) {
        const folderName = folderMatch[1].trim()
        // Skip default browser bookmark folders
        if (folderName && !folderName.includes('Bookmarks') && !folderName.includes('bar')) {
          folderStack.push(folderName)
        }
      }

      // Check for folder end
      if (line?.includes('</DL>')) {
        folderStack.pop()
      }

      // Look for anchors
      while (typeof line === 'string' && (match = anchorRegex.exec(line)) !== null) {
        const attributes = match[1]
        const title = (match[2] ?? '').trim()

        // Extract URL
        const urlMatch = attributes?.match(/HREF="([^"]+)"/i)
        if (!urlMatch) continue

        const url = urlMatch[1]
        // Skip javascript:, data: URLs and empty URLs for security and data quality
        if (!url || url.startsWith('javascript:') || url.startsWith('data:')) continue
        
        // Ensure URL has a valid protocol, adding https:// if missing
        let validUrl = url
        try {
          new URL(url) // This will throw if URL is invalid
        } catch (e) {
          // If URL parsing fails, try adding https:// prefix
          if (!url.match(/^[a-z]+:\/\//i)) {
            validUrl = `https://${url}`
            try {
              new URL(validUrl) // Validate again with added prefix
            } catch (e) {
              continue // Skip this URL if it's still invalid
            }
          } else {
            continue // Skip invalid URLs with protocols
          }
        }

        // Extract ADD_DATE
        const addDateMatch = attributes?.match(/ADD_DATE="([^"]+)"/i)
        const addDateStr = addDateMatch ? addDateMatch[1] : undefined

        // Convert ADD_DATE to timestamp if available
        let addDate: number | undefined = undefined
        if (addDateStr) {
          addDate = parseInt(addDateStr, 10)
        }

        // Extract ICON (favicon) if available
        const iconMatch = attributes?.match(/ICON="([^"]+)"/i)
        // If favicon exists in HTML, use it; otherwise, leave it undefined
        // Note: This is still called faviconUrl for backward compatibility, but stored as favicon in the database
        const faviconUrl = iconMatch ? iconMatch[1] : undefined

        // Extract hashtags from title
        const hashTags = extractHashTags(title)

        // Clean title by removing hashtags
        const cleanTitle = title.replace(/#\w+/g, '').trim()

        // Process folder names and hashtags into normalized tags
        const normalizedFolderTags = folderStack.map(folder => normalizeTagName(folder))
        const normalizedHashTags = hashTags.map(tag => normalizeTagName(tag))

        // Combine folder tags and hashtags into a single tags array, ensuring uniqueness
        const tagSet = new Set<string>([...normalizedFolderTags, ...normalizedHashTags])
        // Filter out empty strings - important for database integrity as tag names must not be empty
        // and for performance as empty tags add no value
        const combinedTags = Array.from(tagSet).filter(Boolean)

        result.push({
          title: cleanTitle || url,
          url: validUrl,  // Use the validated URL
          addDate,
          tags: combinedTags,
          faviconUrl
        })
      }
    }
  }
  catch (error) {
    console.error('Error parsing bookmarks HTML:', error)
    // Return any bookmarks that were successfully extracted before the error
  }

  console.log(`Successfully extracted ${result.length} bookmarks from HTML`)
  return result
}

/**
 * Process imported bookmarks into entities ready for database insertion
 * This function is kept for backward compatibility.
 * New code should use the bulk import functions in server/db/operations/import.ts
 *
 * @param importedBookmarks Array of imported bookmarks from HTML
 * @param userId ID of the user importing the bookmarks
 * @returns Processed entities ready for database insertion
 * @deprecated Use importBookmarksBulk from server/db/operations/import.ts instead
 */
export function processBookmarksToEntities(
  _importedBookmarks: ImportedBookmark[],
  _userId: string
): ProcessedEntities {
  console.warn('processBookmarksToEntities is deprecated. Use importBookmarksBulk from server/db/operations/import.ts instead')

  // Initialize result containers with empty arrays
  return {
    urls: [],
    bookmarks: [],
    tags: []
  }
}

/**
 * Import job status type
 * This is kept for compatibility with existing code
 * Will be implemented properly later
 */
export interface JobStatus {
  jobId: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: {
    total: number
    processed: number
    imported: number
    skipped: number
    currentStep: string
    error?: string
    startedAt: Date
    completedAt?: Date
  }
  result?: {
    success: boolean
    imported: number
    skipped: number
    message: string
    errors?: string[]
  }
}

/**
 * Stub function for getting import job status
 * This will be implemented properly later
 */
export function getImportJobStatus(_jobId: string): JobStatus | null {
  console.warn('getImportJobStatus is a stub function and will be implemented later')
  return null
}

/**
 * Stub function for cancelling an import job
 * This will be implemented properly later
 */
export function cancelImportJob(_jobId: string): boolean {
  console.warn('cancelImportJob is a stub function and will be implemented later')
  return false
}
