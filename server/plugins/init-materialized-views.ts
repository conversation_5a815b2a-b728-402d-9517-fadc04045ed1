import { db } from '$/db'
import { sql } from 'drizzle-orm'
import { initializePopularUrlsView } from '$/db/views/popular/popular-urls'

/**
 * Check if required tables exist before initializing views
 */
async function checkRequiredTablesExist(): Promise<boolean> {
  try {
    // Query to check if the 'link' and 'bookmark' tables exist
    const result = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'link'
      ) AS link_exists,
      EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'bookmark'
      ) AS bookmark_exists
    `)

    // Check if both tables exist
    const { link_exists, bookmark_exists } = result.rows[0] as { link_exists: boolean; bookmark_exists: boolean }
    return link_exists && bookmark_exists
  } catch (error) {
    console.error('Error checking if required tables exist:', error)
    return false
  }
}

/**
 * Server plugin to initialize materialized views on startup
 *
 * This plugin attempts to initialize the materialized views immediately when loaded.
 * If that fails, it falls back to using the 'request' hook to initialize the views
 * on the first request, when the database connection is guaranteed to be established.
 */
export default defineNitroPlugin(async (nitroApp) => {
  console.log('Materialized views plugin loaded successfully')

  // Try to initialize immediately or after a delay
  try {
    console.log('Attempting to initialize materialized views...')

    // Check if required tables exist before initializing views
    let tablesExist = await checkRequiredTablesExist()

    if (tablesExist) {
      await initializePopularUrlsView()
      console.log('Materialized views initialized successfully on startup')
      global.__materializedViewsInitialized = true
    } else {
      console.log('Required tables do not exist yet, waiting 10 seconds before retrying...')

      // Wait 10 seconds and try again
      await new Promise(resolve => setTimeout(resolve, 10000))

      console.log('Retrying materialized view initialization after delay...')
      tablesExist = await checkRequiredTablesExist()

      if (tablesExist) {
        await initializePopularUrlsView()
        console.log('Materialized views initialized successfully after delay')
        global.__materializedViewsInitialized = true
      } else {
        console.log('Required tables still do not exist after delay, will retry on first request')
        global.__materializedViewsInitialized = false
      }
    }
  }
  catch (error) {
    console.log('Could not initialize views, will retry on first request')
    console.log('Error:', error instanceof Error ? error.message : String(error))

    // Use the 'request' hook as a fallback
    nitroApp.hooks.hook('request', async () => {
      // Use a flag to ensure we only run the initialization once per request
      if (!global.__materializedViewsInitialized) {
        // Set a temporary flag to prevent multiple concurrent initialization attempts
        const isInitializing = global.__isInitializingMaterializedViews
        if (isInitializing) return

        global.__isInitializingMaterializedViews = true

        try {
          // Check if required tables exist before initializing views
          const tablesExist = await checkRequiredTablesExist()

          if (tablesExist) {
            console.log('Initializing materialized views on request...')
            await initializePopularUrlsView()
            console.log('Materialized views initialized successfully')
            global.__materializedViewsInitialized = true
          } else {
            console.log('Required tables still do not exist on request, initialization will be retried later')
          }
        }
        catch (error) {
          console.error('Failed to initialize materialized views:', error)
          // Log more detailed error information
          if (error instanceof Error) {
            console.error('Error details:', error.message)
            console.error('Stack trace:', error.stack)
          }
          else {
            console.error('Unknown error type:', error)
          }
        }
        finally {
          // Reset the initialization flag
          global.__isInitializingMaterializedViews = false
        }
      }
    })
  }
})

// Add TypeScript declarations for global variables
declare global {
  // eslint-disable-next-line no-var
  var __materializedViewsInitialized: boolean
  // eslint-disable-next-line no-var
  var __isInitializingMaterializedViews: boolean
}
