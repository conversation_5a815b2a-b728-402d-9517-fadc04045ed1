import { faker } from '@faker-js/faker'
import { readFileSync } from 'fs'
import { join } from 'path'

// Set seed for deterministic data generation
faker.seed(42)

/**
 * Generate a realistic user bio using Faker.js
 */
function generateUserBio(): string {
  const bioTypes = [
    () => `${faker.person.jobTitle()} passionate about ${faker.hacker.ingverb()}ing modern web applications`,
    () => `${faker.person.jobDescriptor()} engineer building ${faker.company.buzzAdjective()} ${faker.hacker.noun()} solutions`,
    () => `Tech enthusiast and ${faker.person.jobArea()} professional focusing on ${faker.hacker.abbreviation()}`,
    () => `${faker.person.jobType()} developer who loves ${faker.hacker.noun()} and ${faker.hacker.noun()}`,
    () => `${faker.person.jobArea()} specialist with ${faker.number.int({ min: 2, max: 15 })} years of experience`,
    () => `${faker.company.buzzNoun()} advocate and ${faker.person.jobDescriptor()} engineer`,
    () => `Building ${faker.hacker.adjective()} applications with ${faker.hacker.noun()} and ${faker.hacker.noun()}`,
    () => `${faker.person.jobTitle()} turning ${faker.hacker.noun()} into ${faker.company.buzzNoun()}`,
    () => `${faker.person.jobArea()} professional crafting ${faker.hacker.adjective()} user experiences`,
    () => `${faker.person.jobDescriptor()} ${faker.person.jobType()} bridging ${faker.hacker.noun()} and ${faker.company.buzzNoun()}`
  ]
  
  return faker.helpers.arrayElement(bioTypes)()
}

/**
 * Generate realistic avatar URLs using a deterministic approach
 */
function generateAvatarUrl(seed: number): string {
  const avatarServices = [
    (id: number) => `https://images.unsplash.com/photo-${1470000000 + id}?w=100&h=100&fit=crop&crop=face`,
    (id: number) => `https://api.dicebear.com/7.x/avataaars/svg?seed=${id}`,
    (id: number) => `https://api.dicebear.com/7.x/personas/svg?seed=${id}`,
    (id: number) => `https://robohash.org/${id}?set=set1&size=100x100`
  ]
  
  const serviceIndex = seed % avatarServices.length
  return avatarServices[serviceIndex]!(seed)
}

/**
 * Generate realistic usernames with better determinism
 */
function generateUsername(firstName: string, index: number): string {
  const sanitizedFirstName = firstName.toLowerCase().replace(/[^a-z]/g, '')
  const suffixes = ['dev', 'code', 'tech', 'eng', 'js', 'build', 'craft', 'make']
  const suffix = suffixes[index % suffixes.length]
  return `${sanitizedFirstName}_${suffix}${(index + 1).toString().padStart(2, '0')}`
}

/**
 * Check if database already has data
 */
async function isDatabaseSeeded(): Promise<boolean> {
  try {
    const { db } = await import('$/db')
    const { schema } = await import('$/db/schema')
    const userCount = await db.select().from(schema.user).limit(1)
    return userCount.length > 0
  } catch (error) {
    console.error('Error checking if database is seeded:', error)
    return false
  }
}

/**
 * Main seeding function
 */
async function seedDatabase() {
  console.log('🚀 Starting database seeding...')
  
  try {
    // Dynamic imports to avoid early initialization issues
    const { extractBookmarksFromHtml } = await import('$/services/import')
    const { importBookmarksBulk } = await import('$/db/operations/import')
    const { db } = await import('$/db')
    const { schema } = await import('$/db/schema')
    
    // Parse real bookmarks from HTML file
    const bookmarksPath = join(process.cwd(), 'mocks', 'bookmarks_28_10_2024.html')
    const htmlContent = readFileSync(bookmarksPath, 'utf-8')
    const realBookmarks = extractBookmarksFromHtml(htmlContent)
    console.log(`📚 Parsed ${realBookmarks.length} real bookmarks from HTML file`)
    
    // Generate 10 users with authentication first
    console.log('👥 Creating 10 realistic users...')
    
    // Get existing emails, usernames, and account IDs to avoid conflicts
    const existingUsers = await db.select({
      email: schema.user.email,
      username: schema.user.username
    }).from(schema.user)
    
    const existingAccounts = await db.select({
      accountId: schema.account.accountId,
      userId: schema.account.userId
    }).from(schema.account)
    
    const existingEmails = new Set(existingUsers.map(u => u.email))
    const existingUsernames = new Set(existingUsers.map(u => u.username).filter(Boolean))
    const existingAccountIds = new Set(existingAccounts.map(a => a.accountId))
    const existingAccountUserIds = new Set(existingAccounts.map(a => a.userId))
    
    const users: any[] = []
    const accounts: any[] = []
    
    for (let i = 0; i < 10; i++) {
      let firstName = faker.person.firstName()
      let lastName = faker.person.lastName()
      let email = faker.internet.email({ firstName, lastName })
      let username = generateUsername(firstName, i)
      
      // Ensure email is unique
      let emailCounter = 1
      while (existingEmails.has(email)) {
        email = faker.internet.email({ firstName, lastName, provider: `test${emailCounter}.com` })
        emailCounter++
      }
      existingEmails.add(email)
      
      // Ensure username is unique
      let usernameCounter = 1
      while (existingUsernames.has(username)) {
        username = `${generateUsername(firstName, i)}_${usernameCounter}`
        usernameCounter++
      }
      existingUsernames.add(username)
      
      const userId = faker.string.uuid()
      
      // Skip if this user already has an account
      if (existingAccountUserIds.has(userId)) {
        continue
      }
      
      users.push({
        id: userId,
        name: `${firstName} ${lastName}`,
        email: email,
        username: username,
        displayUsername: username,
        bio: i % 4 === 0 ? null : generateUserBio(), // 25% chance of no bio
        role: 'USER',
        emailVerified: faker.datatype.boolean({ probability: 0.9 }),
        onboarded: faker.datatype.boolean({ probability: 0.95 }),
        image: i % 3 === 0 ? null : generateAvatarUrl(i), // 33% chance of no image
        avatarUrl: i % 3 === 0 ? null : generateAvatarUrl(i + 100), // 33% chance of no avatar
        createdAt: faker.date.between({ 
          from: new Date('2024-01-01'), 
          to: new Date() 
        }),
        updatedAt: new Date()
      })
      
      // Ensure accountId (email) is unique for accounts table
      if (!existingAccountIds.has(email)) {
        accounts.push({
          // Remove id field - let it auto-generate
          accountId: email,
          providerId: 'credential',
          userId: userId,
          password: faker.internet.password({ length: 20, prefix: '$2a$10$' }),
          createdAt: new Date(),
          updatedAt: new Date()
        })
        existingAccountIds.add(email)
      }
    }

    // Insert users and accounts using direct database operations
    console.log('💾 Inserting users and accounts...')
    await db.insert(schema.user).values(users)
    await db.insert(schema.account).values(accounts)
    
    // Create follow relationships between users
    console.log('🤝 Creating follow relationships...')
    const followers: any[] = []
    const userIds = users.map(u => u.id)
    
    // Generate realistic follow patterns (some users follow others, some follow back)
    for (let i = 0; i < 25; i++) {
      const followerId = faker.helpers.arrayElement(userIds)
      const followeeId = faker.helpers.arrayElement(userIds.filter(id => id !== followerId))
      
      // Check if this relationship already exists in our array
      const exists = followers.some(f => 
        f.followerId === followerId && f.followeeId === followeeId
      )
      
      if (!exists) {
        followers.push({
          followerId,
          followeeId,
          createdAt: faker.date.between({ 
            from: new Date('2024-01-01'), 
            to: new Date() 
          })
        })
      }
    }
    
    await db.insert(schema.follower).values(followers)
    
    // Distribute bookmarks among users using the existing import functions
    console.log('📖 Distributing bookmarks among users...')
    
    // Shuffle bookmarks for random distribution
    const shuffledBookmarks = faker.helpers.shuffle([...realBookmarks])
    
    let totalImported = 0
    let totalSkipped = 0
    
    for (let i = 0; i < users.length; i++) {
      const user = users[i]
      
      // Each user gets a random number of bookmarks (5-60)
      const bookmarkCount = faker.number.int({ min: 5, max: 60 })
      const startIndex = (i * 60) % shuffledBookmarks.length
      const userBookmarks = []
      
      // Get bookmarks for this user, wrapping around if needed
      for (let j = 0; j < bookmarkCount; j++) {
        const bookmarkIndex = (startIndex + j) % shuffledBookmarks.length
        const bookmark = shuffledBookmarks[bookmarkIndex]
        if (bookmark) {
          userBookmarks.push(bookmark)
        }
      }
      
      console.log(`  📚 Importing ${userBookmarks.length} bookmarks for user ${user.name}...`)
      
      // Use the existing import function to properly handle links, tags, etc.
      const importResult = await importBookmarksBulk(userBookmarks, user.id, 50)
      
      totalImported += importResult.imported
      totalSkipped += importResult.skipped
      
      if (importResult.errors?.length) {
        console.log(`    ⚠️  Errors for ${user.name}:`, importResult.errors.slice(0, 3))
      }
    }
    
    // Create some reading progress manually
    console.log('📊 Creating reading progress data manually...')
    
    const existingBookmarksForMeta = await db.select({
      id: schema.bookmark.id,
      userId: schema.bookmark.userId 
    }).from(schema.bookmark).limit(200); // Fetch a pool of bookmarks

    // Get existing bookmark meta to avoid duplicates
    const existingBookmarkMeta = await db.select({
      bookmarkId: schema.bookmarkMeta.bookmarkId,
      userId: schema.bookmarkMeta.userId
    }).from(schema.bookmarkMeta)
    
    const existingMetaPairs = new Set(
      existingBookmarkMeta.map(m => `${m.bookmarkId}:${m.userId}`)
    )

    let createdBookmarkMetasCount = 0;

    if (existingBookmarksForMeta.length === 0) {
      console.log('   ⚠️ No existing bookmarks found, skipping bookmarkMeta seeding.');
    } else {
      const bookmarkMetasToInsert: typeof schema.bookmarkMeta.$inferInsert[] = [];
      const numBookmarkMetasToCreate = 80;
      const shuffledBookmarks = faker.helpers.shuffle([...existingBookmarksForMeta])

      for (let i = 0; i < numBookmarkMetasToCreate && i < shuffledBookmarks.length; i++) {
        const randomBookmark = shuffledBookmarks[i];
        
        if (!randomBookmark.id || !randomBookmark.userId) {
            console.warn(`Skipping bookmarkMeta for a bookmark with missing id or userId.`);
            continue;
        }

        const metaPairKey = `${randomBookmark.id}:${randomBookmark.userId}`
        
        // Skip if this bookmark already has meta for this user
        if (existingMetaPairs.has(metaPairKey)) {
          continue;
        }

        bookmarkMetasToInsert.push({
          id: faker.string.uuid(),
          bookmarkId: randomBookmark.id,
          userId: randomBookmark.userId,
          readStatus: faker.helpers.arrayElement(['unread', 'in-progress', 'completed'] as const),
          readPercentage: faker.number.int({ min: 0, max: 100 }),
          visitCount: faker.number.int({ min: 1, max: 15 }),
          timeSpentSeconds: faker.number.int({ min: 30, max: 3600 }),
          lastReadAt: faker.date.between({ 
            from: new Date('2024-01-01'), 
            to: new Date() 
          }),
          createdAt: new Date(),
          updatedAt: new Date(),
        });
        
        // Add to set to avoid duplicates within this batch
        existingMetaPairs.add(metaPairKey);
      }

      if (bookmarkMetasToInsert.length > 0) {
        await db.insert(schema.bookmarkMeta).values(bookmarkMetasToInsert);
        createdBookmarkMetasCount = bookmarkMetasToInsert.length;
      }
    }

    console.log('✅ Database seeding completed successfully!')
    console.log('📊 Created:')
    console.log(`   - ${users.length} realistic users with authentication`)
    console.log(`   - ${followers.length} follow relationships between users`)
    console.log(`   - ${totalImported} bookmarks imported from real data`)
    console.log(`   - ${totalSkipped} bookmarks skipped (duplicates/invalid)`)
    console.log(`   - ${createdBookmarkMetasCount} bookmark reading progress records created manually`)
    console.log('')
    console.log('🎯 Users now have a realistic dataset with:')
    console.log('   - Real URLs and titles from your bookmark export')
    console.log('   - Programmatically generated user profiles using Faker.js')
    console.log('   - Social connections (followers/following)')
    console.log('   - Reading progress and notes on bookmarks')
    console.log('   - Proper tags extracted from bookmark folders and content')
    
  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}

export default defineNitroPlugin(async () => {
  console.log('🔍 Nitro seed plugin loaded! SEED_DATABASE =', process.env.SEED_DATABASE)
  
  if (process.env.SEED_DATABASE === 'true') {
    try {
      console.log('🌱 SEED_DATABASE=true detected, starting database seeding...')
      
      // Check if database has existing data (for informational purposes)
      const alreadySeeded = await isDatabaseSeeded()
      if (alreadySeeded) {
        console.log('🌱 Database contains existing data, but proceeding with seeding anyway...')
      }
      
      console.log('🌱 Starting database seeding...')
      await seedDatabase()
      
    } catch (error) {
      console.error('❌ Seeding plugin error:', error)
      // Don't throw here to avoid breaking the app startup
    }
  } else {
    console.log('ℹ️  SEED_DATABASE not set to true, skipping seeding...')
  }
})