import { eq, desc, and, sql, inArray } from 'drizzle-orm'
import type { PgTransaction as DrizzlePgTransaction } from 'drizzle-orm/pg-core'
import { v7 as uuidv7 } from 'uuid'
import { db } from '$/db'
import { link } from '$/db/schema'
import { popularUrlsView, refreshPopularUrlsView, type PopularUrl } from '$/db/views/popular/popular-urls'

// Define a simplified transaction type to avoid generic type arguments
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type PgTransaction = DrizzlePgTransaction<any, any, any>

/**
 * Link data structure
 */
export interface LinkData {
  url: string
  title?: string
  description?: string
}

/**
 * Find or create a link record in the database
 */
export async function findOrCreateLink(urlString: string, title: string = '', description: string = ''): Promise<typeof link.$inferSelect> {
  const linkResult = await db.query.link.findFirst({
    where: eq(link.url, urlString)
  })

  if (linkResult) return linkResult

  const [newLink] = await db.insert(link)
    .values({
      id: uuidv7(),
      url: urlString,
      title: title || '',
      description: description || ''
    })
    .returning()

  if (!newLink) {
    throw new Error(`Failed to create link record for: ${urlString}`)
  }

  return newLink
}

/**
 * Save link record with optional metadata
 */
export async function saveLink(data: LinkData): Promise<typeof link.$inferSelect | null> {
  try {
    const existingLink = await db.query.link.findFirst({
      where: eq(link.url, data.url)
    })

    if (existingLink) {
      // Update existing link if we have new data
      if (data.title || data.description) {
        const [updatedLink] = await db
          .update(link)
          .set({
            title: data.title || existingLink.title,
            description: data.description || existingLink.description
          })
          .where(eq(link.id, existingLink.id))
          .returning()
        
        return updatedLink
      }
      return existingLink
    }

    // Create new link
    const [newLink] = await db
      .insert(link)
      .values({
        id: uuidv7(),
        url: data.url,
        title: data.title || '',
        description: data.description || ''
      })
      .returning()

    return newLink
  } catch (error) {
    console.error('Error saving link:', error)
    return null
  }
}

/**
 * Refresh the popular_urls materialized view
 * This should be called periodically (e.g., hourly) by a scheduled task
 */
export async function refreshPopularUrls() {
  try {
    await refreshPopularUrlsView()
    console.log('Popular URLs materialized view refreshed at', new Date().toISOString())
    return true
  }
  catch (error) {
    console.error('Error refreshing popular URLs view:', error)
    return false
  }
}

/**
 * Get popular URLs for a specific time period
 * @param period Time period ('day', 'week', 'month', 'year', 'all')
 * @param limit Maximum number of results to return
 * @param offset Pagination offset
 * @param excludeNsfw Whether to exclude NSFW content
 * @returns Array of popular URLs
 */
export async function getPopularUrls({
  period = 'all',
  limit = 20,
  offset = 0,
  excludeNsfw = true
}: {
  period?: 'day' | 'week' | 'month' | 'year' | 'all'
  limit?: number
  offset?: number
  excludeNsfw?: boolean
}): Promise<PopularUrl[]> {
  try {
    // Build conditions
    const conditions = []

    if (excludeNsfw) {
      conditions.push(eq(popularUrlsView.isNsfw, false))
    }

    // Determine which score to order by
    let orderByColumn
    if (period === 'day') {
      orderByColumn = popularUrlsView.dailyScore
    }
    else if (period === 'week') {
      orderByColumn = popularUrlsView.weeklyScore
    }
    else if (period === 'month') {
      orderByColumn = popularUrlsView.monthlyScore
    }
    else if (period === 'year') {
      orderByColumn = popularUrlsView.yearlyScore
    }
    else {
      orderByColumn = popularUrlsView.popularityScore
    }

    // Execute the query with all conditions
    const results = await db.select()
      .from(popularUrlsView)
      .where(conditions.length ? and(...conditions) : undefined)
      .orderBy(desc(orderByColumn))
      .limit(limit)
      .offset(offset)

    return results
  }
  catch (error) {
    console.error('Error fetching popular URLs:', error)

    // Fallback query if the materialized view is not available
    try {
      console.log('Attempting fallback query for popular URLs')

      // Simple fallback query based on bookmark counts
      const fallbackResults = await db.execute(sql`
        SELECT
          l.id AS "linkId",
          l.url,
          l.title,
          l.description,
          l.is_nsfw AS "isNsfw",
          COUNT(b.id) AS "saveCount",
          COUNT(DISTINCT b.user_id) AS "uniqueUsers",
          MIN(b.created_at) AS "firstSavedAt",
          MAX(b.created_at) AS "lastSavedAt",
          b.tag_names AS tags
        FROM link l
        JOIN bookmark b ON l.id = b.link_id
        WHERE
          b.is_private = false
          ${excludeNsfw ? sql`AND l.is_nsfw = false` : sql``}
          ${period === 'day'
            ? sql`AND b.created_at > NOW() - INTERVAL '1 day'`
            : period === 'week'
              ? sql`AND b.created_at > NOW() - INTERVAL '7 days'`
              : period === 'month'
                ? sql`AND b.created_at > NOW() - INTERVAL '30 days'`
                : period === 'year' ? sql`AND b.created_at > NOW() - INTERVAL '365 days'` : sql``}
        GROUP BY l.id, l.url, l.title, l.description, l.is_nsfw, b.tag_names
        ORDER BY "saveCount" DESC
        LIMIT ${limit}
        OFFSET ${offset}
      `)

      return fallbackResults.rows as unknown as PopularUrl[]
    }
    catch (fallbackError) {
      console.error('Fallback query also failed:', fallbackError)
      throw error
    }
  }
}

/**
 * Efficiently finds or creates links in bulk
 * Optimized for large batch operations during imports
 *
 * @param linksData Array of link data objects with url and title
 * @param tx Optional transaction object for batch operations
 * @returns Array of link records with id, url, and title
 */
export async function bulkFindOrCreateLinks(
  linksData: { url: string, title: string | null, favicon?: string | null }[],
  tx?: PgTransaction
): Promise<{ id: string, url: string, title: string | null, favicon?: string | null }[]> {
  if (!linksData.length) return []

  // Use provided transaction or create a new one
  const executor = tx || db

  // Extract unique URLs
  const urlMap = new Map<string, { url: string, title: string | null, favicon?: string | null }>()
  linksData.forEach(l => urlMap.set(l.url, l))
  const uniqueLinks = Array.from(urlMap.values())
  const urlStrings = uniqueLinks.map(l => l.url)

  // Find existing links in a single query
  const existingLinks = await executor
    .select({
      id: link.id,
      url: link.url,
      title: link.title,
      favicon: link.favicon
    })
    .from(link)
    .where(inArray(link.url, urlStrings))

  // Create a map for quick lookup
  const existingUrlMap = new Map(existingUrls.map(u => [u.url, u]))

  // Filter URLs that need to be created
  const urlsToCreate = uniqueUrls.filter(u => !existingUrlMap.has(u.url))

  // If we have new URLs to create, bulk insert them
  if (urlsToCreate.length > 0) {
    const newUrlRows = urlsToCreate.map(u => ({
      id: uuidv7(),
      url: u.url,
      title: u.title || '',
      favicon: u.favicon || null,
      createdAt: new Date()
    }))

    // Bulk insert all new URLs in a single operation
    const insertedUrls = await executor.insert(urls).values(newUrlRows).returning({
      id: urls.id,
      url: urls.url,
      title: urls.title,
      favicon: urls.favicon
    })

    // Combine existing and newly inserted URLs
    return [...existingUrls, ...insertedUrls]
  }

  // If no new URLs, just return the existing ones
  return existingUrls
}

/**
 * Update URL metadata in bulk
 * This is useful for updating favicon URLs and other metadata after import
 *
 * @param urlUpdates Array of URL updates with id and metadata
 * @param tx Optional transaction object for batch operations
 * @returns Array of updated URL records
 */
export async function bulkUpdateUrls(
  urlUpdates: { id: string, title?: string, description?: string, favicon?: string | null }[],
  tx?: PgTransaction
): Promise<typeof urls.$inferSelect[]> {
  if (!urlUpdates.length) return []

  // Use provided transaction or create a new one
  const executor = tx || db
  const results: typeof urls.$inferSelect[] = []

  // Process in batches to avoid overwhelming the database
  const BATCH_SIZE = 100
  for (let i = 0; i < urlUpdates.length; i += BATCH_SIZE) {
    const batch = urlUpdates.slice(i, i + BATCH_SIZE)

    // Process each URL update in the batch
    for (const update of batch) {
      const [updated] = await executor
        .update(urls)
        .set({
          title: update.title !== undefined ? update.title : undefined,
          description: update.description !== undefined ? update.description : undefined,
          favicon: update.favicon !== undefined ? update.favicon : undefined,
          updatedAt: new Date()
        })
        .where(eq(urls.id, update.id))
        .returning()

      if (updated) {
        results.push(updated)
      }
    }
  }

  return results
}
