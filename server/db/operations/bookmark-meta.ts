import { eq, and } from 'drizzle-orm'
import { v7 as uuidv7 } from 'uuid'
import { db } from '$/db'
import { schema } from '$/db/schema'

// Define schema references
const bookmark = schema.bookmark
const bookmarkMeta = schema.bookmarkMeta

/**
 * Types for bookmark meta operations
 */
export interface BookmarkMeta {
  id: string;
  bookmarkId: string;
  userId: string;
  readStatus: string;
  readPercentage: number;
  lastReadAt: Date | null;
  visitCount: number;
  timeSpentSeconds: number;
  createdAt: Date;
  updatedAt: Date | null;
}

export interface NewBookmarkMeta {
  id?: string;
  bookmarkId: string;
  userId: string;
  readStatus?: string;
  readPercentage?: number;
  lastReadAt?: Date | null;
  visitCount?: number;
  timeSpentSeconds?: number;
}

export interface UpdateBookmarkMeta {
  readStatus?: string;
  readPercentage?: number;
  lastReadAt?: Date;
  visitCount?: number;
  timeSpentSeconds?: number;
}

/**
 * Get bookmark meta for a bookmark
 * @param bookmarkId ID of the bookmark
 * @param userId ID of the user
 * @returns BookmarkMeta object or null if not found
 */
export async function getBookmarkMeta(bookmarkId: string, userId: string): Promise<BookmarkMeta | null> {
  try {
    // Query the bookmark_meta table for existing progress
    const record = await db.query.bookmarkMeta.findFirst({
      where: and(
        eq(bookmarkMeta.bookmarkId, bookmarkId),
        eq(bookmarkMeta.userId, userId)
      )
    })
    return record ?? null
  } catch (error) {
    console.error('Error getting bookmark meta:', error)
    throw new Error('Failed to get bookmark meta')
  }
}

/**
 * Create a new bookmark meta entry
 * @param data New bookmark meta data
 * @returns Created bookmark meta
 */
export async function createBookmarkMeta(data: NewBookmarkMeta): Promise<BookmarkMeta> {
  try {
    // Generate a new ID if not provided
    const id = data.id ?? uuidv7()

    // Insert new metadata record
    const [created] = await db.insert(bookmarkMeta)
      .values({
        id,
        bookmarkId: data.bookmarkId,
        userId: data.userId,
        readStatus: data.readStatus ?? 'unread',
        readPercentage: data.readPercentage ?? 0,
        lastReadAt: data.lastReadAt ?? null,
        visitCount: data.visitCount ?? 0,
        timeSpentSeconds: data.timeSpentSeconds ?? 0
      })
      .returning()

    if (!created) {
      throw new Error('Failed to create bookmark meta')
    }

    return created
  } catch (error) {
    console.error('Error creating bookmark meta:', error)
    throw error
  }
}

/**
 * Find or create a bookmark meta entry
 * @param bookmarkId The bookmark ID
 * @param userId The user ID
 * @returns The existing or newly created bookmark meta
 */
export async function findOrCreateBookmarkMeta(bookmarkId: string, userId: string): Promise<BookmarkMeta> {
  // Check if it already exists
  const existing = await getBookmarkMeta(bookmarkId, userId)
  
  if (existing) {
    return existing
  }
  
  // Create a new entry
  return await createBookmarkMeta({
    bookmarkId,
    userId,
    readStatus: 'unread',
    readPercentage: 0
  })
}

/**
 * Update bookmark meta
 * @param bookmarkId ID of the bookmark
 * @param userId ID of the user
 * @param data Data to update
 * @returns Updated bookmark meta
 */
export async function updateBookmarkMeta(
  bookmarkId: string,
  userId: string,
  data: UpdateBookmarkMeta
): Promise<BookmarkMeta | null> {
  try {
    // Ensure bookmark belongs to user
    const bookmarkExists = await db.query.bookmark.findFirst({
      where: and(
        eq(bookmark.id, bookmarkId),
        eq(bookmark.userId, userId)
      )
    })
    if (!bookmarkExists) {
      throw new Error('Bookmark not found or does not belong to the user')
    }
    
    // Check if metadata exists
    const existingMeta = await db.query.bookmarkMeta.findFirst({
      where: and(
        eq(bookmarkMeta.bookmarkId, bookmarkId),
        eq(bookmarkMeta.userId, userId)
      )
    })
    
    let result
    const now = new Date()
    
    if (existingMeta) {
      // Update existing record
      const [updated] = await db.update(bookmarkMeta)
        .set({
          ...data,
          updatedAt: now
        })
        .where(eq(bookmarkMeta.id, existingMeta.id))
        .returning()
      result = updated
    } else {
      // Insert new metadata record
      const [inserted] = await db.insert(bookmarkMeta)
        .values({
          id: uuidv7(),
          bookmarkId,
          userId,
          readStatus: data.readStatus ?? 'unread',
          readPercentage: data.readPercentage ?? 0,
          lastReadAt: data.lastReadAt ?? now,
          visitCount: data.visitCount ?? 1,
          timeSpentSeconds: data.timeSpentSeconds ?? 0
        })
        .returning()
      result = inserted
    }
    
    return result ?? null
  } catch (error) {
    console.error('Error updating bookmark meta:', error)
    throw error
  }
}

/**
 * Mark a bookmark as completed
 * @param bookmarkId ID of the bookmark
 * @param userId ID of the user
 * @returns Updated bookmark meta
 */
export async function markBookmarkAsCompleted(bookmarkId: string, userId: string): Promise<BookmarkMeta | null> {
  return updateBookmarkMeta(bookmarkId, userId, {
    readStatus: 'completed',
    readPercentage: 100,
    lastReadAt: new Date()
  })
}

/**
 * Mark a bookmark as unread
 * @param bookmarkId ID of the bookmark
 * @param userId ID of the user
 * @returns Updated bookmark meta
 */
export async function markBookmarkAsUnread(bookmarkId: string, userId: string): Promise<BookmarkMeta | null> {
  return updateBookmarkMeta(bookmarkId, userId, {
    readStatus: 'unread',
    readPercentage: 0,
    lastReadAt: new Date()
  })
}

/**
 * Increment visit count for a bookmark
 * @param bookmarkId ID of the bookmark
 * @param userId ID of the user
 * @param seconds Time spent in seconds
 * @returns Updated bookmark meta
 */
export async function incrementBookmarkVisit(
  bookmarkId: string, 
  userId: string,
  seconds: number = 0
): Promise<BookmarkMeta | null> {
  // Get current metadata
  const meta = await getBookmarkMeta(bookmarkId, userId)
  
  if (!meta) {
    // Create new metadata with visit count 1
    return createBookmarkMeta({
      bookmarkId,
      userId,
      visitCount: 1,
      timeSpentSeconds: seconds
    })
  }
  
  // Update existing metadata
  return updateBookmarkMeta(bookmarkId, userId, {
    visitCount: meta.visitCount + 1,
    timeSpentSeconds: meta.timeSpentSeconds + seconds,
    lastReadAt: new Date()
  })
}

// Backward compatibility functions
export const getReadingProgress = getBookmarkMeta
export const updateReadingProgress = updateBookmarkMeta
