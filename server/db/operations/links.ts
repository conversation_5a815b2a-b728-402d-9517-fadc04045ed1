import { eq, desc, and, sql, inArray } from 'drizzle-orm'
import type { PgTransaction as DrizzlePgTransaction } from 'drizzle-orm/pg-core'
import { v7 as uuidv7 } from 'uuid'
import { db } from '$/db'
import { schema } from '$/db/schema'

// Define a simplified transaction type to avoid generic type arguments
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type PgTransaction = DrizzlePgTransaction<any, any, any>

// Define Link table reference
const link = schema.link

/**
 * Structure for popular URL data
 */
export interface PopularLink {
  linkId: string
  url: string
  title: string | null
  description: string | null
  isNsfw: boolean
  saveCount: number
  uniqueUsers: number
  firstSavedAt: Date
  lastSavedAt: Date
  tags: string[]
}

/**
 * Link data structure
 */
export interface LinkData {
  url: string
  title?: string
  description?: string
}

/**
 * Find or create a link record in the database
 */
export async function findOrCreateLink(urlString: string, title: string = '', description: string = ''): Promise<typeof link.$inferSelect> {
  const linkResult = await db.query.link.findFirst({
    where: eq(link.url, urlString)
  })

  if (linkResult) return linkResult

  const [newLink] = await db.insert(link)
    .values({
      id: uuidv7(),
      url: urlString,
      title: title || '',
      description: description || ''
    })
    .returning()

  if (!newLink) {
    throw new Error(`Failed to create link record for: ${urlString}`)
  }

  return newLink
}

/**
 * Save link record with optional metadata
 */
export async function saveLink(data: LinkData): Promise<typeof link.$inferSelect | null> {
  try {
    const existingLink = await db.query.link.findFirst({
      where: eq(link.url, data.url)
    })

    if (existingLink) {
      // Update existing link if we have new data
      if (data.title || data.description) {
        const [updatedLink] = await db
          .update(link)
          .set({
            title: data.title || existingLink.title,
            description: data.description || existingLink.description
          })
          .where(eq(link.id, existingLink.id))
          .returning()
        
        return updatedLink
      }
      return existingLink
    }

    // Create new link
    const [newLink] = await db
      .insert(link)
      .values({
        id: uuidv7(),
        url: data.url,
        title: data.title || '',
        description: data.description || ''
      })
      .returning()

    return newLink
  } catch (error) {
    console.error('Error saving link:', error)
    return null
  }
}

/**
 * Refresh popular links statistics
 * This should be called periodically (e.g., hourly) by a scheduled task
 */
export async function refreshPopularLinks(): Promise<boolean> {
  try {
    // Instead of refreshing a materialized view, we'll update the bookmark counts in the link table
    await db.execute(sql`
      UPDATE link l
      SET bookmark_count = (
        SELECT COUNT(*)
        FROM bookmark b
        WHERE b.link_id = l.id
      )
    `)
    
    console.log('Popular links statistics refreshed at', new Date().toISOString())
    return true
  }
  catch (error) {
    console.error('Error refreshing popular links statistics:', error)
    return false
  }
}

/**
 * Get popular URLs for a specific time period
 * @param period Time period ('day', 'week', 'month', 'year', 'all')
 * @param limit Maximum number of results to return
 * @param offset Pagination offset
 * @param excludeNsfw Whether to exclude NSFW content
 * @returns Array of popular URLs
 */
export async function getPopularLinks({
  period = 'all',
  limit = 20,
  offset = 0,
  excludeNsfw = true
}: {
  period?: 'day' | 'week' | 'month' | 'year' | 'all'
  limit?: number
  offset?: number
  excludeNsfw?: boolean
}): Promise<PopularLink[]> {
  try {
    // Build time filter based on period
    let timeFilter = sql``
    if (period === 'day') {
      timeFilter = sql`AND b.created_at > NOW() - INTERVAL '1 day'`
    } else if (period === 'week') {
      timeFilter = sql`AND b.created_at > NOW() - INTERVAL '7 days'`
    } else if (period === 'month') {
      timeFilter = sql`AND b.created_at > NOW() - INTERVAL '30 days'`
    } else if (period === 'year') {
      timeFilter = sql`AND b.created_at > NOW() - INTERVAL '365 days'`
    }

    // Build query with direct SQL for performance
    const results = await db.execute(sql`
      SELECT
        l.id AS "linkId",
        l.url,
        l.title,
        l.description,
        l.is_nsfw AS "isNsfw",
        COUNT(b.id) AS "saveCount",
        COUNT(DISTINCT b.user_id) AS "uniqueUsers",
        MIN(b.created_at) AS "firstSavedAt",
        MAX(b.created_at) AS "lastSavedAt",
        l.tag_names AS tags
      FROM link l
      JOIN bookmark b ON l.id = b.link_id
      WHERE
        b.is_private = false
        ${excludeNsfw ? sql`AND l.is_nsfw = false` : sql``}
        ${timeFilter}
      GROUP BY l.id, l.url, l.title, l.description, l.is_nsfw, l.tag_names
      ORDER BY "saveCount" DESC
      LIMIT ${limit}
      OFFSET ${offset}
    `)

    return results.rows as unknown as PopularLink[]
  }
  catch (error) {
    console.error('Error fetching popular URLs:', error)
    throw new Error('Failed to fetch popular URLs')
  }
}

/**
 * Efficiently finds or creates links in bulk
 * Optimized for large batch operations during imports
 *
 * @param linksData Array of link data objects with url and title
 * @param tx Optional transaction object for batch operations
 * @returns Array of link records with id, url, and title
 */
export async function bulkFindOrCreateLinks(
  linksData: { url: string, title: string | null, favicon?: string | null }[],
  tx?: PgTransaction
): Promise<{ id: string, url: string, title: string | null, favicon?: string | null }[]> {
  if (!linksData.length) return []

  // Use provided transaction or create a new one
  const executor = tx || db

  // Extract unique URLs
  const urlMap = new Map<string, { url: string, title: string | null, favicon?: string | null }>()
  linksData.forEach(l => urlMap.set(l.url, l))
  const uniqueLinks = Array.from(urlMap.values())
  const urlStrings = uniqueLinks.map(l => l.url)

  // Find existing links in a single query
  const existingLinks = await executor
    .select({
      id: link.id,
      url: link.url,
      title: link.title,
      favicon: link.favicon
    })
    .from(link)
    .where(inArray(link.url, urlStrings))

  // Create a map for quick lookup
  const existingLinkMap = new Map(existingLinks.map(l => [l.url, l]))

  // Filter URLs that need to be created
  const linksToCreate = uniqueLinks.filter(l => !existingLinkMap.has(l.url))

  // If we have new links to create, bulk insert them
  if (linksToCreate.length > 0) {
    const newLinkRows = linksToCreate.map(l => ({
      id: uuidv7(),
      url: l.url,
      title: l.title || '',
      favicon: l.favicon || null
    }))

    // Bulk insert all new links in a single operation
    const insertedLinks = await executor.insert(link).values(newLinkRows).returning({
      id: link.id,
      url: link.url,
      title: link.title,
      favicon: link.favicon
    })

    // Combine existing and newly inserted links
    return [...existingLinks, ...insertedLinks]
  }

  // If no new links, just return the existing ones
  return existingLinks
}

/**
 * Update link metadata in bulk
 * This is useful for updating favicon URLs and other metadata after import
 *
 * @param linkUpdates Array of link updates with id and metadata
 * @param tx Optional transaction object for batch operations
 * @returns Array of updated link records
 */
export async function bulkUpdateLinks(
  linkUpdates: { id: string, title?: string, description?: string, favicon?: string | null }[],
  tx?: PgTransaction
): Promise<typeof link.$inferSelect[]> {
  if (!linkUpdates.length) return []

  // Use provided transaction or create a new one
  const executor = tx || db
  const results: typeof link.$inferSelect[] = []

  // Process in batches to avoid overwhelming the database
  const BATCH_SIZE = 100
  for (let i = 0; i < linkUpdates.length; i += BATCH_SIZE) {
    const batch = linkUpdates.slice(i, i + BATCH_SIZE)

    // Process each link update in the batch
    for (const update of batch) {
      const [updated] = await executor
        .update(link)
        .set({
          title: update.title !== undefined ? update.title : undefined,
          description: update.description !== undefined ? update.description : undefined,
          favicon: update.favicon !== undefined ? update.favicon : undefined,
          updatedAt: new Date()
        })
        .where(eq(link.id, update.id))
        .returning()

      if (updated) {
        results.push(updated)
      }
    }
  }

  return results
}

/**
 * Decrement usage count for a link and potentially remove it if no longer used
 * @param linkId ID of the link to decrement usage for
 * @returns Boolean indicating if the link was deleted
 */
export async function decrementLinkUsage(linkId: string): Promise<boolean> {
  return await db.transaction(async (tx) => {
    // Get the current link info
    const linkInfo = await tx.query.link.findFirst({
      where: eq(link.id, linkId)
    })

    if (!linkInfo) {
      return false // Link doesn't exist
    }

    // Check if any bookmarks still reference this link
    const bookmarkCount = await tx.select({ count: sql<number>`count(*)` })
      .from(schema.bookmark)
      .where(eq(schema.bookmark.linkId, linkId))
      .then(result => result[0]?.count || 0)

    if (bookmarkCount <= 1) {
      // This was the last bookmark using this link, delete the link
      await tx.delete(link)
        .where(eq(link.id, linkId))
      return true
    }

    // Update the bookmark count
    await tx.update(link)
      .set({ 
        bookmarkCount: Math.max(0, (linkInfo.bookmarkCount || 1) - 1),
        updatedAt: new Date()
      })
      .where(eq(link.id, linkId))
    
    return false
  })
}
