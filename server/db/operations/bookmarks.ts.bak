import { eq, and, inArray, sql } from 'drizzle-orm'
import { v7 as uuidv7 } from 'uuid'
import { db } from '$/db'
import type { JobStatus } from '$/services/import'
import { getImportJobStatus as getJobStatus, cancelImportJob as cancelJob } from '$/services/import'
import { schema } from '$/db/schema'
import { findOrCreateLink } from '$/db/operations/links'
import { addTagsToBookmark, normalizeTagName } from '$/db/operations/tags'
import { getImportJobStatus as getJobStatus, cancelImportJob as cancelJob } from '$/services/import'
import { schema } from '$/db/schema'
import { findOrCreateLink } from '$/db/operations/links'
import { addTagsToBookmark } from '$/db/operations/tags'
import { normalizeTagName } from '$/db/operations/tags'

// Define schema references
const bookmark = schema.bookmark
const bookmarkEmbedding = schema.bookmarkEmbedding
const link = schema.link
const tag = schema.tag

const EMBEDDING_MODEL_NAME = 'rag_bge_small_en_v15'

/**
 * Single bookmark creation data
 */
export interface BookmarkCreateData {
  url: string
  title: string
  notes?: string | null
  tags?: string[]
  userId: string
  isPrivate?: boolean
  isReadLater?: boolean
}

/**
 * Response from bookmark creation
 */
export interface BookmarkCreateResult {
  id: string
  url: string
  title: string
  notes: string | null
  tags?: string[]
  isPrivate: boolean
  isReadLater: boolean
  createdAt: Date
}

/**
 * Check if a bookmark already exists for a user and URL
 */
export async function checkExistingBookmark(userId: string, linkId: string): Promise<boolean> {
  // Use prepared statement for better performance
  const existingBookmark = await db.query.bookmark.findFirst({
    where: and(
      eq(bookmark.userId, userId),
      eq(bookmark.linkId, linkId)
    )
  })

  return !!existingBookmark
}

/**
 * Create a single bookmark with tags and embedding
 */
export async function createBookmark(data: BookmarkCreateData): Promise<BookmarkCreateResult> {
  return await db.transaction(async (tx) => {
    // Find or create link
    const linkRecord = await findOrCreateLink(data.url, data.title)

    // Check if bookmark already exists
    const existingBookmark = await tx.query.bookmark.findFirst({
      where: and(
        eq(bookmark.userId, data.userId),
        eq(bookmark.linkId, linkRecord.id)
      )
    })

    if (existingBookmark) {
      throw new Error('You\'ve already bookmarked this URL')
    }

    // Prepare tags array
    const tagNames = data.tags?.length ? data.tags : []

    // Create bookmark
    const [newBookmark] = await tx.insert(bookmark)
      .values({
        userId: data.userId,
        linkId: linkRecord.id,
        notes: data.notes || null,
        isPrivate: data.isPrivate ?? false,
        isReadLater: data.isReadLater ?? false,
        tagNames // Use the array directly
      })
      .returning()

    if (!newBookmark) {
      throw new Error('Failed to create bookmark')
    }

    // Generate and save embedding
    if (linkRecord) {
      // Create a structured embedding text with key-value pairs and additional context
      const embeddingText = [
        // Core bookmark data with field labels to create structure
        `Title: ${linkRecord.title || ''}`,
        `URL: ${linkRecord.url || ''}`,
        
        // Include notes when available
        data.notes ? `Notes: ${data.notes}` : '',
        
        // Tags with proper formatting
        tagNames.length ? `Tags: ${tagNames.join(', ')}` : '',
        
        // Domain information for better context
        `Domain: ${new URL(linkRecord.url).hostname}`
      ]
        .filter(Boolean) // Remove any empty strings
        .join('\n') // Join with newlines for better structure

      try {
        // Use a placeholder value for embedding during testing
        await tx.insert(bookmarkEmbedding).values({
          id: uuidv7(),
          bookmarkId: newBookmark.id,
          chunk: embeddingText,
          embedding: null, // We'll update this later with a proper embedding
          modelName: EMBEDDING_MODEL_NAME,
          modelVersion: '1.0'
        })
      }
      catch (embeddingError) {
        console.error(`Failed to create embedding for bookmark ${newBookmark.id}:`, embeddingError)
        // Continue without embedding rather than failing the whole bookmark creation
      }
    }

    // Process tags if they were provided
    if (data.tags && data.tags.length > 0) {
      await addTagsToBookmark(newBookmark.id, data.tags)
    }

    // Return formatted result
    return {
      id: newBookmark.id,
      url: linkRecord.url,
      title: linkRecord.title || '',
      notes: newBookmark.notes,
      tags: data.tags,
      isPrivate: newBookmark.isPrivate,
      isReadLater: newBookmark.isReadLater,
      createdAt: newBookmark.createdAt || new Date()
    }
  })
}

/**
 * Get bookmark with resolved title
 * Uses userTitle if available, otherwise falls back to link's original title
 */
export const getBookmarkByBookmarkId = async (bookmarkId: string) => {
  // Use prepared statement with join for better performance
  const result = await db.query.bookmark.findFirst({
    where: eq(bookmark.id, bookmarkId),
    with: {
      link: true
    }
  })

  if (!result) return null

  // Use userTitle if available, otherwise fall back to link title
  const displayTitle = result.userTitle || result.link.title || ''

  return {
    ...result,
    displayTitle
  }
}

/**
 * Get all bookmarks for a user, including link and tags
 */
export const getBookmarksByUserId = async (userId: string) => {
  const bookmarks = await db.query.bookmark.findMany({
    where: eq(bookmark.userId, userId),
    with: {
      link: true
    },
    orderBy: (bookmark, { desc }) => [desc(bookmark.createdAt)]
  })

  return bookmarks
}


// Import job status functions - these are now just wrappers for backwards compatibility
export function getImportJobStatus(jobId: string): JobStatus | null {
  return getJobStatus(jobId)
}

export function cancelImportJob(jobId: string): boolean {
  return cancelJob(jobId)
}

/**
 * Data structure for bulk bookmark import
 */
export interface BookmarkBulkImportData {
  url: string;
  title: string;
  notes?: string | null;
  tags?: string[];
  isPrivate?: boolean;
  isReadLater?: boolean;
  // Additional fields that might come from imports
  importedAt?: Date;
  originalId?: string;
}

/**
 * Result of bulk bookmark import operation
 */
export interface BookmarkBulkImportResult {
  // Summary counts
  total: number;
  created: number;
  skipped: number; // Already existed
  failed: number;  // Failed due to validation or other issues
  
  // Created bookmark IDs for reference
  bookmarkIds: string[];
  
  // Stats
  linksCreated: number;
  tagsCreated: number;
  duration: number; // Processing time in ms
}

/**
 * Create multiple bookmarks efficiently for a user
 * Optimized for bulk operations with batched database operations
 */
export async function createBookmarksByUserId(
  userId: string,
  bookmarksData: BookmarkBulkImportData[],
  batchSize = 100
): Promise<BookmarkBulkImportResult> {
  const startTime = Date.now();
  const result: BookmarkBulkImportResult = {
    total: bookmarksData.length,
    created: 0,
    skipped: 0,
    failed: 0,
    bookmarkIds: [],
    linksCreated: 0,
    tagsCreated: 0,
    duration: 0
  };

  // Validate input
  if (!userId || !bookmarksData.length) {
    result.duration = Date.now() - startTime;
    return result;
  }

  // Process in batches to avoid memory issues with very large imports
  for (let i = 0; i < bookmarksData.length; i += batchSize) {
    const batch = bookmarksData.slice(i, i + batchSize);
    const batchResult = await processBatch(userId, batch);
    
    // Accumulate results
    result.created += batchResult.created;
    result.skipped += batchResult.skipped;
    result.failed += batchResult.failed;
    result.bookmarkIds.push(...batchResult.bookmarkIds);
    result.linksCreated += batchResult.linksCreated;
    result.tagsCreated += batchResult.tagsCreated;
  }

  result.duration = Date.now() - startTime;
  return result;
}

/**
 * Process a batch of bookmarks in a single transaction
 * This is an internal helper function for createBookmarksByUserId
 */
async function processBatch(
  userId: string,
  batch: BookmarkBulkImportData[]
): Promise<BookmarkBulkImportResult> {
  const batchResult: BookmarkBulkImportResult = {
    total: batch.length,
    created: 0,
    skipped: 0,
    failed: 0,
    bookmarkIds: [],
    linksCreated: 0,
    tagsCreated: 0,
    duration: 0
  };

  // Skip empty batches
  if (!batch.length) return batchResult;

  const transactionResult = await db.transaction(async (tx) => {
    try {
      // 1. Extract all unique URLs and find existing links
      const uniqueUrls = [...new Set(batch.map(b => b.url))];
      const existingLinks = await tx.query.link.findMany({
        where: inArray(link.url, uniqueUrls)
      });
      
      // Create a map for quick lookups
      const linkMap = new Map(existingLinks.map(l => [l.url, l]));
      
      // 2. Create new links for URLs that don't exist yet
      const newLinkEntries = uniqueUrls
        .filter(url => !linkMap.has(url))
        .map(url => {
          const bookmarkData = batch.find(b => b.url === url);
          return {
            id: uuidv7(),
            url,
            title: bookmarkData?.title || '',
            description: '', // Default empty description
          };
        });
      
      // Batch insert new links
      if (newLinkEntries.length > 0) {
        const newLinks = await tx.insert(link)
          .values(newLinkEntries)
          .returning();
        
        // Add new links to the map
        for (const newLink of newLinks) {
          linkMap.set(newLink.url, newLink);
        }
        
        batchResult.linksCreated = newLinks.length;
      }
      
      // 3. Check for existing bookmarks to avoid duplicates
      const linkIds = Array.from(linkMap.values()).map(l => l.id);
      const existingBookmarks = await tx.query.bookmark.findMany({
        where: and(
          eq(bookmark.userId, userId),
          inArray(bookmark.linkId, linkIds)
        )
      });
      
      // Create a set of already bookmarked link IDs for this user
      const existingBookmarkLinkIds = new Set(existingBookmarks.map(b => b.linkId));
      
      // 4. Collect all unique tags across all bookmarks
      const allTags = new Set<string>();
      batch.forEach(b => {
        if (b.tags && b.tags.length > 0) {
          b.tags.forEach(tag => {
            const normalizedTag = normalizeTagName(tag);
            if (normalizedTag) {
              allTags.add(normalizedTag);
            }
          });
        }
      });
      
      const uniqueTags = Array.from(allTags);
      
      // 5. Find existing tags
      const existingTags = await tx.select({
        name: tag.name
      })
        .from(tag)
        .where(inArray(tag.name, uniqueTags));
      
      const existingTagNames = new Set(existingTags.map(t => t.name));
      
      // 6. Create new tags
      const newTagEntries = uniqueTags
        .filter(tagName => !existingTagNames.has(tagName))
        .map(tagName => ({
          name: tagName,
          usageCount: 1
        }));
      
      if (newTagEntries.length > 0) {
        await tx.insert(tag)
          .values(newTagEntries)
          .onConflictDoUpdate({
            target: tag.name,
            set: {
              usageCount: sql`${tag.usageCount} + 1`
            }
          });
        
        batchResult.tagsCreated = newTagEntries.length;
      }
      
      // 7. Update usage count for existing tags
      if (existingTagNames.size > 0) {
        await tx.update(tag)
          .set({ 
            usageCount: sql`${tag.usageCount} + 1`,
            updatedAt: new Date()
          })
          .where(inArray(tag.name, Array.from(existingTagNames)));
      }
      
      // 8. Prepare bookmark entries
      const bookmarkEntries = batch
        .filter(b => {
          // Skip if the user already has this URL bookmarked
          const linkRecord = linkMap.get(b.url);
          return linkRecord && !existingBookmarkLinkIds.has(linkRecord.id);
        })
        .map(b => {
          const linkRecord = linkMap.get(b.url)!;
          const normalizedTags = b.tags?.map(normalizeTagName).filter(Boolean) || [];
          
          return {
            id: uuidv7(),
            userId,
            linkId: linkRecord.id,
            notes: b.notes || null,
            isPrivate: b.isPrivate ?? false,
            isReadLater: b.isReadLater ?? false,
            tagNames: normalizedTags,
            createdAt: b.importedAt || new Date()
          };
        });
      
      // Count skipped bookmarks (already exist)
      batchResult.skipped = batch.length - bookmarkEntries.length;
      
      // If no new bookmarks to create, return early
      if (bookmarkEntries.length === 0) {
        return batchResult;
      }
      
      // 9. Batch insert bookmarks
      const newBookmarks = await tx.insert(bookmark)
        .values(bookmarkEntries)
        .returning();
      
      batchResult.created = newBookmarks.length;
      batchResult.bookmarkIds = newBookmarks.map(b => b.id);
      
      // 10. Update link metadata - bookmark counts and tags
      // For each link, update its bookmarkCount and combine all tags used
      const linkUpdatePromises = Array.from(linkMap.values()).map(async linkRecord => {
        // Get all tags for this link from the newly created bookmarks
        const linkBookmarks = newBookmarks.filter(b => b.linkId === linkRecord.id);
        if (linkBookmarks.length === 0) return;
        
        // Collect all tags used for this link
        const linkTags = new Set<string>();
        linkBookmarks.forEach(b => {
          if (b.tagNames && b.tagNames.length > 0) {
            b.tagNames.forEach(t => linkTags.add(t));
          }
        });
        
        // Update the link with new tags and increment bookmark count
        await tx.update(link)
          .set({
            bookmarkCount: sql`${link.bookmarkCount} + ${linkBookmarks.length}`,
            // Append new tags and ensure no duplicates
            tagNames: sql`array_distinct(array_cat(COALESCE(${link.tagNames}, ARRAY[]::text[]), ${Array.from(linkTags)}::text[]))`,
            updatedAt: new Date()
          })
          .where(eq(link.id, linkRecord.id));
      });
      
      await Promise.all(linkUpdatePromises);
      
      // 11. Generate embeddings (can be done in bulk or deferred to a background process)
      // For now, we'll generate basic embeddings inline
      const embeddingEntries = newBookmarks.map(newBookmark => {
        const bookmarkData = batch.find(b => {
          const bLinkRecord = linkMap.get(b.url);
          return bLinkRecord && bLinkRecord.id === newBookmark.linkId;
        })!;
        const linkRecord = linkMap.get(bookmarkData.url)!;
        
        // Create a structured embedding text with key-value pairs and additional context
        const embeddingText = [
          // Core bookmark data with field labels to create structure
          `Title: ${linkRecord.title || ''}`,
          `URL: ${linkRecord.url || ''}`,
          
          // Include notes when available
          bookmarkData.notes ? `Notes: ${bookmarkData.notes}` : '',
          
          // Tags with proper formatting
          bookmarkData.tags?.length ? `Tags: ${bookmarkData.tags.join(', ')}` : '',
          
          // Domain information for better context
          `Domain: ${new URL(linkRecord.url).hostname}`
        ]
          .filter(Boolean) // Remove any empty strings
          .join('\n'); // Join with newlines for better structure
        
        return {
          id: uuidv7(),
          bookmarkId: newBookmark.id,
          chunk: embeddingText,
          embedding: null, // We'll update this later with a proper embedding
          modelName: EMBEDDING_MODEL_NAME,
          modelVersion: '1.0'
        };
      });
      
      // Batch insert embeddings
      if (embeddingEntries.length > 0) {
        try {
          await tx.insert(bookmarkEmbedding).values(embeddingEntries);
        } catch (embeddingError) {
          console.error(`Failed to create embeddings for batch:`, embeddingError);
          // Continue without embeddings rather than failing the whole batch
        }
      }
      
      return batchResult;
    } catch (error) {
      console.error('Error processing bookmark batch:', error);
      batchResult.failed = batch.length;
      return batchResult;
    }
  });
  
  return transactionResult || batchResult; // Ensure we always return a result
}
