import { v7 as uuidv7 } from 'uuid'
import { db } from '$/db'
import { schema } from '$/db/schema'
import type { ImportedBookmark, JobStatus } from '$/services/import'
import { bulkFindOrCreateLinks } from '$/db/operations/links'
import { getOrCreateTagsBulk, normalizeTagName } from '$/db/operations/tags'
import { eq } from 'drizzle-orm'

// Define schema references
const bookmark = schema.bookmark
const bookmarkEmbedding = schema.bookmarkEmbedding

/**
 * Result interface for bookmark import operations
 */
export interface BookmarkImportResult {
  success: boolean
  imported: number
  skipped: number
  message: string
  errors?: string[]
}

/**
 * Process a chunk of bookmarks in bulk with optimized database operations
 * This function orchestrates the bulk import process across multiple entities
 *
 * @param importedBookmarks Array of imported bookmarks to process
 * @param userId ID of the user importing the bookmarks
 * @param skipExistingCheck Whether to skip checking for existing bookmarks
 * @param deferEmbeddings Whether to defer embedding generation
 * @returns Result of the import operation
 */
export async function processBulkImportChunk(
  importedBookmarks: ImportedBookmark[],
  userId: string,
  skipExistingCheck = false,
  deferEmbeddings = false
): Promise<BookmarkImportResult> {
  let importCount = 0
  let skippedCount = 0
  const errors: string[] = []

  // Early validation
  if (!importedBookmarks || importedBookmarks.length === 0) {
    return {
      success: true,
      imported: 0,
      skipped: 0,
      message: 'No bookmarks provided in this chunk'
    }
  }

  // Prepare data structures for batch processing
  const urlMap = new Map<string, { url: string, title: string | null, favicon?: string | null }>()
  const allTags = new Set<string>()

  // Extract unique URLs and tags
  for (const bookmark of importedBookmarks) {
    if (!bookmark.url) {
      errors.push(`Skipped bookmark: Missing URL`)
      skippedCount++
      continue
    }

    // Add URL to map (will deduplicate automatically)
    urlMap.set(bookmark.url, {
      url: bookmark.url,
      title: bookmark.title || null,
      favicon: bookmark.faviconUrl || null
    })

    // Add tags to set (will deduplicate automatically)
    if (bookmark.tags?.length) {
      for (const tag of bookmark.tags) {
        const normalizedTag = normalizeTagName(tag)
        if (normalizedTag) {
          allTags.add(normalizedTag)
        }
      }
    }
  }

  try {
    // Process everything in a transaction
    return await db.transaction(async (tx) => {
      try {
        // STEP 1: BULK PROCESS LINKS
        // Find or create all URLs in a single batch operation
        const urlsToProcess = Array.from(urlMap.values())
        const linkRecords = await bulkFindOrCreateLinks(urlsToProcess, tx)
        
        if (!linkRecords) {
          throw new Error('Failed to process links')
        }

        // Build URL lookup map for quick access
        const urlLookup = new Map<string, { id: string, title: string | null }>()
        for (const record of linkRecords) {
          if (record && record.url) {
            urlLookup.set(record.url, { id: record.id, title: record.title })
          }
        }

        // STEP 2: BULK PROCESS TAGS
        // Find or create all tags in a single batch operation
        const tagsToProcess = Array.from(allTags)
        const tagRecords = await getOrCreateTagsBulk(tagsToProcess, tx)

        // Build tag lookup map for quick access
        const tagLookup = new Map<string, string>() // tag name -> tag name (since name is the PK)
        tagRecords.forEach((tag) => {
          tagLookup.set(tag.name, tag.name)
        })

        // STEP 3: CHECK EXISTING BOOKMARKS (if required)
        let existingBookmarks = new Set<string>() // Set of linkIds
        if (!skipExistingCheck) {
          try {
            // Use direct select instead of tx.query which causes errors
            const results = await tx.select({ linkId: bookmark.linkId })
              .from(bookmark)
              .where(eq(bookmark.userId, userId))
            
            existingBookmarks = new Set(results.map(r => r.linkId))
          } catch (e) {
            console.error('Error checking existing bookmarks:', e)
            // Continue with empty set if query fails
          }
        }

        // STEP 4: PREPARE BATCH INSERTS
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const bookmarksToInsert: any[] = []
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const embeddingsToInsert: any[] = []

        // Process each bookmark
        for (const bookmark of importedBookmarks) {
          if (!bookmark.url) continue

          // Get URL record - Skip if URL wasn't successfully processed
          const urlRecord = urlLookup.get(bookmark.url)
          if (!urlRecord || !urlRecord.id) {
            skippedCount++
            continue
          }

          // Skip if already bookmarked by this user
          if (!skipExistingCheck && existingBookmarks.has(urlRecord.id)) {
            errors.push(`Skipped duplicate: ${bookmark.url}`)
            skippedCount++
            continue
          }

          // Generate ID once for this bookmark
          const bookmarkId = uuidv7()

          // Process tags for tagNames array
          const tagNamesForBookmark: string[] = []
          if (bookmark.tags?.length) {
            for (const tagName of bookmark.tags) {
              const normalizedTag = normalizeTagName(tagName)
              if (normalizedTag && tagLookup.has(normalizedTag)) {
                tagNamesForBookmark.push(normalizedTag)
              }
            }
          }

          // Prepare bookmark insertion with tagNames array
          bookmarksToInsert.push({
            id: bookmarkId,
            userId: userId,
            linkId: urlRecord.id,
            notes: bookmark.title || null, // Using title as notes for imported bookmarks
            tagNames: tagNamesForBookmark, // Use the array-based approach
            createdAt: bookmark.addDate ? new Date(bookmark.addDate * 1000) : new Date()
          })

          // Prepare embedding placeholder if not deferred
          if (!deferEmbeddings) {
            embeddingsToInsert.push({
              id: uuidv7(),
              bookmarkId: bookmarkId,
              modelName: 'rag_bge_small_en_v15',
              modelVersion: '1.0',
              chunk: bookmark.title || bookmark.url, // Use title or URL as the chunk
              createdAt: new Date()
            })
          }

          importCount++
        }

        // STEP 5: EXECUTE BATCH OPERATIONS
        // Using the bulk insert operations to efficiently write to database
        if (bookmarksToInsert.length > 0) {
          // Batch insert all bookmarks at once
          await tx.insert(bookmark).values(bookmarksToInsert).onConflictDoNothing() // Added onConflictDoNothing

          // Batch insert all embeddings at once (if not deferred)
          if (!deferEmbeddings && embeddingsToInsert.length > 0) {
            try {
              await tx.insert(bookmarkEmbedding).values(embeddingsToInsert).onConflictDoNothing() // Added onConflictDoNothing
              console.log(`Successfully inserted ${embeddingsToInsert.length} embedding placeholders`)
            }
            catch (batchEmbeddingError) {
              console.error(`Failed to insert embedding placeholders:`, batchEmbeddingError)
              // Continue without embeddings rather than failing the whole import
            }
          }
        }

        return {
          success: importCount > 0,
          imported: importCount,
          skipped: skippedCount,
          errors: errors.length > 0 ? errors : undefined,
          message: `Chunk processed. Imported: ${importCount}, Skipped: ${skippedCount}.`
        }
      }
      catch (txError: unknown) {
        // Log and return transaction errors
        console.error(`Transaction failed:`, txError)
        const errorMessage = txError instanceof Error ? txError.message : 'Unknown error'
        return {
          success: false,
          imported: importCount,
          skipped: skippedCount + (importedBookmarks.length - importCount - skippedCount),
          errors: [...errors, `Transaction error: ${errorMessage}`],
          message: `Chunk processing failed: ${errorMessage}`
        }
      }
    })
  }
  catch (error: unknown) {
    // Catch and handle any errors outside the transaction
    console.error(`Bulk import failed:`, error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return {
      success: false,
      imported: importCount,
      skipped: skippedCount + (importedBookmarks.length - importCount - skippedCount),
      errors: [...errors, `Import error: ${errorMessage}`],
      message: `Bulk import failed: ${errorMessage}`
    }
  }
}

/**
 * Import bookmarks for a user from parsed bookmark data
 * This function handles the orchestration of the import process
 *
 * @param parsedBookmarks Array of parsed bookmarks to import
 * @param userId ID of the user importing the bookmarks
 * @param chunkSize Size of chunks to process in parallel
 * @returns Detailed result of the import operation
 */
export async function importBookmarksBulk(
  parsedBookmarks: ImportedBookmark[],
  userId: string,
  chunkSize = 100
): Promise<BookmarkImportResult> {
  let importCount = 0
  let skippedCount = 0
  const errors: string[] = []

  console.log(`Starting import process for user ${userId} with ${parsedBookmarks.length} bookmarks`)

  // Split bookmarks into chunks for parallel processing
  const chunks: ImportedBookmark[][] = []
  for (let i = 0; i < parsedBookmarks.length; i += chunkSize) {
    chunks.push(parsedBookmarks.slice(i, i + chunkSize))
  }

  console.log(`Processing ${chunks.length} chunks of bookmarks`)

  // Process all chunks in parallel
  const results = await Promise.all(
    chunks.map(chunk =>
      processBulkImportChunk(
        chunk,
        userId,
        false, // Don't skip existing check
        false // Don't defer embeddings
      )
    )
  )

  // Aggregate results
  for (const result of results) {
    importCount += result.imported
    skippedCount += result.skipped
    if (result.errors?.length) {
      errors.push(...result.errors)
    }
  }

  // Return the final result
  return {
    success: importCount > 0,
    imported: importCount,
    skipped: skippedCount,
    errors: errors.length > 0 ? errors : undefined,
    message: `Import completed. Imported: ${importCount}, Skipped: ${skippedCount} out of ${parsedBookmarks.length} bookmarks.`
  }
}
