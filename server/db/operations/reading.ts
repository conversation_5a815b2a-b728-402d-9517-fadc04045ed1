// filepath: /home/<USER>/repos/favorites/server/db/operations/reading.ts
import { eq, and } from 'drizzle-orm'
import { v7 as uuidv7 } from 'uuid'
import { db, tables } from '$/db'
const { bookmark, bookmarkMeta } = tables

// TODO: The reading schema has been removed or modified in the new schema structure.
// The following code is commented out until a new implementation is created.

// Define reading types
interface Reading {
  id: string;
  bookmarkId: string;
  userId: string;
  readStatus: string;
  readPercentage: number;
  createdAt: Date;
  updatedAt: Date | null;
}

interface NewReading {
  id?: string;
  bookmarkId: string;
  userId: string;
  readStatus: string;
  readPercentage: number;
  createdAt?: Date;
  updatedAt?: Date | null;
}

/**
 * Get reading progress for a bookmark
 * @param bookmarkId ID of the bookmark
 * @param userId ID of the user
 * @returns Reading progress or null if not found
 */

export async function getReadingProgress(bookmarkId: string, userId: string): Promise<Reading | null> {
  try {
    // Query the bookmark_meta table for existing progress
    const record = await db.query.bookmarkMeta.findFirst({
      where: and(
        eq(bookmarkMeta.bookmarkId, bookmarkId),
        eq(bookmarkMeta.userId, userId)
      )
    })
    return record ?? null
  } catch (error) {
    console.error('Error getting reading progress:', error)
    throw new Error('Failed to get reading progress')
  }
}

/**
 * Update reading progress for a bookmark
 * @param bookmarkId ID of the bookmark
 * @param userId ID of the user
 * @param data Reading progress data to update
 * @returns Updated reading progress
 */

export async function updateReadingProgress(
  bookmarkId: string,
  userId: string,
  data: { readStatus: string; readPercentage: number }
): Promise<Reading | null> {
  try {
    // Ensure bookmark belongs to user
    const bookmarkExists = await db.query.bookmark.findFirst({
      where: and(
        eq(bookmark.id, bookmarkId),
        eq(bookmark.userId, userId)
      )
    })
    if (!bookmarkExists) {
      throw new Error('Bookmark not found or does not belong to the user')
    }
    // Check if metadata exists
    const existingMeta = await db.query.bookmarkMeta.findFirst({
      where: and(
        eq(bookmarkMeta.bookmarkId, bookmarkId),
        eq(bookmarkMeta.userId, userId)
      )
    })
    let result
    const now = new Date()
    if (existingMeta) {
      // Update existing record
      const [updated] = await db.update(bookmarkMeta)
        .set({
          readStatus: data.readStatus,
          readPercentage: data.readPercentage,
          lastReadAt: now
        })
        .where(eq(bookmarkMeta.id, existingMeta.id))
        .returning()
      result = updated
    } else {
      // Insert new metadata record
      const [inserted] = await db.insert(bookmarkMeta)
        .values({
          id: uuidv7(),
          bookmarkId,
          userId,
          readStatus: data.readStatus,
          readPercentage: data.readPercentage,
          lastReadAt: now,
          visitCount: 1,
          timeSpentSeconds: 0
        })
        .returning()
      result = inserted
    }
    return result ?? null
  } catch (error) {
    console.error('Error updating reading progress:', error)
    throw new Error('Failed to update reading progress')
  }
}

/**
 * Mark a bookmark as completed
 * @param bookmarkId ID of the bookmark
 * @param userId ID of the user
 * @returns Updated reading progress
 */

export async function markBookmarkAsCompleted(bookmarkId: string, userId: string): Promise<Reading | null> {
  return updateReadingProgress(bookmarkId, userId, {
    readStatus: 'completed',
    readPercentage: 100
  })
}

