import { eq } from 'drizzle-orm'
import { db } from '$/db'
import { user } from '$/db/schema'

export const findUserByEmail = async (email: string) => {
  try {
    const [existingUser] = await db
      .select()
      .from(user)
      .where(eq(user.email, email))
    return existingUser || null
  }
  catch (error) {
    console.error(error)
    return null
  }
}

export const createUserWithPassword = async (payload: { email: string, password: string }) => {
  // Extract username from email for initial display name
  const emailUsername = payload.email.split('@')[0]
  
  const extendedPayload = {
    email: payload.email,
    emailVerified: false,
    role: 'USER',
    name: emailUsername, // Use email username as initial name
    username: null, // Will be set during onboarding
    displayUsername: null, // Will be set during onboarding
    image: null,
    avatarUrl: null,
    hashedPassword: payload.password,
    banned: false,
    banReason: null,
    onboarded: false,
    bio: null
  }
  
  try {
    const [record] = await db
      .insert(user)
      .values(extendedPayload)
      .onConflictDoUpdate({
        target: user.email,
        set: {
          email: payload.email,
          hashedPassword: payload.password
        }
      })
      .returning()
    return record
  }
  catch (error) {
    console.error(error)
    throw new Error('Failed to upsert user')
  }
}
