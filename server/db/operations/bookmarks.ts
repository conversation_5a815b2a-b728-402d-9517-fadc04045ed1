import { eq, and, inArray, sql, lt, or, like, ilike, count } from 'drizzle-orm'
import { v7 as uuidv7 } from 'uuid'
import { db } from '$/db'
import type { JobStatus } from '$/services/import'
import { getImportJobStatus as getJobStatus, cancelImportJob as cancelJob } from '$/services/import'
import { schema } from '$/db/schema'
import { findOrCreateLink, decrementLinkUsage } from '$/db/operations/links'
import { removeTagsFromBookmark, addTagsToBookmark, normalizeTagName } from '$/db/operations/tags'
import { findOrCreateBookmarkMeta } from '$/db/operations/bookmark-meta'

// Define schema references
const bookmark = schema.bookmark
const bookmarkEmbedding = schema.bookmarkEmbedding
const link = schema.link
const tag = schema.tag

const EMBEDDING_MODEL_NAME = 'rag_bge_small_en_v15'

/**
 * Single bookmark creation data
 */
export interface BookmarkCreateData {
  url: string
  title: string
  notes?: string | null
  tags?: string[]
  userId: string
  isPrivate?: boolean
  isReadLater?: boolean
}

/**
 * Response from bookmark creation
 */
export interface BookmarkCreateResult {
  id: string
  url: string
  title: string
  notes: string | null
  tags?: string[]
  isPrivate: boolean
  isReadLater: boolean
  createdAt: Date
}

/**
 * Check if a bookmark already exists for a user and URL
 */
export async function checkExistingBookmark(userId: string, linkId: string): Promise<boolean> {
  // Use prepared statement for better performance
  const existingBookmark = await db.query.bookmark.findFirst({
    where: and(
      eq(bookmark.userId, userId),
      eq(bookmark.linkId, linkId)
    )
  })

  return !!existingBookmark
}

/**
 * Create a single bookmark with tags and embedding
 */
export async function createBookmark(data: BookmarkCreateData): Promise<BookmarkCreateResult> {
  return await db.transaction(async (tx) => {
    // Find or create link
    const linkRecord = await findOrCreateLink(data.url, data.title)

    // Check if bookmark already exists
    const existingBookmark = await tx.query.bookmark.findFirst({
      where: and(
        eq(bookmark.userId, data.userId),
        eq(bookmark.linkId, linkRecord.id)
      )
    })

    if (existingBookmark) {
      throw new Error('You\'ve already bookmarked this URL')
    }

    // Prepare tags array
    const tagNames = data.tags?.length ? data.tags : []

    // Create bookmark
    const [newBookmark] = await tx.insert(bookmark)
      .values({
        userId: data.userId,
        linkId: linkRecord.id,
        notes: data.notes || null,
        isPrivate: data.isPrivate ?? false,
        isReadLater: data.isReadLater ?? false,
        tagNames // Use the array directly
      })
      .returning()

    if (!newBookmark) {
      throw new Error('Failed to create bookmark')
    }

    // If bookmark is marked for reading later, create bookmark-meta entry
    if (newBookmark.isReadLater) {
      try {
        await findOrCreateBookmarkMeta(newBookmark.id, data.userId)
      }
      catch (metaError) {
        console.error(`Failed to create bookmark meta for bookmark ${newBookmark.id}:`, metaError)
        // Continue without meta rather than failing the whole bookmark creation
      }
    }

    // Generate and save embedding
    if (linkRecord) {
      // Create a structured embedding text with key-value pairs and additional context
      const embeddingText = [
        // Core bookmark data with field labels to create structure
        `Title: ${linkRecord.title || ''}`,
        `URL: ${linkRecord.url || ''}`,

        // Include notes when available
        data.notes ? `Notes: ${data.notes}` : '',

        // Tags with proper formatting
        tagNames.length ? `Tags: ${tagNames.join(', ')}` : '',

        // Domain information for better context
        `Domain: ${new URL(linkRecord.url).hostname}`
      ]
        .filter(Boolean) // Remove any empty strings
        .join('\n') // Join with newlines for better structure

      try {
        // Use a placeholder value for embedding during testing
        await tx.insert(bookmarkEmbedding).values({
          id: uuidv7(),
          bookmarkId: newBookmark.id,
          chunk: embeddingText,
          embedding: null, // We'll update this later with a proper embedding
          modelName: EMBEDDING_MODEL_NAME,
          modelVersion: '1.0'
        })
      }
      catch (embeddingError) {
        console.error(`Failed to create embedding for bookmark ${newBookmark.id}:`, embeddingError)
        // Continue without embedding rather than failing the whole bookmark creation
      }
    }

    // Process tags if they were provided
    // Instead of using addTagsToBookmark which creates a new transaction,
    // we'll update the bookmark's tagNames directly within this transaction
    if (data.tags && data.tags.length > 0) {
      // Normalize and filter out empty tags
      const normalizedNames = data.tags
        .map(normalizeTagName)
        .filter(Boolean)

      if (normalizedNames.length > 0) {
        // Update the bookmark with the normalized tag names
        await tx.update(bookmark)
          .set({
            // Set the tag names directly
            tagNames: normalizedNames
          })
          .where(eq(bookmark.id, newBookmark.id))

        // Update the link's tag names as well
        await tx.update(link)
          .set({
            // Ensure normalizedNames is properly cast as an array
            tagNames: sql`ARRAY(
              SELECT DISTINCT unnest
              FROM unnest(
                array_cat(
                  COALESCE(${link.tagNames}, ARRAY[]::text[]),
                  ARRAY[${sql.join(normalizedNames)}]::text[]
                )
              )
            )`
          })
          .where(eq(link.id, linkRecord.id))

        // Create or update tags
        for (const tagName of normalizedNames) {
          await tx.insert(tag)
            .values({
              name: tagName,
              usageCount: 1
            })
            .onConflictDoUpdate({
              target: tag.name,
              set: {
                usageCount: sql`${tag.usageCount} + 1`
              }
            })
        }
      }
    }

    // Return formatted result
    return {
      id: newBookmark.id,
      url: linkRecord.url,
      title: linkRecord.title || '',
      notes: newBookmark.notes,
      tags: data.tags,
      isPrivate: newBookmark.isPrivate,
      isReadLater: newBookmark.isReadLater,
      createdAt: newBookmark.createdAt || new Date()
    }
  })
}

/**
 * Get bookmark with resolved title
 * Uses userTitle if available, otherwise falls back to link's original title
 */
export const getBookmarkByBookmarkId = async (bookmarkId: string) => {
  // Use prepared statement with join for better performance
  const result = await db.query.bookmark.findFirst({
    where: eq(bookmark.id, bookmarkId),
    with: {
      link: true
    }
  })

  if (!result) return null

  // Use userTitle if available, otherwise fall back to link title
  const displayTitle = result.userTitle || result.link.title || ''

  return {
    ...result,
    displayTitle
  }
}

/**
 * Get all bookmarks for a user, including link and tags
 * Maps the response to include all necessary fields from both bookmark and link tables
 */
export const getBookmarksByUserId = async (userId: string, cursor?: string, limit: number = 20) => {
  const bookmarks = await db.query.bookmark.findMany({
    where: and(
      eq(bookmark.userId, userId),
      cursor ? lt(bookmark.createdAt, new Date(cursor)) : undefined
    ),
    with: {
      link: true
    },
    orderBy: (bookmark, { desc }) => [desc(bookmark.createdAt)],
    limit
  });

  return bookmarks.map(bookmark => ({
    id: bookmark.id,
    url: bookmark.link.url,
    title: bookmark.userTitle || bookmark.link.title || '',
    notes: bookmark.notes,
    tldr: bookmark.tldr,
    createdAt: bookmark.createdAt ? bookmark.createdAt.toISOString() : new Date().toISOString(),
    tags: bookmark.tagNames || [],
    isPrivate: bookmark.isPrivate,
    isReadLater: bookmark.isReadLater
  }));
};


// Import job status functions - these are now just wrappers for backwards compatibility
export function getImportJobStatus(jobId: string): JobStatus | null {
  return getJobStatus(jobId)
}

export function cancelImportJob(jobId: string): boolean {
  return cancelJob(jobId)
}

/**
 * Data structure for bulk bookmark import
 */
export interface BookmarkBulkImportData {
  url: string;
  title: string;
  notes?: string | null;
  tags?: string[];
  isPrivate?: boolean;
  isReadLater?: boolean;
  // Additional fields that might come from imports
  importedAt?: Date;
  originalId?: string;
}

/**
 * Result of bulk bookmark import operation
 */
export interface BookmarkBulkImportResult {
  // Summary counts
  total: number;
  created: number;
  skipped: number; // Already existed
  failed: number;  // Failed due to validation or other issues

  // Created bookmark IDs for reference
  bookmarkIds: string[];

  // Stats
  linksCreated: number;
  tagsCreated: number;
  duration: number; // Processing time in ms
}

/**
 * Create multiple bookmarks efficiently for a user
 * Optimized for bulk operations with batched database operations
 */
export async function createBookmarksByUserId(
  userId: string,
  bookmarksData: BookmarkBulkImportData[],
  batchSize = 100
): Promise<BookmarkBulkImportResult> {
  const startTime = Date.now();
  const result: BookmarkBulkImportResult = {
    total: bookmarksData.length,
    created: 0,
    skipped: 0,
    failed: 0,
    bookmarkIds: [],
    linksCreated: 0,
    tagsCreated: 0,
    duration: 0
  };

  // Validate input
  if (!userId || !bookmarksData.length) {
    result.duration = Date.now() - startTime;
    return result;
  }

  // Process in batches to avoid memory issues with very large imports
  for (let i = 0; i < bookmarksData.length; i += batchSize) {
    const batch = bookmarksData.slice(i, i + batchSize);
    const batchResult = await processBatch(userId, batch);

    // Accumulate results
    result.created += batchResult.created;
    result.skipped += batchResult.skipped;
    result.failed += batchResult.failed;
    result.bookmarkIds.push(...batchResult.bookmarkIds);
    result.linksCreated += batchResult.linksCreated;
    result.tagsCreated += batchResult.tagsCreated;
  }

  result.duration = Date.now() - startTime;
  return result;
}

/**
 * Process a batch of bookmarks in a single transaction
 * This is an internal helper function for createBookmarksByUserId
 */
async function processBatch(
  userId: string,
  batch: BookmarkBulkImportData[]
): Promise<BookmarkBulkImportResult> {
  const batchResult: BookmarkBulkImportResult = {
    total: batch.length,
    created: 0,
    skipped: 0,
    failed: 0,
    bookmarkIds: [],
    linksCreated: 0,
    tagsCreated: 0,
    duration: 0
  };

  // Skip empty batches
  if (!batch.length) return batchResult;

  const transactionResult = await db.transaction(async (tx) => {
    try {
      // 1. Extract all unique URLs and find existing links
      const uniqueUrls = [...new Set(batch.map(b => b.url))];
      const existingLinks = await tx.query.link.findMany({
        where: inArray(link.url, uniqueUrls)
      });

      // Create a map for quick lookups
      const linkMap = new Map(existingLinks.map(l => [l.url, l]));

      // 2. Create new links for URLs that don't exist yet
      const newLinkEntries = uniqueUrls
        .filter(url => !linkMap.has(url))
        .map(url => {
          const bookmarkData = batch.find(b => b.url === url);
          return {
            id: uuidv7(),
            url,
            title: bookmarkData?.title || '',
            description: '', // Default empty description
          };
        });

      // Batch insert new links
      if (newLinkEntries.length > 0) {
        const newLinks = await tx.insert(link)
          .values(newLinkEntries)
          .returning();

        // Add new links to the map
        for (const newLink of newLinks) {
          linkMap.set(newLink.url, newLink);
        }

        batchResult.linksCreated = newLinks.length;
      }

      // 3. Collect all unique tags across all bookmarks (no need to pre-check for duplicates)
      const allTags = new Set<string>();
      batch.forEach(b => {
        if (b.tags && b.tags.length > 0) {
          b.tags.forEach(tag => {
            const normalizedTag = normalizeTagName(tag);
            if (normalizedTag) {
              allTags.add(normalizedTag);
            }
          });
        }
      });

      const uniqueTags = Array.from(allTags);

      // 5. Find existing tags
      const existingTags = await tx.select({
        name: tag.name
      })
        .from(tag)
        .where(inArray(tag.name, uniqueTags));

      const existingTagNames = new Set(existingTags.map(t => t.name));

      // 6. Create new tags
      const newTagEntries = uniqueTags
        .filter(tagName => !existingTagNames.has(tagName))
        .map(tagName => ({
          name: tagName,
          usageCount: 1
        }));

      if (newTagEntries.length > 0) {
        await tx.insert(tag)
          .values(newTagEntries)
          .onConflictDoUpdate({
            target: tag.name,
            set: {
              usageCount: sql`${tag.usageCount} + 1`
            }
          });

        batchResult.tagsCreated = newTagEntries.length;
      }

      // 7. Update usage count for existing tags
      if (existingTagNames.size > 0) {
        await tx.update(tag)
          .set({
            usageCount: sql`${tag.usageCount} + 1`,
            updatedAt: new Date()
          })
          .where(inArray(tag.name, Array.from(existingTagNames)));
      }

      // 8. Prepare bookmark entries for all URLs (we'll handle duplicates with ON CONFLICT)
      const bookmarkEntries = batch.map(b => {
        const linkRecord = linkMap.get(b.url);
        if (!linkRecord) return null; // Skip if link wasn't found/created

        const normalizedTags = b.tags?.map(normalizeTagName).filter(Boolean) || [];

        return {
          id: uuidv7(),
          userId,
          linkId: linkRecord.id,
          notes: b.notes || null,
          isPrivate: b.isPrivate ?? false,
          isReadLater: b.isReadLater ?? false,
          tagNames: normalizedTags,
          createdAt: b.importedAt || new Date()
        };
      }).filter(Boolean) as any[]; // Filter out any null entries

      // If no bookmarks to create, return early
      if (bookmarkEntries.length === 0) {
        return batchResult;
      }

      // 9. Batch insert bookmarks with ON CONFLICT DO NOTHING for better performance
      const newBookmarks = await tx.insert(bookmark)
        .values(bookmarkEntries)
        .onConflictDoNothing({
          target: [bookmark.userId, bookmark.linkId] // Your unique constraint columns
        })
        .returning();

      batchResult.created = newBookmarks.length;
      batchResult.skipped = bookmarkEntries.length - newBookmarks.length;
      batchResult.bookmarkIds = newBookmarks.map(b => b.id);

      // 10. Update link metadata - bookmark counts and tags
      // For each link, update its bookmarkCount and combine all tags used
      const linkUpdatePromises = Array.from(linkMap.values()).map(async linkRecord => {
        // Get all tags for this link from the newly created bookmarks
        const linkBookmarks = newBookmarks.filter(b => b.linkId === linkRecord.id);
        if (linkBookmarks.length === 0) return;

        // Collect all tags used for this link
        const linkTags = new Set<string>();
        linkBookmarks.forEach(b => {
          if (b.tagNames && b.tagNames.length > 0) {
            b.tagNames.forEach(t => linkTags.add(t));
          }
        });

        // Update the link with new tags and increment bookmark count
        await tx.update(link)
          .set({
            bookmarkCount: sql`${link.bookmarkCount} + ${linkBookmarks.length}`,
            // Ensure linkTags is properly cast as an array
            tagNames: sql`ARRAY(
              SELECT DISTINCT unnest
              FROM unnest(
                array_cat(
                  COALESCE(${link.tagNames}, ARRAY[]::text[]),
                  ARRAY[${sql.join(Array.from(linkTags))}]::text[]
                )
              )
            )`,
            updatedAt: new Date()
          })
          .where(eq(link.id, linkRecord.id));
      });

      await Promise.all(linkUpdatePromises);

      // 11. Generate embeddings (can be done in bulk or deferred to a background process)
      // For now, we'll generate basic embeddings inline
      const embeddingEntries = newBookmarks.map(newBookmark => {
        const bookmarkData = batch.find(b => {
          const bLinkRecord = linkMap.get(b.url);
          return bLinkRecord && bLinkRecord.id === newBookmark.linkId;
        })!;
        const linkRecord = linkMap.get(bookmarkData.url)!;

        // Create a structured embedding text with key-value pairs and additional context
        const embeddingText = [
          // Core bookmark data with field labels to create structure
          `Title: ${linkRecord.title || ''}`,
          `URL: ${linkRecord.url || ''}`,

          // Include notes when available
          bookmarkData.notes ? `Notes: ${bookmarkData.notes}` : '',

          // Tags with proper formatting
          bookmarkData.tags?.length ? `Tags: ${bookmarkData.tags.join(', ')}` : '',

          // Domain information for better context
          `Domain: ${new URL(linkRecord.url).hostname}`
        ]
          .filter(Boolean) // Remove any empty strings
          .join('\n'); // Join with newlines for better structure

        return {
          id: uuidv7(),
          bookmarkId: newBookmark.id,
          chunk: embeddingText,
          embedding: null, // We'll update this later with a proper embedding
          modelName: EMBEDDING_MODEL_NAME,
          modelVersion: '1.0'
        };
      });

      // Batch insert embeddings
      if (embeddingEntries.length > 0) {
        try {
          await tx.insert(bookmarkEmbedding).values(embeddingEntries);
        } catch (embeddingError) {
          console.error(`Failed to create embeddings for batch:`, embeddingError);
          // Continue without embeddings rather than failing the whole batch
        }
      }

      return batchResult;
    } catch (error) {
      console.error('Error processing bookmark batch:', error);
      batchResult.failed = batch.length;
      return batchResult;
    }
  });

  return transactionResult || batchResult; // Ensure we always return a result
}

/**
 * Search interface for bookmarks
 */
export interface SearchBookmarksParams {
  userId: string
  searchTerm?: string
  tags?: string[]
  scope?: 'user' | 'global'
  scopeUser?: string
  cursor?: string
  limit?: number
}

export interface SearchBookmarksResult {
  bookmarks: any[]
  nextCursor: string | null
  total: number
}

/**
 * Advanced search for bookmarks with support for text, tags, and scope filtering
 */
export async function searchBookmarks(params: SearchBookmarksParams): Promise<SearchBookmarksResult> {
  const {
    userId,
    searchTerm = '',
    tags = [],
    scope = 'user',
    scopeUser,
    cursor,
    limit = 20
  } = params

  // Build bookmark-only conditions first
  const bookmarkConditions = []

  // Scope filtering
  if (scope === 'user') {
    bookmarkConditions.push(eq(bookmark.userId, userId))
  } else if (scope === 'global' && scopeUser) {
    // Search specific user's public bookmarks
    bookmarkConditions.push(eq(bookmark.userId, scopeUser))
    bookmarkConditions.push(eq(bookmark.isPrivate, false))
  } else if (scope === 'global') {
    // Search all public bookmarks
    bookmarkConditions.push(eq(bookmark.isPrivate, false))
  }

  // Cursor-based pagination
  if (cursor) {
    bookmarkConditions.push(lt(bookmark.createdAt, new Date(cursor)))
  }

  // Search term filtering in bookmark fields only
  if (searchTerm.trim()) {
    const searchPattern = `%${searchTerm.toLowerCase()}%`
    bookmarkConditions.push(
      or(
        ilike(bookmark.notes, searchPattern),
        ilike(bookmark.userTitle, searchPattern),
        ilike(bookmark.tldr, searchPattern)
      )
    )
  }

  // Tag filtering - bookmark must have ALL specified tags
  if (tags.length > 0) {
    const normalizedTags = tags.map(tag => tag.toLowerCase())
    bookmarkConditions.push(
      sql`${bookmark.tagNames} @> ARRAY[${sql.join(normalizedTags)}]::text[]`
    )
  }

  try {
    // Execute the search query
    const searchResults = await db.query.bookmark.findMany({
      where: and(...bookmarkConditions),
      with: {
        link: true
      },
      orderBy: (bookmark, { desc }) => [desc(bookmark.createdAt)],
      limit: limit + 1 // Fetch one extra to determine if there are more results
    })

    // If we have a search term, filter results to also include link title/URL matches
    let filteredResults = searchResults
    if (searchTerm.trim()) {
      const searchPattern = searchTerm.toLowerCase()
      filteredResults = searchResults.filter(result => {
        // Check if already matched bookmark fields, or if it matches link fields
        const matchesBookmark = (
          (result.notes && result.notes.toLowerCase().includes(searchPattern)) ||
          (result.userTitle && result.userTitle.toLowerCase().includes(searchPattern)) ||
          (result.tldr && result.tldr.toLowerCase().includes(searchPattern))
        )
        const matchesLink = (
          (result.link.title && result.link.title.toLowerCase().includes(searchPattern)) ||
          (result.link.url && result.link.url.toLowerCase().includes(searchPattern))
        )
        return matchesBookmark || matchesLink
      })
    }

    // Determine if there are more results based on filtered results
    const hasMore = filteredResults.length > limit
    const bookmarks = hasMore ? filteredResults.slice(0, limit) : filteredResults

    // Get total count for the search (optional, can be expensive)
    let total = bookmarks.length
    if (bookmarks.length > 0) {
      // For now, we'll just return the current batch count
      // In a production app, you might want to run a separate count query
      total = bookmarks.length
    }

    // Format results
    const formattedBookmarks = bookmarks.map(bookmark => ({
      id: bookmark.id,
      url: bookmark.link.url,
      title: bookmark.userTitle || bookmark.link.title || '',
      notes: bookmark.notes,
      tldr: bookmark.tldr,
      createdAt: bookmark.createdAt ? bookmark.createdAt.toISOString() : new Date().toISOString(),
      tags: bookmark.tagNames || [],
      isPrivate: bookmark.isPrivate,
      isReadLater: bookmark.isReadLater
    }))

    return {
      bookmarks: formattedBookmarks,
      nextCursor: hasMore ? bookmarks[bookmarks.length - 1].createdAt?.toISOString() || null : null,
      total
    }
  } catch (error) {
    console.error('Search error:', error)
    throw error
  }
}

/**
 * Update an existing bookmark's properties
 */
export interface BookmarkUpdateData {
  title: string
  notes?: string | null
  tags?: string[]
  isPrivate: boolean
  isReadLater: boolean
}

export async function updateBookmark(
  bookmarkId: string,
  userId: string,
  data: BookmarkUpdateData
): Promise<BookmarkCreateResult> {
  return await db.transaction(async (tx) => {
    // Ensure bookmark belongs to user
    const existingBookmark = await tx.query.bookmark.findFirst({
      where: and(
        eq(bookmark.userId, userId),
        eq(bookmark.id, bookmarkId)
      )
    })
    if (!existingBookmark) {
      throw new Error('Bookmark not found or does not belong to the user')
    }

    // Update bookmark fields
    const [updated] = await tx.update(bookmark)
      .set({
        userTitle: data.title,
        notes: data.notes ?? null,
        isPrivate: data.isPrivate,
        isReadLater: data.isReadLater
      })
      .where(eq(bookmark.id, bookmarkId))
      .returning()
    if (!updated) {
      throw new Error('Failed to update bookmark')
    }

    // Update tags: clear existing and set new if provided
    if (data.tags) {
      // Remove all current tags
      const currentTagNames = existingBookmark.tagNames || []
      if (currentTagNames.length) {
        await removeTagsFromBookmark(bookmarkId, currentTagNames)
      }
      // Add new tags array
      if (data.tags.length) {
        await addTagsToBookmark(bookmarkId, data.tags)
      }
    }

    // Get the link URL for the response
    const linkRecord = await tx.query.link.findFirst({
      where: eq(link.id, existingBookmark.linkId),
      columns: {
        url: true
      }
    })

    if (!linkRecord) {
      throw new Error('Link record not found')
    }

    // Construct response object
    return {
      id: updated.id,
      url: linkRecord.url,
      title: updated.userTitle || '',
      notes: updated.notes,
      tags: data.tags,
      isPrivate: updated.isPrivate,
      isReadLater: updated.isReadLater,
      createdAt: updated.createdAt || new Date()
    }
  })
}

/**
 * Delete a bookmark and cleanup related resources
 * @param bookmarkId ID of the bookmark to delete
 * @param userId ID of the user who owns the bookmark
 * @returns Boolean indicating success
 */
export async function deleteBookmark(bookmarkId: string, userId: string): Promise<boolean> {
  return await db.transaction(async (tx) => {
    // Find the bookmark to be deleted
    const bookmarkToDelete = await tx.query.bookmark.findFirst({
      where: and(
        eq(bookmark.id, bookmarkId),
        eq(bookmark.userId, userId)
      )
    })

    if (!bookmarkToDelete) {
      throw new Error('Bookmark not found or does not belong to the user')
    }

    const { linkId, tagNames } = bookmarkToDelete

    // Delete related embedding first (if it exists)
    await tx.delete(bookmarkEmbedding)
      .where(eq(bookmarkEmbedding.bookmarkId, bookmarkId))

    // Delete the bookmark (this will cascade to bookmark_meta due to foreign key constraint)
    const deletedCount = await tx.delete(bookmark)
      .where(eq(bookmark.id, bookmarkId))
      .returning()
      .then(rows => rows.length)

    if (deletedCount !== 1) {
      throw new Error('Failed to delete bookmark')
    }

    // Remove bookmark's tags
    if (tagNames && tagNames.length > 0) {
      await removeTagsFromBookmark(bookmarkId, tagNames)
    }

    // Decrement the link usage counter and potentially cleanup orphaned links
    if (linkId) {
      await decrementLinkUsage(linkId)
    }

    return true
  })
}
