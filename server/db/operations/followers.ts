import { eq, and, inArray } from 'drizzle-orm'
import { db } from '$/db'
import { schema } from '$/db/schema'
import { sanitizedUserSchema } from '$/db/schema/user'
import type { User } from '$/db/schema/user'

// Define schema references
const follower = schema.follower
const user = schema.user

/**
 * Follow a user
 * @param followerId ID of the user who is following
 * @param followingId ID of the user being followed
 * @returns The created follower relationship
 * @throws Error if users don't exist, user tries to follow themselves, or relationship already exists
 */
export async function followUser(followerId: string, followingId: string) {
  try {
    // Early return if user is trying to follow themselves
    if (followerId === followingId) {
      throw new Error('Users cannot follow themselves')
    }

    // Check if users exist
    const [followerUser, followingUser] = await Promise.all([
      db.query.user.findFirst({ where: eq(user.id, followerId) }),
      db.query.user.findFirst({ where: eq(user.id, followingId) })
    ])

    if (!followerUser) {
      throw new Error('Follower user not found')
    }

    if (!followingUser) {
      throw new Error('Following user not found')
    }

    // Check if relationship already exists
    const existingRelationship = await db.query.follower.findFirst({
      where: and(
        eq(follower.followerId, followerId),
        eq(follower.followeeId, followingId)
      )
    })

    if (existingRelationship) {
      throw new Error('Already following this user')
    }

    // Create the follower relationship
    const [relationship] = await db.insert(follower)
      .values({
        followerId,
        followeeId: followingId,
        createdAt: new Date()
      })
      .returning()

    return relationship
  } catch (error) {
    // Re-throw the error with the original message
    if (error instanceof Error) {
      throw error
    }
    // Handle unexpected errors
    throw new Error(`Failed to follow user: ${error}`)
  }
}

/**
 * Unfollow a user
 * @param followerId ID of the user who is following
 * @param followingId ID of the user being followed
 * @returns True if the relationship was deleted, false otherwise
 * @throws Error if the operation fails
 */
export async function unfollowUser(followerId: string, followingId: string) {
  try {
    // Delete the follower relationship
    const result = await db.delete(follower)
      .where(
        and(
          eq(follower.followerId, followerId),
          eq(follower.followeeId, followingId)
        )
      )
      .returning()

    return result.length > 0
  } catch (error) {
    console.error('Error unfollowing user:', error)
    if (error instanceof Error) {
      throw error
    }
    throw new Error(`Failed to unfollow user: ${error}`)
  }
}

/**
 * Check if a user follows another user
 * @param followerId ID of the user who might be following
 * @param followingId ID of the user who might be followed
 * @returns True if the relationship exists, false otherwise
 * @throws Error if the operation fails
 */
export async function checkFollowStatus(followerId: string, followingId: string) {
  try {
    const relationship = await db.query.follower.findFirst({
      where: and(
        eq(follower.followerId, followerId),
        eq(follower.followeeId, followingId)
      )
    })

    return !!relationship
  } catch (error) {
    console.error('Error checking follow status:', error)
    if (error instanceof Error) {
      throw error
    }
    throw new Error(`Failed to check follow status: ${error}`)
  }
}

/**
 * Get users that a user is following
 * @param userId ID of the user
 * @returns Array of users that the user is following
 * @throws Error if the operation fails
 */
export async function getFollowing(userId: string) {
  try {
    const followingRelationships = await db.query.follower.findMany({
      where: eq(follower.followerId, userId),
      with: {
        followee: true
      }
    })

    // Map and sanitize user data
    return followingRelationships.map(relationship =>
      sanitizedUserSchema.parse(relationship.followee)
    )
  } catch (error) {
    console.error('Error getting following list:', error)
    if (error instanceof Error) {
      throw error
    }
    throw new Error(`Failed to get following list: ${error}`)
  }
}

/**
 * Get users following a user
 * @param userId ID of the user
 * @returns Array of users following the user
 * @throws Error if the operation fails
 */
export async function getFollowers(userId: string) {
  try {
    const followerRelationships = await db.query.follower.findMany({
      where: eq(follower.followeeId, userId),
      with: {
        follower: true
      }
    })

    // Map and sanitize user data
    return followerRelationships.map(relationship =>
      sanitizedUserSchema.parse(relationship.follower)
    )
  } catch (error) {
    console.error('Error getting followers list:', error)
    if (error instanceof Error) {
      throw error
    }
    throw new Error(`Failed to get followers list: ${error}`)
  }
}

/**
 * Get follow counts for a user
 * @param userId ID of the user
 * @returns Object containing follower and following counts
 * @throws Error if the operation fails
 */
export async function getFollowCounts(userId: string) {
  try {
    const [followerCount, followingCount] = await Promise.all([
      db.query.follower.findMany({
        where: eq(follower.followeeId, userId),
        columns: {
          followerId: true
        }
      }).then(results => results.length),
      db.query.follower.findMany({
        where: eq(follower.followerId, userId),
        columns: {
          followeeId: true
        }
      }).then(results => results.length)
    ])

    return {
      followerCount,
      followingCount
    }
  } catch (error) {
    console.error('Error getting follow counts:', error)
    if (error instanceof Error) {
      throw error
    }
    throw new Error(`Failed to get follow counts: ${error}`)
  }
}

/**
 * Get recent bookmarks from users that a user is following (feed)
 * @param userId ID of the user requesting the feed
 * @param limit Maximum number of bookmarks to return (default: 20)
 * @returns Array of recent bookmarks from followed users
 * @throws Error if the operation fails
 */
export async function getFollowingFeed(userId: string, limit: number = 20) {
  try {
    // First get the list of users the current user is following
    const followingRelationships = await db.query.follower.findMany({
      where: eq(follower.followerId, userId),
      columns: {
        followeeId: true
      }
    })

    if (followingRelationships.length === 0) {
      return []
    }

    const followingUserIds = followingRelationships.map(rel => rel.followeeId)

    // Get recent bookmarks from followed users
    const feedBookmarks = await db.query.bookmark.findMany({
      where: inArray(schema.bookmark.userId, followingUserIds),
      with: {
        user: true,
        link: true
      },
      orderBy: (bookmark, { desc }) => [desc(bookmark.createdAt)],
      limit
    })

    return feedBookmarks.map(bookmark => ({
      ...bookmark,
      user: sanitizedUserSchema.parse(bookmark.user)
    }))
  } catch (error) {
    console.error('Error getting following feed:', error)
    if (error instanceof Error) {
      throw error
    }
    throw new Error(`Failed to get following feed: ${error}`)
  }
}
