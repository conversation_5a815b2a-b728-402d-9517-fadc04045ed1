import { pgTable, text, boolean, integer, index, unique } from 'drizzle-orm/pg-core'
import { relations } from 'drizzle-orm'
import { v7 as uuidv7 } from 'uuid'
import { z } from 'zod'
import { timestamps } from './partial'
import { user } from './user'
import { link } from './link'

export const bookmark = pgTable(
  'bookmark',
  {
    id: text('id').primaryKey().$default(() => uuidv7()),
    userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
    linkId: text('link_id').notNull().references(() => link.id, { onDelete: 'cascade' }),
    userTitle: text('user_title'), // User's custom title, can be null if using original
    notes: text('notes'),
    tldr: text('tldr'),
    isPrivate: boolean('is_private').notNull().default(false),
    isReadLater: boolean('is_read_later').notNull().default(false),
    // Array of tag names (references tags.name)
    tagNames: text('tag_names').array().$type<string[]>().default([]),
    // Track number of visits to this bookmark
    visitCount: integer('visit_count').notNull().default(0),
    ...timestamps
  },
  (bookmark) => ({
    // Add GIN index for fast tag array lookups
    tagsIndex: index('bookmark_tags_idx').on(bookmark.tagNames),
    // Ensure one bookmark per URL per user
    uniqueUserUrl: unique('unique_user_url_idx').on(bookmark.userId, bookmark.linkId)
  })
)

export const bookmarkSelectSchema = z.object({
  id: z.string().min(1),
  userId: z.string().min(1),
  linkId: z.string().min(1),
  userTitle: z.string().nullable().optional(), // Optional user title
  notes: z.string().nullable().optional(),
  tldr: z.string().nullable().optional(),
  isPrivate: z.boolean(),
  isReadLater: z.boolean(),
  tagNames: z.array(z.string()).default([]),
  visitCount: z.number().min(0),
  createdAt: z.date(),
  updatedAt: z.date().optional()
})

export const bookmarkInsertSchema = z.object({
  id: z.string().min(1).optional(),
  userId: z.string().min(1),
  linkId: z.string().min(1),
  userTitle: z.string().nullable().optional(),
  notes: z.string().nullable().optional(),
  tldr: z.string().nullable().optional(),
  isPrivate: z.boolean().optional(),
  isReadLater: z.boolean().optional(),
  tagNames: z.array(z.string()).optional(),
  visitCount: z.number().min(0).optional()
})

export type Bookmark = z.infer<typeof bookmarkSelectSchema>
export type NewBookmark = z.infer<typeof bookmarkInsertSchema>

export const bookmarkRelations = relations(bookmark, ({ one }) => ({
  user: one(user, {
    fields: [bookmark.userId],
    references: [user.id]
  }),
  link: one(link, {
    fields: [bookmark.linkId],
    references: [link.id]
  })
}))