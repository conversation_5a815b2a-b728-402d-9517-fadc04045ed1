import { pgTable, text, integer, index } from 'drizzle-orm/pg-core'
import { relations } from 'drizzle-orm'
import { z } from 'zod'
import { timestamps } from './partial'

/**
 * Tags table with name as primary key
 * This design allows for direct tag name references in arrays
 * and simplifies tag lookups without requiring joins
 *
 * Tag naming constraints:
 * - Lowercase alphanumeric characters and underscores only
 * - No spaces, hyphens, or special characters
 * - Stored as normalized slugs (e.g., "web_development")
 * - Tags are verified to be URL-friendly before insertion
 */
export const tag = pgTable('tag', {
  // Use the tag name as the primary key (normalized, lowercase, alphanumeric + underscore only)
  name: text('name').primaryKey(),
  usageCount: integer('usage_count').notNull().default(0),
  ...timestamps
}, (table) => {
  return {
    usageCountIndex: index('tag_usage_count_idx').on(table.usageCount)
  }
})

export const tagSelectSchema = z.object({
  name: z.string().min(1).regex(/^[a-z0-9_]+$/),
  usageCount: z.number().min(0),
  createdAt: z.date(),
  updatedAt: z.date().optional()
})

export const tagInsertSchema = z.object({
  name: z.string().min(1).regex(/^[a-z0-9_]+$/),
  usageCount: z.number().min(0).optional()
})

export type Tag = z.infer<typeof tagSelectSchema>
export type NewTag = z.infer<typeof tagInsertSchema>

export const tagRelations = relations(tag, () => ({}))
