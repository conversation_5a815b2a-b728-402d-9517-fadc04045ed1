import { pgTable, text, boolean, integer, index } from 'drizzle-orm/pg-core'
import { relations } from 'drizzle-orm'
import { v7 as uuidv7 } from 'uuid'
import { z } from 'zod'
import { timestamps } from './partial'
import { bookmark } from './bookmark'

export const link = pgTable('link', {
  id: text('id').primaryKey().$default(() => uuidv7()),
  url: text('url').notNull().unique(),
  title: text('title'),
  description: text('description'), // todo: remove this field if not needed
  favicon: text('favicon'), // Store base64 encoded favicon data
  isNsfw: boolean('is_nsfw').notNull().default(false),
  clickCount: integer('click_count').notNull().default(0),
  bookmarkCount: integer('bookmark_count').notNull().default(0),
  // Array of unique tag names used across all bookmarks for this URL
  tagNames: text('tag_names').array().$type<string[]>().default([]),
  ...timestamps
}, (table) => {
  return {
    // Add GIN index for fast tag array lookups
    tagsIndex: index('link_tag_idx').on(table.tagNames)
  }
})

export const linkSelectSchema = z.object({
  id: z.string().min(1),
  url: z.string().url(),
  title: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  favicon: z.string().nullable().optional(),
  isNsfw: z.boolean(),
  clickCount: z.number().min(0),
  bookmarkCount: z.number().min(0),
  tagNames: z.array(z.string()).default([]),
  createdAt: z.date(),
  updatedAt: z.date().optional()
})

export const linkInsertSchema = z.object({
  id: z.string().min(1).optional(),
  url: z.string().url(),
  title: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  favicon: z.string().nullable().optional(),
  isNsfw: z.boolean().default(false).optional(),
  clickCount: z.number().min(0).optional(),
  bookmarkCount: z.number().min(0).optional(),
  tagNames: z.array(z.string()).optional()
})

export type Link = z.infer<typeof linkSelectSchema>
export type NewLink = z.infer<typeof linkInsertSchema>

export const linkRelations = relations(link, ({ many }) => ({
  bookmarks: many(bookmark)
}))
