import {
  pgTable,
  text,
  timestamp
} from "drizzle-orm/pg-core";
import { z } from "zod";
import { v7 as uuidv7 } from 'uuid'
import { user } from "./user";
import { timestamps } from "./partial";
import { relations } from "drizzle-orm";

export const session = pgTable("session", {
  id: text('id').primaryKey().$default(() => uuidv7()),
  expiresAt: timestamp("expires_at").notNull(),
  token: text("token").notNull().unique(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  userId: text("user_id") .notNull().references(() => user.id, { onDelete: "cascade" }),
  impersonatedBy: text("impersonated_by"),
  ...timestamps
});

// relations
export const sessionRelations = relations(session, ({ one }) => ({
  user: one(user, {
    fields: [session.userId],
    references: [user.id]
  })
}))

export const sessionSelectSchema = z.object({
  id: z.string().min(1),
  expiresAt: z.date(),
  token: z.string().min(1),
  ipAddress: z.string().nullable().optional(),
  userAgent: z.string().nullable().optional(),
  userId: z.string().min(1),
  impersonatedBy: z.string().nullable().optional(),
  createdAt: z.date(),
  updatedAt: z.date().optional()
})

export const sessionInsertSchema = z.object({
  id: z.string().min(1).optional(),
  expiresAt: z.date(),
  token: z.string().min(1),
  ipAddress: z.string().nullable().optional(),
  userAgent: z.string().nullable().optional(),
  userId: z.string().min(1),
  impersonatedBy: z.string().nullable().optional()
})


