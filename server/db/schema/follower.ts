import { pgTable, text, timestamp, primaryKey } from 'drizzle-orm/pg-core'
import { relations } from 'drizzle-orm'
import { user } from './user'

export const follower = pgTable('follower', {
  followerId: text('follower_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  followeeId: text('followee_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow()
}, t => ({
  pk: primaryKey(t.followerId, t.followeeId)
}))

export const followerRelations = relations(follower, ({ one }) => ({
  follower: one(user, {
    fields: [follower.followerId],
    references: [user.id],
    relationName: 'follower'
  }),
  followee: one(user, {
    fields: [follower.followeeId],
    references: [user.id],
    relationName: 'followee'
  })
}))
