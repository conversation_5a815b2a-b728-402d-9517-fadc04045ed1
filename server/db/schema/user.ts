import { pgTable, text, boolean, timestamp } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { v7 as uuidv7 } from "uuid";
import { createSelectSchema, createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { timestamps } from "./partial";
import { Role } from "./enum";
import { bookmark } from "./bookmark";
import { follower } from "./follower";

export const user = pgTable("user", {
  id: text("id").primaryKey().$default(() => uuidv7()),
  name: text("name").notNull(),
  bio: text("bio"),
  email: text("email").notNull().unique(),
  emailVerified: boolean("email_verified").notNull().default(false),
  username: text("username").unique(),
  displayUsername: text("display_username"),
  image: text("image"),
  role: text("role").notNull(),
  banned: boolean("banned"),
  banReason: text("ban_reason"),
  banExpires: timestamp("ban_expires"),
  avatarUrl: text("avatar_url"),
  onboarded: boolean("onboarded").notNull().default(false),
  lastActive: timestamp("last_active", { withTimezone: true }).$onUpdate(
    () => new Date()
  ),
  ...timestamps,
});

export const userSelectSchema = createSelectSchema(user, {
  id: z.string().min(1),
  name: z.string().min(1).max(100),
  bio: z.string().max(500).nullable().optional(),
  email: z.string().email(),
  emailVerified: z.boolean(),
  role: z.string(), // Role enum
  username: z.string().min(3).max(30).nullable().optional(),
  displayUsername: z.string().min(3).max(30).nullable().optional(),
  image: z.string().nullable().optional(),
  onboarded: z.boolean(),
  lastActive: z.date().nullable().optional(),
  createdAt: z.date(),
  updatedAt: z.date().optional(),
});

export const userInsertSchema = createInsertSchema(user, {
  id: z.string().min(1).optional(),
  name: z.string().min(1).max(100),
  bio: z.string().max(500).nullable().optional(),
  email: z.string().email(),
  emailVerified: z.boolean().optional(),
  role: z.string().optional(),
  username: z.string().min(3).max(30).nullable().optional(),
  displayUsername: z.string().min(3).max(30).nullable().optional(),
  image: z.string().nullable().optional(),
  onboarded: z.boolean().optional(),
  lastActive: z.date().nullable().optional(),
});

export type User = typeof userSelectSchema._type;
export type NewUser = typeof userInsertSchema._type;

export const userRelations = relations(user, ({ many }) => ({
  bookmark: many(bookmark),
  follower: many(follower, {
    relationName: "follower",
  }),
  followee: many(follower, {
    relationName: "followee",
  }),
}));

// create validation for user register loginWithPasswordSchema
export const loginWithPasswordSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});
export const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1).max(100),
});

// Create a sanitized schema by omitting sensitive fields
export const sanitizedUserSchema = userSelectSchema.omit({
  banned: true,
  banReason: true,
  banExpires: true
});
