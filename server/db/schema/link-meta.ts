import { pgTable, text, jsonb } from 'drizzle-orm/pg-core'
import { relations } from 'drizzle-orm'
import { v7 as uuidv7 } from 'uuid'
import { z } from 'zod'
import { timestamps } from './partial'
import { user } from './user'
import { link } from './link'

/**
 * Table for tracking user interactions with URLs such as clicks and impressions
 * Used for analytics and calculating popularity/engagement metrics
 */
export const linkMeta = pgTable('link_meta', {
  id: text('id').primaryKey().$default(() => uuidv7()),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  linkId: text('link_id').notNull().references(() => link.id, { onDelete: 'cascade' }),
  interactionType: text('interaction_type').notNull(), // 'click', 'impression'
  source: text('source'), // where in the UI ('homepage', 'bookmark_list', etc.)
  deviceInfo: jsonb('device_info'), // device type, browser, etc.
  ...timestamps
})

export const linkMetaSelectSchema = z.object({
  id: z.string().min(1),
  userId: z.string().min(1),
  linkId: z.string().min(1),
  interactionType: z.enum(['click', 'impression']),
  source: z.string().nullable().optional(),
  deviceInfo: z.record(z.unknown()).nullable().optional(),
  createdAt: z.date(),
  updatedAt: z.date().optional()
})

export const linkMetaInsertSchema = z.object({
  id: z.string().min(1).optional(),
  userId: z.string().min(1),
  linkId: z.string().min(1),
  interactionType: z.enum(['click', 'impression']),
  source: z.string().optional(),
  deviceInfo: z.record(z.unknown()).optional()
})

export type LinkMeta = z.infer<typeof linkMetaSelectSchema>
export type NewLinkMeta = z.infer<typeof linkMetaInsertSchema>

export const linkMetaRelations = relations(linkMeta, ({ one }) => ({
  user: one(user, {
    fields: [linkMeta.userId],
    references: [user.id]
  }),
  link: one(link, {
    fields: [linkMeta.linkId],
    references: [link.id]
  })
}))
