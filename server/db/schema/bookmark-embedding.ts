import { index, pgTable, text, vector } from 'drizzle-orm/pg-core'
import { relations } from 'drizzle-orm'
import { createSelectSchema, createInsertSchema } from 'drizzle-zod'
import { z } from 'zod'
import { timestamps } from './partial'
import { bookmark } from './bookmark'

export const bookmarkEmbedding = pgTable('bookmark_embedding', {
  id: text('id').primaryKey(),
  bookmarkId: text('bookmark_id').notNull(),
  modelName: text('model_name').notNull(),
  modelVersion: text('model_version').notNull(),
  chunk: text('chunk').notNull(), // The chunk of text that was embedded
  embedding: vector('embedding', { dimensions: 384 }), // The embedding vector (size depends on the model)
  ...timestamps
},
table => [
  index('embeddingIndex').using('hnsw', table.embedding.op('vector_cosine_ops'))
])

// Define the relation to bookmarks (each embedding belongs to one bookmark)
export const bookmarkEmbeddingRelations = relations(bookmarkEmbedding, ({ one }) => ({
  bookmark: one(bookmark, {
    fields: [bookmarkEmbedding.bookmarkId],
    references: [bookmark.id]
  })
}))

export const bookmarkEmbeddingSelectSchema = createSelectSchema(bookmarkEmbedding, {
  id: z.string().min(1),
  bookmarkId: z.string().min(1),
  modelName: z.string(),
  modelVersion: z.string(),
  createdAt: z.date(),
  updatedAt: z.date().optional()
})

export const bookmarkEmbeddingInsertSchema = createInsertSchema(bookmarkEmbedding, {
  id: z.string().min(1).optional(),
  bookmarkId: z.string().min(1),
  modelName: z.string(),
  modelVersion: z.string()
})

export type BookmarkEmbedding = typeof bookmarkEmbeddingSelectSchema._type
export type NewBookmarkEmbedding = typeof bookmarkEmbeddingInsertSchema._type
