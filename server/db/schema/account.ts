
import {
  pgTable,
  text,
  timestamp
} from "drizzle-orm/pg-core";
import { user } from "./user";
import { v7 as uuidv7 } from 'uuid'
import { z } from "zod";
import { timestamps } from "./partial";
import { relations } from "drizzle-orm";

export const account = pgTable("account", {
  id: text('id').primaryKey().$default(() => uuidv7()),
  accountId: text("account_id").notNull(),
  providerId: text("provider_id").notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  idToken: text("id_token"),
  accessTokenExpiresAt: timestamp("access_token_expires_at"),
  refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
  scope: text("scope"),
  password: text("password"),
  ...timestamps
});

// relations
export const accountRelations = relations(account, ({ one }) => ({
  user: one(user, {
    fields: [account.userId],
    references: [user.id]
  })
}))