import { user, userRelations } from './user'
import { link, linkRelations } from './link'
import { bookmark, bookmarkRelations } from './bookmark'
import { tag, tagRelations } from './tag'
import { bookmarkEmbedding, bookmarkEmbeddingRelations } from './bookmark-embedding'
import { follower, followerRelations } from './follower'
import { linkMeta, linkMetaRelations } from './link-meta'
import { bookmarkMeta, bookmarkMetaRelations } from './bookmark-meta'
import { verification } from './verification'
import { account, accountRelations } from './account'
import { session, sessionRelations } from './session'

export const schema = {
  account,
  user,
  session,
  verification,
  link,
  bookmark,
  tag,
  bookmarkEmbedding,
  follower,
  linkMeta,
  bookmarkMeta,
}
export const relations = {
  accountRelations,
  userRelations,
  sessionRelations,
  linkRelations,
  bookmarkRelations,
  tagRelations,
  bookmarkEmbeddingRelations,
  followerRelations,
  linkMetaRelations,
  bookmarkMetaRelations,
}
