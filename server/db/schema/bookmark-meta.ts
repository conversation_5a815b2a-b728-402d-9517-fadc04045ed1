import { pgTable, text, integer, timestamp, unique } from 'drizzle-orm/pg-core'
import { relations } from 'drizzle-orm'
import { v7 as uuidv7 } from 'uuid'
import { z } from 'zod'
import { timestamps } from './partial'
import { user } from './user'
import { bookmark } from './bookmark'

/**
 * Table for tracking reading progress on bookmarks marked for reading later
 * Only created when a user marks a bookmark for reading later
 */
export const bookmarkMeta = pgTable('bookmark_meta', {
  id: text('id').primaryKey().$default(() => uuidv7()),
  bookmarkId: text('bookmark_id').notNull().references(() => bookmark.id, { onDelete: 'cascade' }),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  readStatus: text('read_status').notNull().default('unread'), // 'unread', 'in-progress', 'completed'
  readPercentage: integer('read_percentage').notNull().default(0), // 0-100
  lastReadAt: timestamp('last_read_at', { withTimezone: true }),
  visitCount: integer('visit_count').notNull().default(0),
  timeSpentSeconds: integer('time_spent_seconds').notNull().default(0),
  ...timestamps
}, (table) => {
  return {
    // Unique constraint to ensure one reading entry per bookmark per user
    readingUniqueIdx: unique('bookmark_meta_bookmark_user_unique_idx').on(table.bookmarkId, table.userId)
  }
})

export const bookmarkMetaSelectSchema = z.object({
  id: z.string().min(1),
  bookmarkId: z.string().min(1),
  userId: z.string().min(1),
  readStatus: z.enum(['unread', 'in-progress', 'completed']),
  readPercentage: z.number().min(0).max(100),
  lastReadAt: z.date().nullable().optional(),
  visitCount: z.number().min(0),
  timeSpentSeconds: z.number().min(0),
  createdAt: z.date(),
  updatedAt: z.date().optional()
})

export const bookmarkMetaInsertSchema = z.object({
  id: z.string().min(1).optional(),
  bookmarkId: z.string().min(1),
  userId: z.string().min(1),
  readStatus: z.enum(['unread', 'in-progress', 'completed']).optional(),
  readPercentage: z.number().min(0).max(100).optional(),
  lastReadAt: z.date().optional(),
  visitCount: z.number().min(0).optional(),
  timeSpentSeconds: z.number().min(0).optional()
})

export type BookmarkMeta = z.infer<typeof bookmarkMetaSelectSchema>
export type NewBookmarkMeta = z.infer<typeof bookmarkMetaInsertSchema>

export const bookmarkMetaRelations = relations(bookmarkMeta, ({ one }) => ({
  bookmark: one(bookmark, {
    fields: [bookmarkMeta.bookmarkId],
    references: [bookmark.id]
  }),
  user: one(user, {
    fields: [bookmarkMeta.userId],
    references: [user.id]
  })
}))