import { drizzle } from 'drizzle-orm/neon-serverless'
import { Pool } from '@neondatabase/serverless'
import { schema, relations } from './schema'

// Ensure we have a database URL
const getDatabaseUrl = () => {
  const url = process.env.DATABASE_URL
  if (!url) {
    console.error('DATABASE_URL environment variable is not set')
    throw new Error('DATABASE_URL environment variable is not set')
  }
  return url
}

// Create the database connection pool
const pool = new Pool({
  connectionString: getDatabaseUrl(),
  // Add connection options for better stability
  max: 10, // Maximum number of clients
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 5000 // How long to wait for a connection
})

// Initialize Drizzle ORM with the connection pool

export const db = drizzle(pool, { schema: { ...schema, ...relations } })
export const tables = { ...schema }
