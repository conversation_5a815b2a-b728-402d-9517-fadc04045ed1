import { pgView } from 'drizzle-orm/pg-core'
import { sql } from 'drizzle-orm'
import { bookmark } from '$/db/schema/bookmark'
import { db } from '$/db'

/**
 * View that aggregates common tags for each URL based on existing bookmarks
 * Creates computed suggestions without duplicating data
 */
export const urlTagSuggestions = pgView('url_tag_suggestions').as((qb) => {
  return qb.select({
    linkId: bookmark.linkId,
    tagName: sql`unnest(${bookmark.tagNames})`.as('tag_name'),
    tagCount: sql`count(*)`.as('tag_count')
  })
    .from(bookmark)
    .groupBy(bookmark.linkId, sql`unnest(${bookmark.tagNames})`)
    .orderBy(bookmark.linkId, sql`count(*) DESC`)
})

/**
 * Get tag suggestions for a URL
 * @param urlId The URL ID to get tag suggestions for
 * @param limit Maximum number of tags to return
 * @returns Array of tags with counts
 */
export const getTagSuggestionsForUrl = async (linkId: string, limit = 10) => {
  return db.execute(sql`
    SELECT s.tag_name, s.tag_count
    FROM url_tag_suggestions s
    WHERE s.link_id = ${linkId}
    ORDER BY s.tag_count DESC
    LIMIT ${limit}
  `)
}
