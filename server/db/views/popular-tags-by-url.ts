import { pgView } from 'drizzle-orm/pg-core'
import { sql } from 'drizzle-orm'
import { bookmark } from '$/db/schema/bookmark'

/**
 * This view aggregates the most popular tags for each URL
 * Can be used for tag suggestions when a user is bookmarking a URL
 *
 * Note: This view is now redundant with the urlTagSuggestions view
 * and the direct tag arrays on URLs. Consider using those instead.
 */
export const popularTagsByUrlView = pgView('popular_tags_by_url').as((qb) => {
  return qb
    .select({
      linkId: bookmark.linkId,
      tagName: sql`unnest(${bookmark.tagNames})`.as('tag_name'),
      count: sql`count(*)`.as('count')
    })
    .from(bookmark)
    .groupBy(bookmark.linkId, sql`unnest(${bookmark.tagNames})`)
    .orderBy(bookmark.linkId, sql`count DESC`)
})
