import { z } from 'zod'
import { pgMaterializedView, text, boolean, integer, timestamp, real } from 'drizzle-orm/pg-core'
import { sql } from 'drizzle-orm'
import { db } from '$/db'

/**
 * Materialized view for popular URLs
 *
 * The popularity score is calculated using a weighted formula that combines:
 * - Save count (35%): Total number of bookmarks for a URL (logarithmically scaled)
 * - Save velocity (25%): Rate of new bookmarks in the last 7 days
 * - Freshness (20%): How recently the URL was first bookmarked
 * - User diversity (10%): Ratio of unique users to total saves
 * - Engagement (10%): Based on visit counts and reading completion
 */
export const popularUrlsView = pgMaterializedView('popular_urls', {
  linkId: text('link_id').notNull(),
  url: text('url').notNull(),
  title: text('title'),
  description: text('description'), // @deprecated - use tldr from bookmarks instead
  isNsfw: boolean('is_nsfw').notNull(),
  saveCount: integer('save_count').notNull(),
  uniqueUsers: integer('unique_users').notNull(),
  firstSavedAt: timestamp('first_saved_at', { withTimezone: true }).notNull(),
  lastSavedAt: timestamp('last_saved_at', { withTimezone: true }).notNull(),
  recentSaveCount: integer('recent_save_count').notNull(),
  totalVisits: integer('total_visits').notNull(),
  avgReadPercentage: real('avg_read_percentage').notNull(),
  completionCount: integer('completion_count').notNull(),
  tags: text('tags').array().notNull(),
  popularityScore: real('popularity_score').notNull(),
  dailyScore: real('daily_score').notNull(),
  weeklyScore: real('weekly_score').notNull(),
  monthlyScore: real('monthly_score').notNull(),
  yearlyScore: real('yearly_score').notNull(),
  calculatedAt: timestamp('calculated_at', { withTimezone: true }).notNull()
}).as(sql`
  WITH
  -- Count total bookmarks per Link
  bookmark_counts AS (
    SELECT
      l.id AS link_id,
      l.url,
      l.title,
      l.description,
      l.is_nsfw,
      COUNT(b.id) AS save_count,
      COUNT(DISTINCT b.user_id) AS unique_users,
      MIN(b.created_at) AS first_saved_at,
      MAX(b.created_at) AS last_saved_at
    FROM link l
    JOIN bookmark b ON l.id = b.link_id
    WHERE b.is_private = false
    GROUP BY l.id, l.url, l.title, l.description, l.is_nsfw
  ),

  -- Calculate recent saves (last 7 days)
  recent_saves AS (
    SELECT
      l.id AS link_id,
      COUNT(b.id) AS recent_save_count
    FROM link l
    JOIN bookmark b ON l.id = b.link_id
    WHERE
      b.created_at > NOW() - INTERVAL '7 days'
      AND b.is_private = false
    GROUP BY l.id
  ),

  -- Calculate engagement metrics
  engagement AS (
    SELECT
      l.id AS link_id,
      SUM(COALESCE(bm.visit_count, 0)) AS total_visits,
      AVG(COALESCE(bm.read_percentage, 0)) AS avg_read_percentage,
      COUNT(CASE WHEN bm.read_status = 'completed' THEN 1 END) AS completion_count
    FROM link l
    JOIN bookmark b ON l.id = b.link_id
    LEFT JOIN bookmark_meta bm ON b.id = bm.bookmark_id
    WHERE b.is_private = false
    GROUP BY l.id
  ),

  -- Get tags for each Link (using tag_names array from bookmarks)
  link_tags AS (
    SELECT
      l.id AS link_id,
      ARRAY_AGG(DISTINCT tag) AS tags
    FROM link l
    JOIN bookmark b ON l.id = b.link_id
    CROSS JOIN UNNEST(b.tag_names) AS tag
    WHERE b.is_private = false
    GROUP BY l.id
  )

  SELECT
    bc.link_id,
    bc.url,
    bc.title,
    bc.description,
    bc.is_nsfw,
    bc.save_count,
    bc.unique_users,
    bc.first_saved_at,
    bc.last_saved_at,
    COALESCE(rs.recent_save_count, 0) AS recent_save_count,
    COALESCE(e.total_visits, 0) AS total_visits,
    COALESCE(e.avg_read_percentage, 0) AS avg_read_percentage,
    COALESCE(e.completion_count, 0) AS completion_count,
    COALESCE(lt.tags, ARRAY[]::text[]) AS tags,

    -- Calculate popularity score components
    -- 1. Save count (35%): Logarithmic scale to prevent dominance of high-count items
    (0.35 * LN(GREATEST(bc.save_count, 1) + 1)) +

    -- 2. Save velocity (25%): Recent saves normalized by days
    (0.25 * COALESCE(rs.recent_save_count, 0) / 7.0) +

    -- 3. Freshness (20%): Exponential decay based on days since first save
    (0.20 * EXP(-0.05 * EXTRACT(DAY FROM NOW() - bc.first_saved_at))) +

    -- 4. User diversity (10%): Ratio of unique users to total saves
    (0.10 * (bc.unique_users::float / GREATEST(bc.save_count, 1))) +

    -- 5. Engagement (10%): Based on visit count and completion ratio
    (0.10 * (
      (0.5 * COALESCE(e.total_visits, 0) / GREATEST(bc.save_count, 1)) +
      (0.5 * COALESCE(e.completion_count, 0) / GREATEST(bc.save_count, 1))
    )) AS popularity_score,

    -- Calculate period-specific scores
    CASE WHEN bc.last_saved_at > NOW() - INTERVAL '1 day'
      THEN (0.5 * LN(GREATEST(bc.save_count, 1) + 1)) + (0.5 * COALESCE(rs.recent_save_count, 0))
      ELSE 0
    END AS daily_score,

    CASE WHEN bc.last_saved_at > NOW() - INTERVAL '7 days'
      THEN (0.4 * LN(GREATEST(bc.save_count, 1) + 1)) + (0.6 * COALESCE(rs.recent_save_count, 0))
      ELSE 0
    END AS weekly_score,

    CASE WHEN bc.last_saved_at > NOW() - INTERVAL '30 days'
      THEN (0.6 * LN(GREATEST(bc.save_count, 1) + 1)) + (0.4 * COALESCE(rs.recent_save_count, 0))
      ELSE 0
    END AS monthly_score,

    CASE WHEN bc.last_saved_at > NOW() - INTERVAL '365 days'
      THEN (0.8 * LN(GREATEST(bc.save_count, 1) + 1)) + (0.2 * COALESCE(rs.recent_save_count, 0))
      ELSE 0
    END AS yearly_score,

    NOW() AS calculated_at
  FROM bookmark_counts bc
  LEFT JOIN recent_saves rs ON bc.link_id = rs.link_id
  LEFT JOIN engagement e ON bc.link_id = e.link_id
  LEFT JOIN link_tags lt ON bc.link_id = lt.link_id
  WHERE bc.save_count >= 1
  ORDER BY popularity_score DESC
`)

/**
 * Initialize the popular_urls materialized view
 * This creates the view if it doesn't exist
 */
export async function initializePopularUrlsView() {
  try {
    // Create the materialized view with the same SQL used to define it
    await db.execute(sql`
      CREATE MATERIALIZED VIEW IF NOT EXISTS popular_urls AS
      WITH
      -- Count total bookmarks per Link
      bookmark_counts AS (
        SELECT
          l.id AS link_id,
          l.url,
          l.title,
          l.description,
          l.is_nsfw,
          COUNT(b.id) AS save_count,
          COUNT(DISTINCT b.user_id) AS unique_users,
          MIN(b.created_at) AS first_saved_at,
          MAX(b.created_at) AS last_saved_at
        FROM link l
        JOIN bookmark b ON l.id = b.link_id
        WHERE b.is_private = false
        GROUP BY l.id, l.url, l.title, l.description, l.is_nsfw
      ),

      -- Calculate recent saves (last 7 days)
      recent_saves AS (
        SELECT
          l.id AS link_id,
          COUNT(b.id) AS recent_save_count
        FROM link l
        JOIN bookmark b ON l.id = b.link_id
        WHERE
          b.created_at > NOW() - INTERVAL '7 days'
          AND b.is_private = false
        GROUP BY l.id
      ),

      -- Calculate engagement metrics
      engagement AS (
        SELECT
          l.id AS link_id,
          SUM(COALESCE(bm.visit_count, 0)) AS total_visits,
          AVG(COALESCE(bm.read_percentage, 0)) AS avg_read_percentage,
          COUNT(CASE WHEN bm.read_status = 'completed' THEN 1 END) AS completion_count
        FROM link l
        JOIN bookmark b ON l.id = b.link_id
        LEFT JOIN bookmark_meta bm ON b.id = bm.bookmark_id
        WHERE b.is_private = false
        GROUP BY l.id
      ),

      -- Get tags for each Link (using tag_names array from bookmarks)
      link_tags AS (
        SELECT
          l.id AS link_id,
          ARRAY_AGG(DISTINCT tag) AS tags
        FROM link l
        JOIN bookmark b ON l.id = b.link_id
        CROSS JOIN UNNEST(b.tag_names) AS tag
        WHERE b.is_private = false
        GROUP BY l.id
      )

      SELECT
        bc.link_id,
        bc.url,
        bc.title,
        bc.description,
        bc.is_nsfw,
        bc.save_count,
        bc.unique_users,
        bc.first_saved_at,
        bc.last_saved_at,
        COALESCE(rs.recent_save_count, 0) AS recent_save_count,
        COALESCE(e.total_visits, 0) AS total_visits,
        COALESCE(e.avg_read_percentage, 0) AS avg_read_percentage,
        COALESCE(e.completion_count, 0) AS completion_count,
        COALESCE(lt.tags, ARRAY[]::text[]) AS tags,

        -- Calculate popularity score components
        -- 1. Save count (35%): Logarithmic scale to prevent dominance of high-count items
        (0.35 * LN(GREATEST(bc.save_count, 1) + 1)) +

        -- 2. Save velocity (25%): Recent saves normalized by days
        (0.25 * COALESCE(rs.recent_save_count, 0) / 7.0) +

        -- 3. Freshness (20%): Exponential decay based on days since first save
        (0.20 * EXP(-0.05 * EXTRACT(DAY FROM NOW() - bc.first_saved_at))) +

        -- 4. User diversity (10%): Ratio of unique users to total saves
        (0.10 * (bc.unique_users::float / GREATEST(bc.save_count, 1))) +

        -- 5. Engagement (10%): Based on visit count and completion ratio
        (0.10 * (
          (0.5 * COALESCE(e.total_visits, 0) / GREATEST(bc.save_count, 1)) +
          (0.5 * COALESCE(e.completion_count, 0) / GREATEST(bc.save_count, 1))
        )) AS popularity_score,

        -- Calculate period-specific scores
        CASE WHEN bc.last_saved_at > NOW() - INTERVAL '1 day'
          THEN (0.5 * LN(GREATEST(bc.save_count, 1) + 1)) + (0.5 * COALESCE(rs.recent_save_count, 0))
          ELSE 0
        END AS daily_score,

        CASE WHEN bc.last_saved_at > NOW() - INTERVAL '7 days'
          THEN (0.4 * LN(GREATEST(bc.save_count, 1) + 1)) + (0.6 * COALESCE(rs.recent_save_count, 0))
          ELSE 0
        END AS weekly_score,

        CASE WHEN bc.last_saved_at > NOW() - INTERVAL '30 days'
          THEN (0.6 * LN(GREATEST(bc.save_count, 1) + 1)) + (0.4 * COALESCE(rs.recent_save_count, 0))
          ELSE 0
        END AS monthly_score,

        CASE WHEN bc.last_saved_at > NOW() - INTERVAL '365 days'
          THEN (0.8 * LN(GREATEST(bc.save_count, 1) + 1)) + (0.2 * COALESCE(rs.recent_save_count, 0))
          ELSE 0
        END AS yearly_score,

        NOW() AS calculated_at
      FROM bookmark_counts bc
      LEFT JOIN recent_saves rs ON bc.link_id = rs.link_id
      LEFT JOIN engagement e ON bc.link_id = e.link_id
      LEFT JOIN link_tags lt ON bc.link_id = lt.link_id
      WHERE bc.save_count >= 1
      ORDER BY popularity_score DESC
    `)

    // Create indexes for efficient querying
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS popular_urls_popularity_score_idx ON popular_urls (popularity_score DESC);
      CREATE INDEX IF NOT EXISTS popular_urls_daily_score_idx ON popular_urls (daily_score DESC);
      CREATE INDEX IF NOT EXISTS popular_urls_weekly_score_idx ON popular_urls (weekly_score DESC);
      CREATE INDEX IF NOT EXISTS popular_urls_monthly_score_idx ON popular_urls (monthly_score DESC);
      CREATE INDEX IF NOT EXISTS popular_urls_yearly_score_idx ON popular_urls (yearly_score DESC);
      CREATE INDEX IF NOT EXISTS popular_urls_link_id_idx ON popular_urls (link_id);
    `)

    console.log('Popular URLs materialized view initialized')
    return true
  }
  catch (error) {
    console.error('Error initializing popular URLs view:', error)
    throw error
  }
}

/**
 * Refresh the popular_urls materialized view
 */
export async function refreshPopularUrlsView() {
  try {
    await db.refreshMaterializedView(popularUrlsView)
    console.log('Popular URLs materialized view refreshed at', new Date().toISOString())
    return true
  }
  catch (error) {
    console.error('Error refreshing popular URLs view:', error)
    return false
  }
}

/**
 * Zod schema for validating popular URLs data
 */
export const popularUrlsSelectSchema = z.object({
  linkId: z.string().min(1),
  url: z.string().url(),
  title: z.string().nullable().optional(),
  /** @deprecated - use tldr from bookmarks instead */
  description: z.string().nullable().optional(),
  isNsfw: z.boolean(),
  saveCount: z.number().int().min(0),
  uniqueUsers: z.number().int().min(0),
  firstSavedAt: z.date(),
  lastSavedAt: z.date(),
  recentSaveCount: z.number().int().min(0),
  totalVisits: z.number().int().min(0),
  avgReadPercentage: z.number().min(0).max(100),
  completionCount: z.number().int().min(0),
  tags: z.array(z.string()),
  popularityScore: z.number(),
  dailyScore: z.number(),
  weeklyScore: z.number(),
  monthlyScore: z.number(),
  yearlyScore: z.number(),
  calculatedAt: z.date()
})

export type PopularUrl = z.infer<typeof popularUrlsSelectSchema>
