# PRD: SearchPlus Component

## 1. Introduction / Overview
The **SearchPlus** component enhances standard search inputs by allowing users to enter free text, specify user scopes (e.g., `@all`, `@user`), and tag filters (e.g., `#news`) in a single, interactive field. Text is enriched in-place with visual cues (e.g., color highlighting) as users type. This creates a more engaging, context-rich search experience while maintaining standard typing and deletion behavior.

## 2. Goals
- Provide a unified input for free text, scope, and tag-based filters.
- Enrich text in real-time with distinct styling for scopes (`@…`) and tags (`#…`).
- Ensure typing and deletion remain smooth and responsive (debounced updates).
- Enable quick filtering by user scope and tag without separate dropdowns.
- Emit parsed search parameters for integration (initially via `console.log`).

## 3. User Stories
1. As a **casual user**, I want to type free text so I can search by keywords.  
2. As a **power user**, I want to prepend `@user` or `@all` so I can quickly filter the search scope.  
3. As any user, I want to add `#tag` filters so I can narrow results by category.  
4. As a user, I want visual highlighting of `@` and `#` tokens in the input so I can confirm my filters before searching.

## 4. Functional Requirements
1. Parse and render **scope tokens** prefixed with `@` (e.g., `@all`, `@user`), highlighted distinctly.  
2. Parse and render **tag tokens** prefixed with `#` (e.g., `#news`), highlighted distinctly.  
3. Treat remaining text as **free-text terms**.  
4. Support standard typing, backspacing, and cursor movements without disrupting enriched styling.  
5. Use a **200ms debounce** on input updates to optimize performance.  
6. On **Enter** or pause, emit an event (or `console.log`) with the parsed parameters:
   - `scope`: single `@` token or default `null`
   - `tags`: list of `#` tokens
   - `text`: list of remaining words
7. Allow removal of individual tokens by clicking or pressing **Backspace** when caret is adjacent.
8. Integrate with **Shadcn-Vue** and **Reka UI** components for styling and interaction.

## 5. Non-Goals (Out of Scope)
- Rendering search results or result lists.  
- Server-side API integration beyond logging parameters.  
- Complex autocomplete suggestions (beyond basic parsing).  

## 6. Design Considerations
- Inline styling using **Tailwind CSS** classes:  
  - `@…` tokens in blue (`text-blue-600`).  
  - `#…` tokens in green (`text-green-600`).  
  - Free text in default color (`text-gray-800`).  
- Use **Reka UI** input container and **Shadcn-Vue** icons/button.  
- Maintain accessibility (`aria-label`, keyboard navigation).

## 7. Technical Considerations
- Implement as a Vue 3 `<script setup>` component using Composition API.  
- Use `debounceref` (from Reka or custom) for throttled updates.  
- Generate unique IDs with `uuidV7`.  
- No third-party parsing libraries; use simple regex-based tokenization.  

## 8. Success Metrics
- **Responsiveness:** Input visual updates within 200ms of typing.  
- **Accuracy:** Parsed output matches user input structure 100% of the time.  
- **Stability:** No console errors or styling glitches during normal typing and deletion.

## 9. Open Questions
- Should multiple `@` scopes be supported or limited to one?  
- Do we need to fetch valid user names/tags dynamically or rely on free-text?  
- What’s the preferred default scope if none is specified?  
